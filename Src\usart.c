/**
 ******************************************************************************
 * @file    usart.c
 * @brief   This file provides code for the configuration
 *          of the USART instances.
 ******************************************************************************
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.</center></h2>
 *
 * This software component is licensed by ST under Ultimate Liberty license
 * SLA0044, the "License"; You may not use this file except in compliance with
 * the License. You may obtain a copy of the License at:
 *                             www.st.com/SLA0044
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "usart.h"
#include "string.h"
#include "main.h"
#include "usbd_cdc_if.h"
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

UART_HandleTypeDef huart3;
DMA_HandleTypeDef hdma_usart3_rx;
uint8_t UserRxBufferUart[256]; // data received from host
#define APP_RX_DATA_SIZE 256
#define APP_TX_DATA_SIZE 256

/* USART3 init function */

void MX_USART3_UART_Init(void)
{

  huart3.Instance = USART3;
  huart3.Init.BaudRate = 115200;
  huart3.Init.WordLength = UART_WORDLENGTH_8B;
  huart3.Init.StopBits = UART_STOPBITS_1;
  huart3.Init.Parity = UART_PARITY_NONE;
  huart3.Init.Mode = UART_MODE_TX_RX;
  huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart3.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart3) != HAL_OK)
  {
    Error_Handler();
  }
  HAL_UART_Receive_DMA(&huart3, UserRxBufferUart, APP_RX_DATA_SIZE);
  __HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE);
}

void HAL_UART_MspInit(UART_HandleTypeDef *uartHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if (uartHandle->Instance == USART3)
  {
    /* USER CODE BEGIN USART3_MspInit 0 */

    /* USER CODE END USART3_MspInit 0 */
    /* USART3 clock enable */
    __HAL_RCC_USART3_CLK_ENABLE();

    __HAL_RCC_GPIOC_CLK_ENABLE();
    /**USART3 GPIO Configuration
    PC11     ------> USART3_RX
    PC10     ------> USART3_TX
    */
    GPIO_InitStruct.Pin = RX_Pin | TX_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    /* USART3 DMA Init */
    /* USART3_RX Init */
    hdma_usart3_rx.Instance = DMA1_Stream1;
    hdma_usart3_rx.Init.Channel = DMA_CHANNEL_4;
    hdma_usart3_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart3_rx.Init.PeriphInc = DMA_PINC_DISABLE;
    hdma_usart3_rx.Init.MemInc = DMA_MINC_ENABLE;
    hdma_usart3_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
    hdma_usart3_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
    hdma_usart3_rx.Init.Mode = DMA_CIRCULAR;
    hdma_usart3_rx.Init.Priority = DMA_PRIORITY_LOW;
    hdma_usart3_rx.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
    if (HAL_DMA_Init(&hdma_usart3_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle, hdmarx, hdma_usart3_rx);

    /* USART3 interrupt Init */
    HAL_NVIC_SetPriority(USART3_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(USART3_IRQn);
    /* USER CODE BEGIN USART3_MspInit 1 */

    /* USER CODE END USART3_MspInit 1 */
  }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef *uartHandle)
{

  if (uartHandle->Instance == USART3)
  {
    /* USER CODE BEGIN USART3_MspDeInit 0 */

    /* USER CODE END USART3_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART3_CLK_DISABLE();

    /**USART3 GPIO Configuration
    PC11     ------> USART3_RX
    PC10     ------> USART3_TX
    */
    HAL_GPIO_DeInit(GPIOC, RX_Pin | TX_Pin);

    /* USART3 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx); 

    /* USART3 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART3_IRQn); 
    /* USER CODE BEGIN USART3_MspDeInit 1 */

    /* USER CODE END USART3_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
void UsartReceive_IDLE(UART_HandleTypeDef *huart)
{
  uint32_t temp;
  uint16_t RX_Size;

  if ((__HAL_UART_GET_FLAG(huart, UART_FLAG_IDLE) != RESET))
  {
    __HAL_UART_CLEAR_IDLEFLAG(huart); // 清楚空闲中断标志，否则一直进入中断
    HAL_UART_DMAStop(huart);          // 停止本次DMA传输
    // temp = huart3.hdmarx->Instance->NDTR; //计算接收到的数据长度
    temp = __HAL_DMA_GET_COUNTER(&hdma_usart3_rx); // 计算接收到的数据长度
    RX_Size = APP_RX_DATA_SIZE - temp; // 接收到的数据长度

    HAL_UART_Receive_DMA(huart, UserRxBufferUart, APP_RX_DATA_SIZE); // 继续下一次接收
    memcpy(UserRxBufferFS, UserRxBufferUart, RX_Size);  // 将接收到的数据拷贝到UserRxBufferFS中

    UserRxBufferFS[RX_Size] = 0;    // 添加结束符
    if (strstr((char *)UserRxBufferFS, "*IDN?") != NULL)  
    {
      RxFlag = 1;
      interface = 1;
    }
    else if (strstr((char *)UserRxBufferFS, "*RST") != NULL)
    {
      RxFlag = 1;
      interface = 1;
    }
    else if (strstr((char *)UserRxBufferFS, ":") != NULL)
    {
      RxFlag = 2;
      interface = 1;
    }
    else
      RxFlag = 0;

    memset(UserRxBufferUart, 0, APP_RX_DATA_SIZE);
  }
}

void UsartSendCmd(uint8_t *content)
{
  uint16_t size = strlen((char *)content);
  HAL_UART_Transmit(&huart3, content, size, 0xFFFF);
}
/* USER CODE END 1 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
