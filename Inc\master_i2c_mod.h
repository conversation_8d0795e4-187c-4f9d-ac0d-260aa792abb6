/**
  ******************************************************************************
  * @file           : master_i2c.h
  * @brief          : Header for master_i2c.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MASTER_I2C_MOD_H__
#define __MASTER_I2C_MOD_H__

#ifdef __cplusplus
 extern "C" {	 
#endif
/* Includes ------------------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stm32f2xx_hal.h"
#include "main.h"
/* USER CODE END Includes */

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */


/* USER CODE BEGIN Prototypes */

extern uint8_t I2C_WriteBytes_mod(uint8_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length);
extern uint8_t I2C_ReadBytes_mod(uint8_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length);

/* USER CODE END Prototypes */
	 
#ifdef __cplusplus
}
#endif

#endif /* __MASTER_I2C_MOD_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
