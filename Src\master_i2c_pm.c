
#include "main.h"
#include "master_i2c_pm.h"
//=====================================
#define SDA(PinState) HAL_GPIO_WritePin(SDA_GPIO_Port,SDA_Pin,PinState?GPIO_PIN_SET:GPIO_PIN_RESET) 
#define GetSDA() HAL_GPIO_ReadPin(SDA_GPIO_Port,SDA_Pin)
#define SCL(PinState) HAL_GPIO_WritePin(SCL_GPIO_Port,SCL_Pin,PinState?GPIO_PIN_SET:GPIO_PIN_RESET)

static unsigned int CLK_WIDE = 50;		// = 0 写字节总周期是609ns;38=400K;191=100K
static unsigned char CLK_WIDE_Low = 50; 

static void Delay_ns(uint32_t nTime)
{
	while(nTime--);	//120MHz,time = 8.33ns,while = 0:58ns; = n:58+n*25; n = 1= 3cycle = 25ns
}
//=====================================
//start
static void start(void)
{
	SCL(1); //SCL = 1
	Delay_ns(CLK_WIDE);
	
	SDA(1); //SDA = 1;
	Delay_ns(CLK_WIDE);
	
	SDA(0); //SDA = 0;
	Delay_ns(CLK_WIDE);
	
	SCL(0); //SCL = 0
	Delay_ns(CLK_WIDE);
}


//=====================================
//stop
static void stop(void)
{
	SCL(0); //SCL = 0
	Delay_ns(CLK_WIDE);
	
	SDA(0); //SDA = 0;
	Delay_ns(CLK_WIDE);
	
	SCL(1); //SCL = 1
	Delay_ns(CLK_WIDE);
	
	SDA(1); //SDA = 1;
	Delay_ns(CLK_WIDE);
}

//==========================================
//MASTER NO ACK
static void master_no_ack(void)
{

	SDA(1); //SDA = 1;
	Delay_ns(CLK_WIDE);
	
	SCL(1); //SCL = 1
	Delay_ns(CLK_WIDE);
	
	SCL(0); //SCL = 0
	Delay_ns(CLK_WIDE);
}


//=====================================
//MASTER  ACK
static void master_ack(void)
{
	SDA(0); //SDA = 0;
	Delay_ns(CLK_WIDE);
	
	SCL(1); //SCL = 1
	Delay_ns(CLK_WIDE);
	
	SCL(0); //SCL = 0
	Delay_ns(CLK_WIDE);

}

/*--------------------------------------
* SLAVE ACK
* Return:   0: Success	1: Failure.
---------------------------------------*/
static uint8_t slave_ack(void)
{
	unsigned char error_time = 255;
	
	SDA(1); //SDA = 1;
	Delay_ns(CLK_WIDE);
	
	SCL(1); //SCL = 1
	while(GetSDA())	  //wait SDA = 0
	{
		error_time--;
		if(error_time == 0)	//timeout
		{
			SCL(0); //SCL = 0
			Delay_ns(CLK_WIDE);
			SCL(1); //SCL = 1
			Delay_ns(CLK_WIDE);
			SCL(0); //SCL = 0
			stop();
			return(1);
		}
	}
	Delay_ns(CLK_WIDE);
	SCL(0); //SCL = 0
	Delay_ns(CLK_WIDE);
	return(0);
}


//==========================================
//write 8 bit
static void write_8bit(uint8_t byte)
{
	uint8_t i=8;
	while(i--)
	{
		SDA(byte & 0x80);
		byte <<=1;
		SCL(1); //SCL = 1
		Delay_ns(CLK_WIDE);	
		SCL(0); //SCL = 0
		Delay_ns(CLK_WIDE_Low);	
	}
}

//==========================================
//read 8 bit
static uint8_t read_8bit()
{
	uint8_t receive,i;
	
	SDA(1); //SDA = 1;
	Delay_ns(CLK_WIDE);	
	
	receive =0;
	i =8;
	while(i--)
	{
		receive <<=1;
		SCL(1); //SCL = 1
		Delay_ns(CLK_WIDE);			 //clk width
		if(GetSDA())receive++;
		SCL(0); //SCL = 0
		Delay_ns(CLK_WIDE);			 //clk width
	}
	return(receive);
}

/*----------------------------------------------
* 读出 count 个字节数据，存入 buffer 开始的地址
* Return:   1: Success			0: Failure.
-----------------------------------------------*/
uint8_t I2C_ReadBytes_PM(uint8_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length)
{
	start();
	
	write_8bit(Addr);			//set device Address
	if(slave_ack()) return(0);
	write_8bit(Reg);			//set Byte address
	if(slave_ack()) return(0);
	
	start();
	write_8bit(Addr+1);   	//command read	= device_address + 1
	if(slave_ack()) return(0);

	while(Length--)
	{
		*pBuffer = read_8bit();
		pBuffer++;
		if(Length !=0 )
		{
			master_ack();
		}
	}

	master_no_ack();	  				//n = 0 end send NO ACK
	stop();
	return(1);
}

/*---------------------------------------------
* 从 buffer 开始的地址，写入 count 个字节数据
* Return:   1: Success	0: Failure.
-----------------------------------------------*/
uint8_t I2C_WriteBytes_PM(uint8_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length)
{
	start();
	write_8bit(Addr);		//set device Address
	if(slave_ack()) return(0);
	write_8bit(Reg);			//set Byte address
	slave_ack();
	while(Length--)
	{
		write_8bit(*pBuffer);
		pBuffer++;
		if(slave_ack()) return(0);
	}
	stop();
	return(1);
}


uint8_t I2C_WriteWord_PM(uint8_t Addr, uint8_t Reg, uint16_t Value)
{
	uint8_t val_msb = Value>>8;
	uint8_t val_lsb = Value & 0xff;
	
	start();
	write_8bit(Addr);			//set device Address
	if(slave_ack()) return(0);
	write_8bit(Reg);			//set msb address
	if(slave_ack()) return(0);
	
	write_8bit(val_msb);
	if(slave_ack()) return(0);
	write_8bit(val_lsb);
	if(slave_ack()) return(0);	
	stop();
	return(1);
}

uint8_t I2C_ReadWord_PM(uint8_t Addr, uint8_t Reg, uint16_t *value)
{
	uint8_t val_msb = 0;
	uint8_t val_lsb = 0;
	
	start();
	
	write_8bit(Addr);			//set device Address
	if(slave_ack()) return(0);
	write_8bit(Reg);			//set msb address
	if(slave_ack()) return(0);

	start();
	write_8bit(Addr+1);   	//command read	= device_address + 1
	if(slave_ack()) return(0);
	val_msb = read_8bit();
	master_ack();
	val_lsb = read_8bit();
	master_no_ack();	  				//n = 0 end send NO ACK
	*value = (val_msb<<8)|val_lsb;
	stop();
	return(1);
}


