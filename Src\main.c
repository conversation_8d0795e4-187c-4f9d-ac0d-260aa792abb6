
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * This notice applies to any and all portions of this file
 * that are not between comment pairs USER CODE BEGIN and
 * USER CODE END. Other portions of this file, whether
 * inserted by the user or by software development tools
 * are owned by their respective copyright owners.
 *
 * Copyright (c) 2019 STMicroelectronics International N.V.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted, provided that the following conditions are met:
 *
 * 1. Redistribution of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. Neither the name of STMicroelectronics nor the names of other
 *    contributors to this software may be used to endorse or promote products
 *    derived from this software without specific written permission.
 * 4. This software, including modifications and/or derivative works of this
 *    software, must execute solely and exclusively on microcontroller or
 *    microprocessor devices manufactured by or for STMicroelectronics.
 * 5. Redistribution and use of this software other than as permitted under
 *    this license is void and will automatically terminate your rights under
 *    this license.
 *
 * THIS SOFTWARE IS PROVIDED BY STMICROELECTRONICS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS, IMPLIED OR STATUTORY WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE AND NON-INFRINGEMENT OF THIRD PARTY INTELLECTUAL PROPERTY
 * RIGHTS ARE DISCLAIMED TO THE FULLEST EXTENT PERMITTED BY LAW. IN NO EVENT
 * SHALL STMICROELECTRONICS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 ******************************************************************************
 */
/* Include ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f2xx_hal.h"
#include "usb_device.h"
#include "gpio.h"
#include "mdio.h"
#include "dac.h"
#include "adc.h"
#include "dma.h"
#include "rng.h"
#include "capi_def.h"
#include "bcm87800.h"
#include "master_i2c.h"
#include "master_i2c_mod.h"
#include "master_i2c_pm.h"
#include "usbd_cdc.h"
#include "usbd_cdc_if.h"
#include "clock-Freq-Registers.h"
#include "usart.h"
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private variables ---------------------------------------------------------*/
#define FLASH_USER_START_ADDR ADDR_FLASH_SECTOR_11
#define Channel_Num 2
#define Sample_Num 16
uint32_t ADC_ConvertedValue[Sample_Num][Channel_Num];

uint32_t accum_time = 0;
uint8_t TimerFlag = 0;
uint32_t bert_TimeCountStart[2][8];

uint8_t C4_0_255_Table[256];
C4_Table_TypeDef *C4 = (C4_Table_TypeDef *)C4_0_255_Table;
bert_TypeDef BER[2][8];
uint32_t st = 0, et = 0;
uint8_t interface = 0; //
uint16_t DAC_ValueCompare;
uint16_t DAC_TxI_Offset;
uint8_t LPMode_Compare;
uint8_t RST_Compare;
uint8_t PM_Compare;
extern RNG_HandleTypeDef hrng;
/* USER CODE BEGIN PV */
/* Private variables ---------------------------------------------------------*/

uint32_t MCUID[3];

_USB_State hid_State;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Process_ReceiveData(void);
void Update_ErrorCount(void);
void Flash_Read(uint32_t addr, uint8_t *buffer, uint32_t length);
HAL_StatusTypeDef Flash_Write(uint32_t addr, uint8_t *buffer, uint32_t length);
void SPI_DAC_Output(void);
void Monitor(void);
void Monitor_Temp(void);

/* USER CODE BEGIN PFP */
/* Private function prototypes -----------------------------------------------*/
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/* USER CODE BEGIN PFP */
/* Private function prototypes -----------------------------------------------*/

/* USER CODE END PFP */

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 *
 * @retval None
 */
/**
 * @brief  主函数
 * @param  无
 * @retval 无
 */
//TODO:系统初始化
int main(void)
{
	/* USER CODE BEGIN 1 */

	/* 用户代码区开始1 */
	uint32_t LoopCount = 0; // 循环计数器，用于主循环计数
	uint16_t i = 0, n = 0;	// 临时变量，用于循环计数和临时存储
	char *VendorName = "OVLINK"; // 厂商名称字符串
	char EVB_SN[16]; // 板卡序列号数组
	uint16_t SampleCount = 0; // 采样计数器，用于ADC采样计数
	bool dsp_init; // DSP初始化状态标志
	/* USER CODE END 1 */

	/* MCU Configuration----------------------------------------------------------*/

	/* MCU配置部分，包含外设复位、Flash接口初始化和Systick初始化 */
	/* Reset of all peripherals, Initializes the Flash interface and the Systick. */
	/* 复位所有外设，初始化Flash接口和Systick */
	HAL_Init();
	
	/* USER CODE BEGIN Init */

	/* 用户初始化代码开始 */
	//读取MCU ID，用于唯一标识设备
	MCUID[0] = *(uint32_t *)(STM32F2_ID);
	MCUID[1] = *(uint32_t *)(STM32F2_ID + 4);
	MCUID[2] = *(uint32_t *)(STM32F2_ID + 8);
	uint8_t arrMCUID[4];
	memcpy((uint8_t *)arrMCUID, &MCUID[2], 4);
	sprintf(EVB_SN, "BX%.8X%.4X", MCUID[0], MCUID[1] & 0xffff);

	/* USER CODE END Init */

	/* Configure the system clock */
	//系统时钟配置
	SystemClock_Config();
	MX_GPIO_Init();
	MX_DMA_Init();
	MX_ADC1_Init();
	MX_DAC_Init();
	MX_SPI1_Init();

	/* USER CODE BEGIN SysInit */
	Flash_Read(FLASH_USER_START_ADDR, C4_0_255_Table, 256);
	for (i = 0; i < 128; i++)
	{
		if (C4_0_255_Table[i] == 0xFF)
			n++;
	}
	if (n > 127) // 默认参数
	{
		memset(&C4_0_255_Table[0], 0x00, 32);

		memcpy(C4->VendorName, VendorName, strlen(VendorName)); // 16bytes
		memcpy(C4->EVB_SN, EVB_SN, strlen(EVB_SN));				// 16bytes

		C4->Password[0] = 0x47;
		C4->Password[1] = 0x45;
		C4->Password[2] = 0x52;
		C4->Password[3] = 0x44;

		C4->Interface = 0;
		C4->SignalMode = CAPI_MODULATION_PAM4;
		C4->DataRate = 0x0e;
		C4->ClkDivide = 0x02;
		C4->RxAutoInvert = 0;
		C4->rng_value = HAL_RNG_GetRandomNumber(&hrng) & 0x00fffff;

		C4->VCC_DAC = 3300;
		C4->VCC_DAC_slope = 256;
		C4->VCC_DAC_offset = 0;
		C4->I_slope = 256;
		C4->I_offset = 0;
		C4->Vcc_slope = 256;
		C4->Vcc_offset = 0;
		C4->Temp_offset = 0;
		C4->IALARM = 8000;

		C4->hostline_Taps[0] = 0;
		C4->hostline_Taps[1] = -30;
		C4->hostline_Taps[2] = 130;
		C4->hostline_Taps[3] = 0;
		C4->hostline_Taps[4] = 0;
		C4->hostline_Taps[5] = 0;
		C4->hostline_Taps[6] = 0;

		Flash_Write(FLASH_USER_START_ADDR, C4_0_255_Table, 256);
		HAL_Delay(100);
	}

	// C4->DataRate
	for (i = 0; i < 8; i++)
	{

		//	C4->HostTxPattern[i] = 8;
		//	C4->HostRxPattern[i] = 8;

		//	C4->TxSquelch[i]    = 0;
		//	C4->TxInvert[i] = 0;
		//	C4->RxInvert[i] = 0;

		C4->LaneTxPattern[i] = 8;
		C4->LaneRxPattern[i] = 8;

		BER[0][i].FECEn_Flag = 0;
		BER[0][i].FECEn_HistoryFlag = 0;
		BER[0][i].EDEn_Flag = 0;
		BER[0][i].EDEn_HistoryFlag = 0;
		BER[0][i].lane_num_taps = 0;
		BER[1][i].lane_num_taps = 0;
	}

	C4->gain_boost = 0;
	C4->peaking_filter = 0;
	C4->host_rx_bw_value = 0;
	C4->lane_rx_bw_value = 0;
	C4->vga = 0;
	C4->Interface = 0;
	C4->RxAutoInvert = 0;

	//	C4->ClkDivide = 0x02;
	//	C4->DataRate = 0x0e;
	//	C4->SignalMode = CAPI_MODULATION_PAM4;

	//	C4->host_Taps[0] = 0;
	//	C4->host_Taps[1] = -30;
	//	C4->host_Taps[2] = 130;
	//	C4->host_Taps[3] = 0;
	//	C4->host_Taps[4] = 0;
	//	C4->host_Taps[5] = 0;
	//	C4->host_Taps[6] = 0;

	/* USER CODE END SysInit */

	/* Initialize all configured peripherals */
	hid_State = None;
	MX_USB_DEVICE_Init();
	MX_USART3_UART_Init();
	SPI_DAC_Output();
	HAL_ADC_Start_DMA(&hadc1, (uint32_t *)&ADC_ConvertedValue, Sample_Num * Channel_Num);
	// HAL_ADC_Start_DMA(&hadc1,(uint32_t*)&ADC_ConvertedValue,Sample_Num*Channel_Num);

	uint16_t SHUNT_CAL; //= 0.00512/Current_LSB = 200uA * RSHUNT = 0.01R
	SHUNT_CAL = 2560;
	I2C_WriteWord_PM(0x80, 0x00, 0x4527);
	I2C_WriteWord_PM(0x80, 0x05, SHUNT_CAL);

	/* USER CODE BEGIN 2 */
	if (MCUID[0] == ((~C4->MCU_ID[0]) - 0x112233) && MCUID[1] == ((~C4->MCU_ID[1]) - C4->rng_value) && MCUID[2] == ((~C4->MCU_ID[2]) - 0x223311))
	{
		C4->Licence = 0x80;
	}
	else
	{
		C4->Licence = 0;
	}
	C4->Licence = 0x80;
	if (C4->Licence == 0x80)
	{
		bcm87800_download_firmware();
		dsp_init = bcm87800_init_prbs(1);
	}

	/* USER CODE END 2 */

	/* Infinite loop */
	/* USER CODE BEGIN WHILE */
	//TODO:主循环
	while (1)
	{

		/* USER CODE END WHILE */
		LoopCount++;
		SampleCount++;
		if (LoopCount > 0x3ff && dsp_init)
		{
			LoopCount = 0;
			HAL_GPIO_TogglePin(LINK_GPIO_Port, LINK_Pin);
		}
		else if (LoopCount > 0x3ff && !dsp_init)
		{
			LoopCount = 0;
			HAL_GPIO_WritePin(LINK_GPIO_Port, LINK_Pin, GPIO_PIN_RESET);
		}
		Process_ReceiveData();
		SPI_DAC_Output();
		Monitor();
		Monitor_Temp();
		/* USER CODE BEGIN 3 */
	}
	/* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
// 系统时钟配置
void SystemClock_Config(void)
{

	RCC_OscInitTypeDef RCC_OscInitStruct;
	RCC_ClkInitTypeDef RCC_ClkInitStruct;

	/**Initializes the CPU, AHB and APB busses clocks
	 */
	RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
	RCC_OscInitStruct.HSEState = RCC_HSE_ON;
	RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
	RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
	RCC_OscInitStruct.PLL.PLLM = 20;
	RCC_OscInitStruct.PLL.PLLN = 192;
	RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
	RCC_OscInitStruct.PLL.PLLQ = 5;
	if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
	{
		Error_Handler();
	}

	/**Initializes the CPU, AHB and APB busses clocks
	 */
	RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
	RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
	RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
	RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
	RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

	if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
	{
		Error_Handler();
	}

	/**Configure the Systick interrupt time
	 */
	HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq() / 1000);

	/**Configure the Systick
	 */
	HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);

	/* SysTick_IRQn interrupt configuration */
	HAL_NVIC_SetPriority(SysTick_IRQn, 0, 0);
}

/* USER CODE BEGIN 4 */
// 读取ADC平均值
uint16_t ReadADCAverageValue(uint16_t Channel)
{
	uint8_t i, j;
	uint32_t ADC_Sum = 0, adc_swap;
	uint16_t adc_value[16];

	for (i = 0; i < Sample_Num; i++)
	{
		ADC_Sum += ADC_ConvertedValue[i][Channel];
	}
	ADC_Sum = ADC_Sum >> 4;
	if (ADC_Sum > 4095)
	{
		ADC_Sum = 4095;
	}
	return (ADC_Sum);
}
//监控温度
/**
 * @brief 监测温度函数
 * 该函数用于读取ADC值并转换为实际温度值，进行温度补偿后存储到DDM_Temp中
 */
void Monitor_Temp(void)
{
	unsigned int ADvalue = 0;
	ADvalue = ReadADCAverageValue(1);
	float f_temp = (ADvalue * 3388 / 4096.0 - 760) / 2.5 + 25;

	f_temp += C4->Temp_offset;
	C4->DDM_Temp = f_temp * 256;
}

//监控电压和电流
/**
 * @brief 监控函数，用于读取和计算电流、电压等参数
 * 该函数通过I2C读取数据，并进行一系列计算和校准操作
 */
void Monitor(void)
{
	unsigned int ADvalue = 0;    // AD转换值
	signed int offset = 0;       // 偏移量
	unsigned int slope = 0;      // 斜率
	float f_temp = 0.0;
	unsigned int TxIOffsetTemp = 0;  // TXI偏移温度值
	float f_Offset = 0.0;        // 偏移浮点数值
	uint16_t buffer[4] = {0};    // 缓冲区数组，用于存储I2C读取的数据



	// 通过I2C读取4个16位数据到缓冲区
	I2C_ReadWord_PM(0x80, 1, &buffer[0]);  // 读取第二个数据  // 读取第一个数据
	I2C_ReadWord_PM(0x80, 2, &buffer[1]);
	I2C_ReadWord_PM(0x80, 3, &buffer[2]);
	I2C_ReadWord_PM(0x80, 4, &buffer[3]);

	C4->DDM_POWER = (float)buffer[2] * 200e-3 * 32;

	ADvalue = buffer[3];
	f_temp = (float)ADvalue * 200e-6;
	f_temp = f_temp * 1000; // mA
	f_Offset = f_temp;

	slope = C4->I_slope;
	offset = C4->I_offset;
	f_temp = f_temp * slope;
	f_temp = f_temp / 256;
	f_temp += offset;
	C4->DDM_I = (uint16_t)f_temp;

	TxIOffsetTemp = f_Offset * 0.035; // 校准没插模块偏差 0.175是电感0.07和0.025R电阻之和，1.221是AD5318的精度
	DAC_TxI_Offset = TxIOffsetTemp;

	ADvalue = ReadADCAverageValue(0); // P2.2 VCCTD
	f_temp = (float)ADvalue;
	f_temp = (f_temp * VREF / ADC_MAX); // V
	f_temp *= 2000;						// MV

	slope = C4->Vcc_slope;
	offset = C4->Vcc_offset;
	f_temp = f_temp * slope;
	f_temp = f_temp / 256;
	f_temp += offset;
	C4->DDM_Vcc = (uint16_t)f_temp;
}
//输出DAC
/**
 * @brief SPI DAC输出函数
 * 该函数用于通过SPI接口输出DAC值，包含校准和斜率补偿功能
 */
void SPI_DAC_Output(void)
{
	signed int offset = 0;        // 偏移量，用于DAC校准
	float f_temp;                 // 临时浮点变量，用于中间计算
	unsigned int slope = 0, diffVal = 0;  // slope:斜率值，diffVal:差值
	uint16_t DAC_UN_Value, OutDAC = 420;  // DAC_UN_Value:最终DAC值，OutDAC:初始输出值420

	// 校准部分
	DAC_UN_Value = C4->VCC_DAC;          // 获取原始DAC值
	slope = C4->VCC_DAC_slope;            // 获取斜率校准值
	offset = C4->VCC_DAC_offset;          // 获取偏移校准值

	f_temp = DAC_UN_Value;                // 将原始值转为浮点数
	f_temp += offset;
	DAC_UN_Value = f_temp;

	DAC_UN_Value += DAC_TxI_Offset;       // 应用TxI偏移补偿
	if (DAC_UN_Value > ADC_MAX)           // 限制DAC值不超过最大值
		DAC_UN_Value = ADC_MAX;



	// 比较和输出部分
	diffVal = abs(DAC_UN_Value - DAC_ValueCompare);
	if (DAC_UN_Value != DAC_ValueCompare && diffVal > 1)  // 计算当前值与目标值的差值
	{
		DAC_ValueCompare = DAC_UN_Value;

		OutDAC = (3.930222 - DAC_UN_Value / 1000.0) / 0.000949;
		WriteOutputDAC(1, OutDAC);
	}
}
//设置输出参考时钟
/**
 * @brief 设置输出参考时钟频率
 * @param Freq 频率选择参数，用于选择不同的频率配置
 * @note 该函数通过I2C通信写入时钟配置寄存器，每次写入后延时2ms
 */
void SetOutputRefClock(uint8_t Freq)
{
	uint16_t n = 0;  // 循环计数器，用于遍历频率配置表
	clock_register_t reg;  // 时钟寄存器结构体变量，用于存储寄存器地址和值

	// 遍历频率配置表中的20个寄存器配置项
	for (n = 0; n < 20; n++)
	{
		// 从频率配置表中获取当前寄存器配置
		reg = Clock_FreqConfig[Freq][n];
		// 通过I2C写入寄存器地址和对应的值
		I2C_WriteByte(0xb2, reg.address, reg.value);
		HAL_Delay(2);  // 每次写入后延时2ms，确保写入稳定
	}
	// 该行被注释掉，可能不需要额外的50ms延时
	// HAL_Delay(50);
}
/**
 * @brief 获取时间差函数
 * 
 * @param u32_TimeStart 
 * @return uint32_t 
 */
uint32_t Get_Time_Diff(uint32_t u32_TimeStart)
{
	uint32_t u32_TimeAct;

	TimerFlag = 0;
	u32_TimeAct = accum_time; // Get actual time
	if (TimerFlag)
	{
		u32_TimeAct = accum_time;
	}
	if (u32_TimeStart <= u32_TimeAct)
	{
		return (u32_TimeAct - u32_TimeStart); // account time /2
	}
	else
	{
		return ((0xFFFFFFFF - u32_TimeStart) + u32_TimeAct); // overflow
	}
}

/**
 * @brief 更新错误计数函数
 * 该函数遍历所有通道，检查并更新错误检测使能标志和错误检测使能历史标志
 * 对于每个通道，分别处理线路检查器和主机检查器的状态
 */
void Update_ErrorCount(void)
{
    // 遍历8个通道
	for (uint8_t ch = 0; ch < 8; ch++)
	{
        // 检查线路检查器的错误检测使能标志或历史标志
		if (BER[0][ch].EDEn_Flag || BER[0][ch].EDEn_HistoryFlag)
		{
            // 获取线路检查器状态
			get_line_Checker_Status(ch);
            // 更新历史标志为当前标志状态
			BER[0][ch].EDEn_HistoryFlag = BER[0][ch].EDEn_Flag;
		}
        // 检查主机检查器的错误检测使能标志或历史标志
		if (BER[1][ch].EDEn_Flag || BER[1][ch].EDEn_HistoryFlag)
		{
            // 获取主机检查器状态
			get_host_Checker_Status(ch);
            // 更新历史标志为当前标志状态
			BER[1][ch].EDEn_HistoryFlag = BER[1][ch].EDEn_Flag;
		}
	}
}

/**
 * @brief 分割命令
 * 
 * @param str 
 * @param spl 
 * @param maincmd 
 * @param subcmd 
 * @param val 
 * @return int 
 */
int splitcmd(char *str, const char *spl, char *maincmd, char *subcmd, uint8_t *val)
{
	char dst[10][100];	// 存储分割后的命令
	int n = 0;	// 分割后的命令数量
	char *result = NULL;
	result = strtok(str, spl);	// 使用spl分割字符串
	while (result != NULL)
	{
		strcpy(dst[n++], result);	// 将分割后的命令复制到dst数组中
		result = strtok(NULL, spl);	// 继续分割
	}

	sprintf(maincmd, "%s\r\n", dst[0]);	// 将第一个命令复制到maincmd中
	*val = 0;
	for (int j = 15; j >= 0; j--)	// 遍历15到0
	{
		char temp[10];	// 临时字符串
		sprintf(temp, "%d", j);	// 将j转换为字符串
		char *p = strstr(maincmd, temp);
		if (p != NULL)
		{
			*val = j;
			break;
		}
	}
	if (n > 1)
	{
		sprintf(subcmd, "%s", "");	// 将subcmd清空
		for (int i = 1; i < n; i++)	// 遍历分割后的命令
		{
			strcat(subcmd, ":");	// 在subcmd后面添加冒号
			strcat(subcmd, dst[i]);	// 将分割后的命令复制到subcmd中
		}
	}
	return n;
}
/**
 * @brief 分割命令
 * 
 * @param dst 
 * @param str 
 * @param spl 
 * @return int 
 */
int split(char dst[][100], char *str, const char *spl)
{
	int n = 0;
	char *result = NULL;
	result = strtok(str, spl);
	while (result != NULL)
	{
		strcpy(dst[n++], result);
		result = strtok(NULL, spl);
	}
	return n;
}

/**
 * @brief 发送数据
 * 
 * @param strval 
 */
void SendData(char *strval)
{
	// USBD_CDC_SetTxBuffer(&hUsbDeviceFS, (uint8_t*)strval, strlen(strval));
	// USBD_CDC_TransmitPacket(&hUsbDeviceFS);
	if (interface == 1)
		UsartSendCmd((uint8_t *)strval);
	else
		CDC_Transmit_FS((uint8_t *)strval, strlen(strval));
}

/**
 * @brief 获取命令
 * 
 * @param strcmd 
 * @param dst 
 * @return uint8_t 
 */
uint8_t getcommand(char *strcmd, char dst[][100])
{
	if (strncmp((char *)UserRxBufferFS, strcmd, strlen(strcmd)) == 0)
	{
		split(dst, (char *)UserRxBufferFS, " ");
		return 1;
	}
	else
		return 0;
}
	
/**
 * @brief 获取命令值
 * 
 * @param strcmd 
 * @param val 
 * @param itype 
 * @return int 
 */
int getcommand_value(char *strcmd, int *val, uint8_t *itype)
{
	char dst[10][100], temp[10];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if (strncmp((char *)UserRxBufferFS, strcmd, strlen(strcmd)) == 0)
	{
		int n = split(dst, (char *)UserRxBufferFS, ":");
		value = dst[n - 1];
		if (strstr(value, "?") != NULL || strstr(value, " ") != NULL || strstr(value, "CHAN") != NULL)
		{
			int m = split(&dst[n - 1], value, " ");
			for (int j = 16; j >= 1; j--)
			{
				sprintf(temp, "%d", j);
				char *p = strstr(dst[n - 1], temp);
				if (p != NULL)
				{
					val_cnt++;
					val[0] = j;
					break;
				}
			}
			for (int i = 0; i < m - 1; i++)
			{
				sscanf(dst[n + i], "%x", &val[val_cnt]);
				val_cnt++;
			}
			*itype = strstr(value, "?") != NULL ? 2 : 1;
		}
		return 1;
	}
	else
		return 0;
}
/**
 * @brief 获取命令值
 * 
 * @param strallcmd 
 * @param strsubcmd 
 * @param val 
 * @param itype 
 * @return int 
 */
int getcommand_value_float(char *strallcmd, char *strsubcmd, double *val, uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if (strncmp(strallcmd, strsubcmd, strlen(strsubcmd)) == 0)
	{
		int n = split(dst, strallcmd, ":");
		value = dst[n - 1];
		if (strstr(value, "?") != NULL || strstr(value, " ") != NULL)
		{
			*itype = strstr(value, "?") != NULL ? 2 : 1;
			// if(*itype==1)
			//{
			int nn = split(&dst[0], value, " ");
			int ll = split(&dst[0], dst[1], ",");
			for (int i = 0; i < ll; i++)
			{
				sscanf(dst[i], "%lf", &val[val_cnt]);
				val_cnt++;
			}
			//}
		}
		return 1;
	}
	else
		return 0;
}

/**
 * @brief 获取命令值
 * 
 * @param strallcmd 
 * @param strsubcmd 
 * @param val 
 * @param itype 
 * @return int 
 */
int getcommand_value_int(char *strallcmd, char *strsubcmd, int *val, uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if (strncmp(strallcmd, strsubcmd, strlen(strsubcmd)) == 0)
	{
		int n = split(dst, strallcmd, ":");
		value = dst[n - 1];
		if (strstr(value, "?") != NULL || strstr(value, " ") != NULL)
		{
			*itype = strstr(value, "?") != NULL ? 2 : 1;

			int nn = split(&dst[0], value, " ");
			int ll = split(&dst[0], dst[1], ",");
			for (int i = 0; i < ll; i++)
			{
				sscanf(dst[i], "%d", &val[val_cnt]);
				val_cnt++;
			}
		}
		return 1;
	}
	else
		return 0;
}
/**
 * @brief 获取命令值
 * 
 * @param strallcmd 
 * @param strsubcmd 
 * @param val 
 * @param itype 
 * @return int 
 */
int getcommand_value_hexint(char *strallcmd, char *strsubcmd, int *val, uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if (strncmp(strallcmd, strsubcmd, strlen(strsubcmd)) == 0)
	{
		int n = split(dst, strallcmd, ":");
		value = dst[n - 1];
		if (strstr(value, "?") != NULL || strstr(value, " ") != NULL)
		{
			*itype = strstr(value, "?") != NULL ? 2 : 1;

			int nn = split(&dst[0], value, " ");
			int ll = split(&dst[0], dst[1], ",");
			for (int i = 0; i < ll; i++)
			{
				sscanf(dst[i], "%x", &val[val_cnt]);
				val_cnt++;
			}
		}
		return 1;
	}
	else
		return 0;
}

//===============================================
/**
 * @brief 检查输入状态
 * 
 * @param rw 
 */
void CheckInput_Statue(uint8_t rw)
{
	if (rw > 0)
	{
		// TXDIS
		if (LPMode_Compare != C4->LPMode)	// 低功耗模式
		{
			LPMode_Compare = C4->LPMode;
			if (C4->LPMode)
				HAL_GPIO_WritePin(LPMode_GPIO_Port, LPMode_Pin, GPIO_PIN_SET);	// 设置低功耗模式
			else
				HAL_GPIO_WritePin(LPMode_GPIO_Port, LPMode_Pin, GPIO_PIN_RESET);	// 清除低功耗模式
		}
		if (RST_Compare != C4->ResetL)	// 复位信号
		{
			RST_Compare = C4->ResetL;
			if (C4->ResetL)
				HAL_GPIO_WritePin(ResetL_GPIO_Port, ResetL_Pin, GPIO_PIN_SET);	// 设置复位信号
			else
				HAL_GPIO_WritePin(ResetL_GPIO_Port, ResetL_Pin, GPIO_PIN_RESET);	// 清除复位信号
			HAL_Delay(2);
		}
		if (PM_Compare != C4->Power)	// 电源信号	
		{
			PM_Compare = C4->Power;	// 电源信号

			if (C4->Power)
				HAL_GPIO_WritePin(PM_GPIO_Port, PM_Pin, GPIO_PIN_SET);	// 设置电源信号
			else
				HAL_GPIO_WritePin(PM_GPIO_Port, PM_Pin, GPIO_PIN_RESET);	// 清除电源信号
			HAL_Delay(2);
		}
	}
	else
	{
		C4->LPMode = HAL_GPIO_ReadPin(LPMode_GPIO_Port, LPMode_Pin); // 读取低功耗模式
		C4->ResetL = HAL_GPIO_ReadPin(ResetL_GPIO_Port, ResetL_Pin);	// 读取复位信号
		C4->ModPrsL = HAL_GPIO_ReadPin(ModPrsL_GPIO_Port, ModPrsL_Pin);	// 读取模式选择信号
		C4->ModSEL = HAL_GPIO_ReadPin(ModSEL_GPIO_Port, ModSEL_Pin);	// 读取模式选择信号
		C4->IntL = HAL_GPIO_ReadPin(IntL_GPIO_Port, IntL_Pin);	// 读取内部逻辑电平信号
		C4->Power = HAL_GPIO_ReadPin(PM_GPIO_Port, PM_Pin);	// 读取电源信号
	}
}

/**
 * @brief  This function is receive usb to vcp data.
 * @param  none
 * @retval None
 */
/**
 * @brief 处理接收到的数据
 * @note 该函数用于处理通过串口接收到的命令，并根据不同的命令执行相应的操作
 */
void Process_ReceiveData(void)
{

	// 定义存储数据的数组
	char dst[10][100], strdata[512];  // 存储命令和响应数据的缓冲区
	uint8_t ch = 0, itype;           // 通道号和命令类型
	double fvalue[10] = {0};         // 存储浮点数值
	int ivalue[264];                 // 存储整型数值
	char maincmd[20], subcmd[250];
	bool ret = false;                // 操作返回状态

	// 处理接收标志位为1的情况
	if (RxFlag == 1)	
	{
		RxFlag = 0x00;	// 清零接收标志位
		HAL_GPIO_TogglePin(LINK_GPIO_Port, LINK_Pin);	// 切换链接状态
		if (getcommand("*IDN?", dst))	// 获取设备ID
		{
			char sn[16] = {32};	// 设备SN
			for (int n = 0; n < 16; n++)	// 遍历设备SN
			{
				if (C4->EVB_SN[n] == 0x20 || C4->EVB_SN[n] == 0x00)	// 如果设备SN为空
					break;
				sn[n] = C4->EVB_SN[n];	// 设置设备SN
			}
			// 固件版本：V1.1 2019-06-23
			sprintf(strdata, "OVLINK,DEBX-08112A-VA,%s,V1.84,%s\r\n", "2025022002", __DATE__); //__DATE__" "__TIME__;
			SendData(strdata);
		}
		else if (getcommand("*RST", dst))	// 复位
		{
			SendData("1\r\n");	// 发送成功
			HAL_Delay(20);
			__set_FAULTMASK(1); // 关闭所有中断
			NVIC_SystemReset(); // 复位
		}
		memset(UserRxBufferFS, 0, APP_TX_DATA_SIZE); // 清空接收缓冲区
	}
	else if (RxFlag == 2)	// 处理接收标志位为2的情况
	{
		RxFlag = 0x00;// 清零接收标志位
		HAL_GPIO_TogglePin(LINK_GPIO_Port, LINK_Pin);	// 切换链接状态
		splitcmd((char *)UserRxBufferFS, ":", maincmd, subcmd, &ch); // 分割命令

		if (strncmp(maincmd, "SYS", 3) == 0)	// 系统命令
		{
			// 1. 设置通道速率
			if (getcommand_value_int(subcmd, ":DATARATE", ivalue, &itype))	// 获取通道速率
			{
				if (itype == 1)
				{
					C4->DataRate = ivalue[0];	// 设置通道速率
					if (bcm87800_init_prbs(0))	// 初始化PRBS
						SendData("1\r\n");	// 发送成功
					else
					{
						SendData("0\r\n");	// 发送失败

						HAL_GPIO_WritePin(RESET_DSP_GPIO_Port, RESET_DSP_Pin, (GPIO_PinState)0);	// 重置DSP
						HAL_Delay(20);	// 延时20ms
						////						__set_FAULTMASK(1);		//关闭所有中断
						//						NVIC_SystemReset();		//复位

						bcm87800_download_firmware();	// 下载固件
						bool init = bcm87800_init_prbs(1);	// 初始化PRBS
					}
					// SendData("0\r\n");
				}
				else if (itype == 2)	
				{
					sprintf(strdata, "%d\r\n", C4->DataRate);	// 发送通道速率
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":EYEMODE", ivalue, &itype))	// 获取眼图模式
			{
				if (itype == 1)
				{
					C4->SignalMode = ivalue[0];	// 设置眼图模式
					if (bcm87800_init_prbs(0))	// 初始化PRBS
						SendData("1\r\n");	// 发送成功
					else
					{
						SendData("0\r\n");	// 发送失败

						HAL_GPIO_WritePin(RESET_DSP_GPIO_Port, RESET_DSP_Pin, (GPIO_PinState)0);
						HAL_Delay(20);
						////						__set_FAULTMASK(1);		//关闭所有中断
						//						NVIC_SystemReset();		//复位

						bcm87800_download_firmware();	// 下载固件
						bool init = bcm87800_init_prbs(1);	// 初始化PRBS
					}
					// SendData("0\r\n");
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", C4->SignalMode);	// 发送眼图模式
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_float(subcmd, ":REFCLK", fvalue, &itype))	// 获取参考时钟
			{
				if (itype == 1)
				{
					SetOutputRefClock(fvalue[0]);	// 设置输出参考时钟
					SendData("1\r\n");	// 发送成功
				}
			}
			else if (getcommand_value_int(subcmd, ":RST", ivalue, &itype))	// 复位
			{
				if (itype == 1)
				{
					SendData("1\r\n");	// 发送成功
					HAL_Delay(10);	// 延时10ms
					__set_FAULTMASK(1); // 关闭所有中断
					NVIC_SystemReset(); // 复位
				}
			}
			else if (getcommand_value_int(subcmd, ":DSPRST", ivalue, &itype))	// 重置DSP
			{
				uint8_t State = ivalue[0];	// 获取状态
				HAL_GPIO_WritePin(RESET_DSP_GPIO_Port, RESET_DSP_Pin, (GPIO_PinState)State);	// 设置DSP状态
				SendData("1\r\n");
			}
			else if (getcommand_value_int(subcmd, ":FECPGEN", ivalue, &itype))	// 设置FEC生成
			{
				if (itype == 1 && C4->SignalMode == 2)	// 如果信号模式为2
				{
					if (bcm87800_set_fec_pgen(ivalue[0]))	// 设置FEC生成
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");	// 发送失败
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", 1);	// 发送FEC生成
					SendData(strdata);	// 发送数据
				}
			}
			// 切换触发分频
			else if (getcommand_value_int(subcmd, ":TRIG", ivalue, &itype))	// 切换触发分频
			{
				if (itype == 1)	// 如果命令类型为1
				{
					C4->ClkDivide = ivalue[0];	// 设置触发分频
					if (bcm87800_clock_divider2())	// 切换触发分频
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");
				}
				else
				{
					sprintf(strdata, "%d\r\n", C4->ClkDivide);// 发送触发分频
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_hexint(subcmd, ":IIC", ivalue, &itype))	// 读取IIC寄存器
			{
				uint8_t devadd, regaddr = 0;	// 设备地址和寄存器地址
				uint16_t size = 0;	// 寄存器数量
				uint8_t regval[128];	// 寄存器值

				devadd = ivalue[0];	// 设备地址
				regaddr = ivalue[1];	// 寄存器地址
				size = ivalue[2];	// 寄存器数量

				if (size == 0)
					size = 1;	// 如果寄存器数量为0，则设置为1
				if (size > 16)
					size = 16;	// 如果寄存器数量大于16，则设置为16
				if (itype == 1)
				{
					for (uint16_t i = 0; i < size; i++)	// 遍历寄存器数量
					{
						regval[i] = ivalue[i + 3];	// 将寄存器值赋值给regval
					}
					if (I2C_WriteBytes(devadd, regaddr, regval, size))	// 写入IIC寄存器
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");	// 发送失败
				}
				else if (itype == 2)	// 如果命令类型为2
				{
					uint8_t ret = 0;	// 返回值

					ret = I2C_ReadBytes(devadd, regaddr, regval, size);	// 读取IIC寄存器
					if (ret > 0)
					{
						char temp[10];	// 临时字符串
						for (int n = 0; n < size; n++)	// 遍历寄存器数量
						{
							sprintf(temp, "%.2X", regval[n]);	// 将寄存器值转换为字符串
							if (n == 0)
							{
								sprintf(strdata, "%s,", strcpy(strdata, temp));	// 将寄存器值赋值给strdata
							}
							else
							{
								sprintf(strdata, "%s,", strcat(strdata, temp));	// 将寄存器值赋值给strdata
							}
						}
						// strdata[size*3-1]='\0';
						char str[50];	// 临时字符串
						sprintf(str, "\r\n");	// 将换行符赋值给str
						strcat(strdata, str);	// 将换行符赋值给strdata

						SendData(strdata);	// 发送数据
					}
					else
					{
						SendData(":SYS:I2C NG\r\n");	// 发送失败
					}
				}
			}
			else if (getcommand_value_int(subcmd, ":DUT:STATE", ivalue, &itype))	// 设置DUT状态
			{
				if (itype == 1)
				{
					C4_0_255_Table[128] = ivalue[0] & 0xff;	// 设置DUT状态
					CheckInput_Statue(1);	// 检查输入状态
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)
				{
					CheckInput_Statue(0);	// 检查输入状态
					sprintf(strdata, "%d\r\n", C4_0_255_Table[128]);	// 发送DUT状态
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":PWONOFF", ivalue, &itype))	// 设置电源开关	
			{
				if (itype == 1)	
				{
					HAL_GPIO_WritePin(PM_GPIO_Port, PM_Pin, (GPIO_PinState)(ivalue[0]));	// 设置电源开关
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", HAL_GPIO_ReadPin(PM_GPIO_Port, PM_Pin));	// 发送电源开关
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":LPMODE", ivalue, &itype))	// 设置低功耗模式
			{
				if (itype == 1)
				{

					HAL_GPIO_WritePin(LPMode_GPIO_Port, LPMode_Pin, (GPIO_PinState)(ivalue[0]));	// 设置低功耗模式
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", HAL_GPIO_ReadPin(LPMode_GPIO_Port, LPMode_Pin));	// 发送低功耗模式
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":RESETL", ivalue, &itype))	// 设置复位信号
			{
				if (itype == 1)
				{

					HAL_GPIO_WritePin(ResetL_GPIO_Port, ResetL_Pin, (GPIO_PinState)(ivalue[0]));	// 设置复位信号
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", HAL_GPIO_ReadPin(ResetL_GPIO_Port, ResetL_Pin));	// 发送复位信号
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":MODSEL", ivalue, &itype))	// 设置模式选择
			{
				if (itype == 1)
				{

					HAL_GPIO_WritePin(ModSEL_GPIO_Port, ModSEL_Pin, (GPIO_PinState)(ivalue[0]));	// 设置模式选择
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", HAL_GPIO_ReadPin(ModSEL_GPIO_Port, ModSEL_Pin));	// 发送模式选择
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":VCCDAC", ivalue, &itype))	// 设置DAC电压
			{
				if (itype == 1)
				{
					C4->VCC_DAC = ivalue[0];	// 设置DAC电压
					SPI_DAC_Output();	// 输出DAC电压
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d\r\n", C4->VCC_DAC);	// 发送DAC电压
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":DDM", ivalue, &itype))	// 设置DDM
			{
				if (itype == 2)
				{
					sprintf(strdata, "%d,%d,%d,%d\r\n", C4->DDM_Temp, C4->DDM_Vcc, C4->DDM_I, C4->DDM_POWER);	// 发送DDM
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":IALARM", ivalue, &itype))	// 设置报警电流
			{
				if (itype == 1)
				{
					C4->IALARM = ivalue[0];	// 设置报警电流
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d\r\n", C4->IALARM);	// 发送报警电流
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":CAL:BIAS", ivalue, &itype))	// 设置偏置电流
			{
				if (itype == 1)
				{
					C4->I_slope = ivalue[0];	// 设置偏置电流斜率
					C4->I_offset = ivalue[1];	// 设置偏置电流偏移
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d,%d\r\n", C4->I_slope, C4->I_offset);	// 发送偏置电流
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":CAL:TEMP", ivalue, &itype))	// 设置温度
			{
				if (itype == 1)
				{
					C4->Temp_offset = ivalue[1];	// 设置温度偏移
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d,%d\r\n", 0x100, C4->Temp_offset);	// 发送温度
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":CAL:VCC", ivalue, &itype))	// 设置VCC
			{
				if (itype == 1)	// 如果命令类型为1
				{
					C4->Vcc_slope = ivalue[0];	// 设置VCC斜率
					C4->Vcc_offset = ivalue[1];	// 设置VCC偏移
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d,%d\r\n", C4->Vcc_slope, C4->Vcc_offset);	// 发送VCC
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":CAL:SETVCC", ivalue, &itype))	// 设置VCC
			{
				if (itype == 1)
				{
					C4->VCC_DAC_slope = ivalue[0];	// 设置VCC斜率
					C4->VCC_DAC_offset = ivalue[1];	// 设置VCC偏移
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d,%d\r\n", C4->VCC_DAC_slope, C4->VCC_DAC_offset);	// 发送VCC
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":INTERDIR", ivalue, &itype))	// 设置接口
			{
				if (itype == 1)
				{
					C4->Interface = ivalue[0];	// 设置接口
					SendData("1\r\n");	// 发送成功
				}
				else
				{
					sprintf(strdata, "%d\r\n", C4->Interface);	// 发送接口
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":CAL:SAVEALL", ivalue, &itype))	// 保存所有
			{
				if (itype == 1)
				{
					Flash_Write(FLASH_USER_START_ADDR, C4_0_255_Table, 256);	// 写入Flash
					HAL_Delay(100);	// 延时100ms
					SendData("1\r\n");	// 发送成功
				}
			}
			else
			{
				SendData("0\r\n");
			}
			memset(UserRxBufferFS, 0, APP_TX_DATA_SIZE);
		}
		else if (strncmp(maincmd, "PPG", 3) == 0)	// 如果主命令为PPG
		{
			// 1.使能 PPG 通道输入输出
			if (getcommand_value_int(subcmd, ":SQUELCH", ivalue, &itype))	// 使能 PPG 通道输入输出
			{
				if (itype == 1)	// 如果命令类型为1
				{
					C4->TxSquelch[ch] = ivalue[0];	// 设置通道输入输出
					if (bcm87800_tx_squelch(ch, C4->TxSquelch[ch]))	// 设置通道输入输出
					{
						SendData("1\r\n");	// 发送成功
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", C4->TxSquelch[ch]);	// 发送通道输入输出
					SendData(strdata);	// 发送数据
				}
			}
			// 2.使能 PPG 所有通道输入输出
			else if (getcommand_value_int(subcmd, ":ALL:SQUELCH", ivalue, &itype))	// 使能 PPG 所有通道输入输出
			{
				if (itype == 1)	// 如果命令类型为1
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						C4->TxSquelch[ch] = ivalue[0];	// 设置通道输入输出
						ret &= bcm87800_tx_squelch(ch, C4->TxSquelch[ch]);	// 设置通道输入输出
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else
				{
					SendData("0\r\n");
				}
			}
			// 5. 配置通道的码型
			//	PRBS7,PRBS9_4,PRBS9_5,PRBS11,PRBS13,PRBS15,PRBS16,PRBS23,PRBS31,PRBS58,FIXED,JP083B,LIN,CJT,SSPRQ
			else if (getcommand_value_int(subcmd, ":PATTERN", ivalue, &itype))	// 配置通道的码型
			{
				if (itype == 1)	// 如果命令类型为1
				{
					if (C4->Interface == 0)
					{
						C4->LaneTxPattern[ch] = ivalue[0];	// 设置通道码型
						if (bcm87800_tx_prbs(ch, C4->LaneTxPattern[ch]))	// 设置通道码型
						{
							SendData("1\r\n");	// 发送成功
						}
						else
							SendData("0\r\n");
					}
					else
					{
						C4->HostTxPattern[ch] = ivalue[0];	// 设置通道码型
						if (bcm87800_tx_prbs(ch, C4->HostTxPattern[ch]))	// 设置通道码型
							SendData("1\r\n");	// 发送成功
						else
							SendData("0\r\n");
					}
				}
				// 6.查询通道的码型
				else if (itype == 2)
				{
					if (C4->Interface == 0)
						sprintf(strdata, "%d\r\n", C4->LaneTxPattern[ch]);	// 发送通道码型
					else
						sprintf(strdata, "%d\r\n", C4->HostTxPattern[ch]);	// 发送通道码型
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":ALL:PATTERN", ivalue, &itype))	// 配置所有通道的码型
			{
				if (itype == 1)
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						if (C4->Interface == 0)
						{
							C4->LaneTxPattern[ch] = ivalue[0];	// 设置通道码型
							ret &= bcm87800_tx_prbs(ch, C4->LaneTxPattern[ch]);	// 设置通道码型
						}
						else
						{
							C4->HostTxPattern[ch] = ivalue[0];	// 设置通道码型
							ret &= bcm87800_tx_prbs(ch, C4->HostTxPattern[ch]);	// 设置通道码型
						}
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");	// 发送成功
					}
					else
						SendData("0\r\n");
				}
			}
			else if (getcommand_value_int(subcmd, ":USERPATTERN", ivalue, &itype))	// 配置用户码型
			{
				if (itype == 1)
				{
					if (C4->Interface == 0)
					{
							C4->LaneTxPattern[ch] = ivalue[0];	// 设置通道码型
						if (bcm87800_tx_prbs(ch, C4->LaneTxPattern[ch]))	// 设置通道码型
						{
							SendData("1\r\n");
						}
						else
							SendData("0\r\n");
					}
					else
					{
						C4->HostTxPattern[ch] = ivalue[0];	// 设置通道码型
						if (bcm87800_tx_prbs(ch, C4->HostTxPattern[ch]))	// 设置通道码型
							SendData("1\r\n");	// 发送成功
						else
							SendData("0\r\n");
					}
					SendData("1\r\n");
				}
				else if (itype == 2)	// 如果命令类型为2
				{
					// sprintf(strdata,"%d,%d,%d,%d\r\n",BER[ch].fixed_pat0,BER[ch].fixed_pat1,BER[ch].fixed_pat2,BER[ch].fixed_pat3);
					SendData(strdata);
				}
			}

			// 9. 配置通道的输出幅值
			else if (getcommand_value_int(subcmd, ":TAPS", ivalue, &itype))	// 配置通道的输出幅值
			{
				if (itype == 1)
				{
					BER[C4->Interface][ch].line_taps[0] = ivalue[0];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[1] = ivalue[1];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[2] = ivalue[2];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[3] = ivalue[3];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[4] = ivalue[4];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[5] = ivalue[5];	// 设置通道输出幅值
					BER[C4->Interface][ch].line_taps[6] = ivalue[6];	// 设置通道输出幅值
					BER[C4->Interface][ch].lane_num_taps = ivalue[7];
					if (bcm87800_tx_emphasis(ch))
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2) // 10. 查询通道的输出幅值
				{
					sprintf(strdata, "%d,%d,%d,%d,%d,%d,%d,%d\r\n",
							BER[C4->Interface][ch].line_taps[0],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[1],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[2],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[3],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[4],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[5],	// 发送通道输出幅值
							BER[C4->Interface][ch].line_taps[6],	// 发送通道输出幅值
							BER[C4->Interface][ch].lane_num_taps);	// 发送通道输出幅值
					SendData(strdata);	// 发送数据
				}
			}
			// 11. 配置所有通道的输出幅值
			else if (getcommand_value_int(subcmd, ":ALL:TAPS", ivalue, &itype))	// 配置所有通道的输出幅值
			{
				if (itype == 1)
				{
					ret = true;	// 设置返回值

					C4->hostline_Taps[0] = ivalue[0];	// 设置通道输出幅值
					C4->hostline_Taps[1] = ivalue[1];	// 设置通道输出幅值
					C4->hostline_Taps[2] = ivalue[2];	// 设置通道输出幅值
					C4->hostline_Taps[3] = ivalue[3];	// 设置通道输出幅值
					C4->hostline_Taps[4] = ivalue[4];	// 设置通道输出幅值
					C4->hostline_Taps[5] = ivalue[5];	// 设置通道输出幅值
					C4->hostline_Taps[6] = ivalue[6];	// 设置通道输出幅值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						BER[C4->Interface][ch].line_taps[0] = ivalue[0];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[1] = ivalue[1];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[2] = ivalue[2];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[3] = ivalue[3];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[4] = ivalue[4];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[5] = ivalue[5];	// 设置通道输出幅值
						BER[C4->Interface][ch].line_taps[6] = ivalue[6];	// 设置通道输出幅值
						BER[C4->Interface][ch].lane_num_taps = ivalue[7];	// 设置通道输出幅值
						ret &= bcm87800_tx_emphasis(ch);	// 设置通道输出幅值
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");	// 发送成功
					}
					else
						SendData("0\r\n");
				}
			}
			// 12. 配置输出通道极性
			else if (getcommand_value_int(subcmd, ":POLA", ivalue, &itype))	// 配置输出通道极性
			{
				if (itype == 1)
				{
					C4->TxInvert[ch] = ivalue[0];	// 设置通道输出极性
					if (bcm87800_tx_polarity(ch, (C4->TxInvert[ch] == 0) ? true : false))	// 设置通道输出极性
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2)	// 如果命令类型为2
				{
					sprintf(strdata, "%d\r\n", C4->TxInvert[ch]);	// 发送通道输出极性
					SendData(strdata);	// 发送数据
				}
			}
			// 21. 配置所有通道输出极性
			else if (getcommand_value_int(subcmd, ":ALL:POLA", ivalue, &itype))	// 配置所有通道输出极性
			{	
				if (itype == 1)
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						C4->TxInvert[ch] = ivalue[0];	// 设置通道输出极性
						ret &= bcm87800_tx_polarity(ch, (C4->TxInvert[ch] == 0) ? true : false);	// 设置通道输出极性
					}
					if (ret)	// 如果返回值为真
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");
				}
				else
				{
					SendData("0\r\n");
				}
			}
			else
			{
				SendData("0\r\n");
			}
		}
		else if (strncmp(maincmd, "ED", 2) == 0)	
		{
			// 1. 配置通道的码型
			//	PRBS7,PRBS9_4,PRBS9_5,PRBS11,PRBS13,PRBS15,PRBS16,PRBS23,PRBS31,PRBS58,FIXED,JP083B,LIN,CJT,SSPRQ
			if (getcommand_value_int(subcmd, ":PATTERN", ivalue, &itype))	// 配置通道的码型
			{
				if (itype == 1)	
				{
					if (C4->Interface == 0)	
					{
						C4->LaneRxPattern[ch] = ivalue[0];	// 设置通道码型
						ret = bcm87800_rx_prbs(ch, C4->LaneRxPattern[ch]);	// 设置通道码型
					}
					else
					{
						C4->HostRxPattern[ch] = ivalue[0];	// 设置通道码型
						ret = bcm87800_rx_prbs(ch, C4->HostRxPattern[ch]);	// 设置通道码型
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2)
				{
					if (C4->Interface == 0)
						sprintf(strdata, "%d\r\n", C4->LaneRxPattern[ch]);	// 发送通道码型
					else
						sprintf(strdata, "%d\r\n", C4->HostRxPattern[ch]);	// 发送通道码型
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":ALL:PATTERN", ivalue, &itype))	// 配置所有通道的码型
			{
				if (itype == 1)
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						if (C4->Interface == 0)
						{
							C4->LaneRxPattern[ch] = ivalue[0];	// 设置通道码型
							ret &= bcm87800_rx_prbs(ch, C4->LaneRxPattern[ch]);	// 设置通道码型
						}
						else
						{
							C4->HostRxPattern[ch] = ivalue[0];	// 设置通道码型
							ret &= bcm87800_rx_prbs(ch, C4->HostRxPattern[ch]);	// 设置通道码型
						}
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");	// 发送成功
					}
					else
						SendData("0\r\n");
				}
			}
			// 3. 配置输入信号极性
			else if (getcommand_value_int(subcmd, ":POLA", ivalue, &itype))	// 配置输入信号极性
			{
				if (itype == 1)
				{
					C4->RxInvert[ch] = ivalue[0];	// 设置通道输入极性
					ret = bcm87800_rx_polarity(ch, C4->RxInvert[ch] == 0 ? true : false);	// 设置通道输入极性
					if (ret)	// 如果返回值为真
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");
				}
				else if (itype == 2)
				{
					sprintf(strdata, "%d\r\n", C4->RxInvert[ch]);	// 发送通道输入极性
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":RXINFO", ivalue, &itype))	// 配置输入信号信息
			{
				if (itype == 1)
				{
					C4->gain_boost = ivalue[0];	// 设置增益提升
					C4->peaking_filter = ivalue[1];	// 设置峰值滤波
					if (C4->Interface == 1)	// 如果接口为1
						C4->host_rx_bw_value = ivalue[2];	// 设置通道输入带宽
					else
						C4->lane_rx_bw_value = ivalue[2];	// 设置通道输入带宽

					C4->vga = ivalue[3];	// 设置VGA

					ret = bcm87800_set_rx_info(ch);	// 设置通道输入信息
					if (ret)	// 如果返回值为真
						SendData("1\r\n");	// 发送成功
					else
						SendData("0\r\n");
				}
				else if (itype == 2)
				{
					if (C4->Interface == 0)
						sprintf(strdata, "%d,%d,%d,%d\r\n", C4->gain_boost, C4->peaking_filter, C4->lane_rx_bw_value, C4->vga);	// 发送通道输入信息
					else
						sprintf(strdata, "%d,%d,%d,%d\r\n", C4->gain_boost, C4->peaking_filter, C4->host_rx_bw_value, C4->vga);	// 发送通道输入信息
					SendData(strdata);	// 发送数据
				}
			}
			else if (getcommand_value_int(subcmd, ":ALL:RXINFO", ivalue, &itype))	// 配置所有通道输入信息
			{
				ret = true;	// 设置返回值
				for (ch = 0; ch < 8; ch++)	// 遍历通道
				{
					C4->gain_boost = ivalue[0];	// 设置增益提升
					C4->peaking_filter = ivalue[1];	// 设置峰值滤波
					if (C4->Interface == 1)	// 如果接口为1
						C4->host_rx_bw_value = ivalue[2];	// 设置通道输入带宽
					else
						C4->lane_rx_bw_value = ivalue[2];	// 设置通道输入带宽
					C4->vga = ivalue[3];	// 设置VGA
					ret &= bcm87800_set_rx_info(ch);	// 设置通道输入信息
				}
				if (ret)	// 如果返回值为真
					SendData("1\r\n");	// 发送成功
				else
					SendData("0\r\n");
			}
			// 3. 配置输入信号极性
			else if (getcommand_value_int(subcmd, ":ALL:POLA", ivalue, &itype))	// 配置所有通道输入极性
			{
				if (itype == 1)	
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						C4->RxInvert[ch] = ivalue[0];	// 设置通道输入极性
						ret &= bcm87800_rx_polarity(ch, C4->RxInvert[ch] == 0 ? true : false);	// 设置通道输入极性
					}
					if (ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
			}
			// 7.开始误码测试
			else if (getcommand_value_int(subcmd, ":MEAS:ENABLE", ivalue, &itype))	// 开始误码测试
			{
				if (itype == 1)
				{
					bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数
					if (C4->SignalMode != 2)	// 如果信号模式不为2
					{
						C4->RxEnable = ivalue[0];	// 设置通道使能
						BER[C4->Interface][ch].RealErrCount_msb = 0;	// 清零误码计数
						BER[C4->Interface][ch].RealErrCount_lsb = 0;	// 清零误码计数
						BER[C4->Interface][ch].RealBitCount = 0;	// 清零误码位数

						BER[C4->Interface][ch].AccumErrCount_msb = 0;	// 清零误码计数
						BER[C4->Interface][ch].AccumErrCount_lsb = 0;	// 清零误码计数
						BER[C4->Interface][ch].AccumBitCount = 0;	// 清零误码位数
						BER[C4->Interface][ch].EDLockFlag = 0;	// 清零误码锁定标志
						ret = bcm87800_enable_checker(ch);	// 使能误码测试
						BER[C4->Interface][ch].EDEn_Flag = C4->RxEnable;	// 设置误码使能
						BER[C4->Interface][ch].FECEn_Flag = 0;	// 清零FEC使能
					}
					else
					{
						C4->FEC_Enable = ivalue[0];	// 设置FEC使能

						ret = bcm87800_set_fec_init(ch, C4->FEC_Enable);	// 设置FEC初始化
						ret &= bcm87800_set_fec_clear(ch);	// 设置FEC清除
						BER[C4->Interface][ch].EDEn_Flag = 0;	// 清零误码使能
						BER[C4->Interface][ch].FECEn_Flag = C4->FEC_Enable;	// 设置FEC使能
						BER[C4->Interface][ch].pre_fec_ber = 0;	// 清零FEC误码
						BER[C4->Interface][ch].post_fec_ber = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_rev_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_corr_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].fec_lock_flag = 0;	// 清零FEC锁定标志
						for (int c = 0; c < 16; c++)	// 遍历FEC误码
							BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;	// 清零FEC误码

						// on fec
					}
					if (ret)	// 如果返回值为真
					{
						SendData("1\r\n");	// 发送成功
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2) // 9. 查询误码测试
				{
					sprintf(strdata, "%d\r\n", BER[C4->Interface][ch].EDEn_Flag);	// 发送误码使能
					SendData(strdata);	// 发送数据
				}
			}
			// 8. 开始所有通道误码测试
			else if (getcommand_value_int(subcmd, ":ALL:MEAS:ENABLE", ivalue, &itype))	// 开始所有通道误码测试
			{
				if (itype == 1)	
				{
					ret = true;	// 设置返回值
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数
						if (C4->SignalMode != 2)	// 如果信号模式不为2
						{
							C4->RxEnable = ivalue[0];	// 设置通道使能
							BER[C4->Interface][ch].RealErrCount_msb = 0;	// 清零误码计数
							BER[C4->Interface][ch].RealErrCount_lsb = 0;	// 清零误码计数
							BER[C4->Interface][ch].RealBitCount = 0;	// 清零误码位数

							BER[C4->Interface][ch].AccumErrCount_msb = 0;	// 清零误码计数
							BER[C4->Interface][ch].AccumErrCount_lsb = 0;	// 清零误码计数
							BER[C4->Interface][ch].AccumBitCount = 0;	// 清零误码位数
							BER[C4->Interface][ch].EDLockFlag = 0;	// 清零误码锁定标志
							BER[C4->Interface][ch].EDEn_Flag = C4->RxEnable;	// 设置误码使能
							BER[C4->Interface][ch].FECEn_Flag = 0;	// 清零FEC使能

							ret &= bcm87800_enable_checker(ch);	// 使能误码测试
						}
						else
						{
							C4->FEC_Enable = ivalue[0];	// 设置FEC使能
							BER[C4->Interface][ch].EDEn_Flag = 0;	// 清零误码使能
							BER[C4->Interface][ch].FECEn_Flag = C4->FEC_Enable;	// 设置FEC使能
							ret &= bcm87800_set_fec_init(ch, C4->FEC_Enable);	// 设置FEC初始化
							ret &= bcm87800_set_fec_clear(ch);	// 设置FEC清除
							BER[C4->Interface][ch].fec_lock_flag = 0;	// 清零FEC锁定标志
							BER[C4->Interface][ch].pre_fec_ber = 0;	// 清零FEC误码
							BER[C4->Interface][ch].post_fec_ber = 0;	// 清零FEC误码
							BER[C4->Interface][ch].tot_frame_rev_cnt = 0;	// 清零FEC误码
							BER[C4->Interface][ch].tot_frame_corr_cnt = 0;	// 清零FEC误码
							BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;	// 清零FEC误码
							BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;	// 清零FEC误码
							for (int c = 0; c < 16; c++)	// 遍历FEC误码
								BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;	// 清零FEC误码

							// on fec
						}
					}
					if (ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if (itype == 2) // 10. 查询所有通道误码测试
				{
					SendData("0\r\n");
				}
			}
			// 13. 清除通道误码数
			else if (getcommand_value_int(subcmd, ":ERR:CLEAR", ivalue, &itype))	// 清除通道误码数
			{
				bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数
				if (C4->SignalMode != 2)	// 如果信号模式不为2
				{
					BER[C4->Interface][ch].RealErrCount_msb = 0;	// 清零误码计数
					BER[C4->Interface][ch].RealErrCount_lsb = 0;	// 清零误码计数
					BER[C4->Interface][ch].RealBitCount = 0;	// 清零误码位数

					BER[C4->Interface][ch].AccumErrCount_msb = 0;	// 清零误码计数
					BER[C4->Interface][ch].AccumErrCount_lsb = 0;	// 清零误码计数
					BER[C4->Interface][ch].AccumBitCount = 0;	// 清零误码位数
					BER[C4->Interface][ch].EDLockFlag = 0;	// 清零误码锁定标志
					ret = true;
				}
				else
				{
					BER[C4->Interface][ch].fec_lock_flag = 0;	// 清零FEC锁定标志
					BER[C4->Interface][ch].pre_fec_ber = 0;	// 清零FEC误码
					BER[C4->Interface][ch].post_fec_ber = 0;	// 清零FEC误码
					BER[C4->Interface][ch].tot_frame_rev_cnt = 0;	// 清零FEC误码
					BER[C4->Interface][ch].tot_frame_corr_cnt = 0;	// 清零FEC误码
					BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;	// 清零FEC误码
					BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;	// 清零FEC误码
					ret = bcm87800_set_fec_clear(ch);	// 设置FEC清除
					for (int c = 0; c < 16; c++)	// 遍历FEC误码
						BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;	// 清零FEC误码
				}
				if (ret)	// 如果返回值为真
					SendData("1\r\n");	// 发送成功
				else
					SendData("0\r\n");	// 发送失败
			}
			// 14. 清除所有通道误码数
			else if (getcommand_value_int(subcmd, ":ALL:ERR:CLEAR", ivalue, &itype))	// 清除所有通道误码数
			{
				ret = true;	// 设置返回值
				for (ch = 0; ch < 8; ch++)	// 遍历通道
				{
					bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数

					if (C4->SignalMode != 2)	// 如果信号模式不为2
					{
						BER[C4->Interface][ch].RealErrCount_msb = 0;	// 清零误码计数
						BER[C4->Interface][ch].RealErrCount_lsb = 0;	// 清零误码计数
						BER[C4->Interface][ch].RealBitCount = 0;	// 清零误码位数

						BER[C4->Interface][ch].AccumErrCount_msb = 0;	// 清零误码计数
						BER[C4->Interface][ch].AccumErrCount_lsb = 0;	// 清零误码计数
						BER[C4->Interface][ch].AccumBitCount = 0;	// 清零误码位数
						BER[C4->Interface][ch].EDLockFlag = 0;	// 清零误码锁定标志
						ret = true;
					}
					else
					{
						ret &= bcm87800_set_fec_clear(ch);	// 设置FEC清除
						BER[C4->Interface][ch].fec_lock_flag = 0;	// 清零FEC锁定标志
						BER[C4->Interface][ch].pre_fec_ber = 0;	// 清零FEC误码
						BER[C4->Interface][ch].post_fec_ber = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_rev_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_corr_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;	// 清零FEC误码
						BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;	// 清零FEC误码
						for (int c = 0; c < 16; c++)	// 遍历FEC误码
							BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;	// 清零FEC误码
					}
				}
				if (ret)
					SendData("1\r\n");
				else
					SendData("0\r\n");
			}
			// 15. 读取通道误码数
			else if (getcommand_value_int(subcmd, ":MEAS:DATA?", ivalue, &itype))	// 读取通道误码数
			{
				st = accum_time;	// 设置时间计数
				memset(strdata, 0, 256);	// 清零字符串
				if (BER[C4->Interface][ch].EDEn_Flag || BER[C4->Interface][ch].EDEn_HistoryFlag)	// 如果误码使能或误码历史标志为真
				{
					bcm87800_Checker_Status(ch);	// 读取通道误码状态
					BER[C4->Interface][ch].EDEn_HistoryFlag = BER[C4->Interface][ch].EDEn_Flag;	// 设置误码历史标志
				}
				sprintf(strdata, "%.0f,%.0f,%.0f,%.0f,%.0f,%.0f,%d\r\n", (double)BER[C4->Interface][ch].RealErrCount_msb, (double)BER[C4->Interface][ch].RealErrCount_lsb, (double)BER[C4->Interface][ch].RealBitCount,
						(double)BER[C4->Interface][ch].AccumErrCount_msb, (double)BER[C4->Interface][ch].AccumErrCount_lsb, (double)BER[C4->Interface][ch].AccumBitCount, BER[C4->Interface][ch].EDLockFlag);	// 发送误码数
				SendData(strdata);	// 发送数据
				BER[C4->Interface][ch].RealErrCount_msb = 0;	// 清零误码计数
				BER[C4->Interface][ch].RealErrCount_lsb = 0;	// 清零误码计数
				BER[C4->Interface][ch].RealBitCount = 0;	// 清零误码位数
				et = accum_time - st;	// 设置时间计数
				bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数
			}
			else if (getcommand_value_int(subcmd, ":MEAS:FECDATA?", ivalue, &itype))	// 读取FEC误码数
			{
				st = accum_time;	// 设置时间计数
				memset(strdata, 0, 256);	// 清零字符串
				if (BER[C4->Interface][ch].FECEn_Flag || BER[C4->Interface][ch].FECEn_HistoryFlag)	// 如果FEC使能或FEC历史标志为真
				{
					bcm87800_set_fec_status(ch);	// 读取FEC状态
					BER[C4->Interface][ch].FECEn_HistoryFlag = BER[C4->Interface][ch].FECEn_Flag;	// 设置FEC历史标志
				}
				char str[50];	// 字符串
				memset(strdata, 0, 512);	// 清零字符串
				sprintf(str, "%0.3e,", BER[C4->Interface][ch].pre_fec_ber);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码
				sprintf(str, "%0.3e,", BER[C4->Interface][ch].post_fec_ber);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码
				sprintf(str, "%012llX,", BER[C4->Interface][ch].tot_frame_rev_cnt);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码
				sprintf(str, "%012llX,", BER[C4->Interface][ch].tot_frame_corr_cnt);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码
				sprintf(str, "%012llX,", BER[C4->Interface][ch].tot_frame_uncorr_cnt);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码
				sprintf(str, "%012llX,", BER[C4->Interface][ch].tot_symbols_corr_cnt);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码

				for (int c = 0; c < 16; c++)	// 遍历FEC误码
				{
					sprintf(str, "%012llX,", BER[C4->Interface][ch].tot_frames_err_cnt[c]);	// 发送FEC误码
					strcat(strdata, str);	// 添加FEC误码
				}
				sprintf(str, "%d\r\n", BER[C4->Interface][ch].fec_lock_flag);	// 发送FEC误码
				strcat(strdata, str);	// 添加FEC误码

				// strdata[511]='\0';
				SendData(strdata);	// 发送数据
				et = accum_time - st;	// 设置时间计数
				bert_TimeCountStart[C4->Interface][ch] = accum_time;	// 设置时间计数
			}
			else if (getcommand_value_int(subcmd, ":ALL:AUTOPOLA", ivalue, &itype))	// 配置所有通道输入极性
			{
				if (itype == 1)	// 如果类型为1
				{
					C4->RxAutoInvert = ivalue[0];	// 设置通道输入极性
					for (ch = 0; ch < 8; ch++)	// 遍历通道
					{
						// in115025_enable_checker(ch);	// 使能误码测试
					}
					SendData("1\r\n");	// 发送成功
				}
				else if (itype == 2)	// 如果类型为2
				{
					sprintf(strdata, "%d\r\n", C4->RxAutoInvert);	// 发送通道输入极性
					SendData(strdata);	// 发送数据
				}
			}
				else	// 如果类型为其他
			{
				SendData("0\r\n");	// 发送失败
			}
		}
		else
		{
			SendData("0\r\n");
		}
		memset(UserRxBufferFS, 0, APP_RX_DATA_SIZE);	// 清零用户接收缓冲区
	}
}

/**
 * @brief 获取Flash扇区
 * @param Address Flash地址
 * @return uint32_t Flash扇区
 */
uint32_t GetSector(uint32_t Address)
{
	uint32_t sector = 0;	// 扇区

	if ((Address < ADDR_FLASH_SECTOR_1) && (Address >= ADDR_FLASH_SECTOR_0))	// 如果地址小于扇区1且大于等于扇区0
	{
		sector = FLASH_SECTOR_0;	// 扇区0
	}
	else if ((Address < ADDR_FLASH_SECTOR_2) && (Address >= ADDR_FLASH_SECTOR_1))	// 如果地址小于扇区2且大于等于扇区1
	{
		sector = FLASH_SECTOR_1;	// 扇区1
	}
	else if ((Address < ADDR_FLASH_SECTOR_3) && (Address >= ADDR_FLASH_SECTOR_2))	// 如果地址小于扇区3且大于等于扇区2
	{
		sector = FLASH_SECTOR_2;	// 扇区2
	}
	else if ((Address < ADDR_FLASH_SECTOR_4) && (Address >= ADDR_FLASH_SECTOR_3))	// 如果地址小于扇区4且大于等于扇区3
	{
		sector = FLASH_SECTOR_3;	// 扇区3
	}
	else if ((Address < ADDR_FLASH_SECTOR_5) && (Address >= ADDR_FLASH_SECTOR_4))	// 如果地址小于扇区5且大于等于扇区4
	{
		sector = FLASH_SECTOR_4;
	}
	else if ((Address < ADDR_FLASH_SECTOR_6) && (Address >= ADDR_FLASH_SECTOR_5))	// 如果地址小于扇区6且大于等于扇区5
	{
		sector = FLASH_SECTOR_5;	// 扇区5
	}
	else if ((Address < ADDR_FLASH_SECTOR_7) && (Address >= ADDR_FLASH_SECTOR_6))	// 如果地址小于扇区7且大于等于扇区6
	{
		sector = FLASH_SECTOR_6;
	}
	else if ((Address < ADDR_FLASH_SECTOR_8) && (Address >= ADDR_FLASH_SECTOR_7))	// 如果地址小于扇区8且大于等于扇区7
	{
		sector = FLASH_SECTOR_7;
	}
	else if ((Address < ADDR_FLASH_SECTOR_9) && (Address >= ADDR_FLASH_SECTOR_8))
	{
		sector = FLASH_SECTOR_8;
	}
	else if ((Address < ADDR_FLASH_SECTOR_10) && (Address >= ADDR_FLASH_SECTOR_9))
	{
		sector = FLASH_SECTOR_9;
	}
	else if ((Address < ADDR_FLASH_SECTOR_11) && (Address >= ADDR_FLASH_SECTOR_10))
	{
		sector = FLASH_SECTOR_10;
	}
	else /*(Address < FLASH_END_ADDR) && (Address >= ADDR_FLASH_SECTOR_11))*/
	{
		sector = FLASH_SECTOR_11;
	}
	return sector;
}
/**
 * @brief 擦除Flash	
 * @param addr Flash地址
 * @param count 擦除数量
 * @return HAL_StatusTypeDef 
 */
HAL_StatusTypeDef Flash_Erase(uint32_t addr, uint8_t count)
{
	uint32_t SECTORError = 0;
	FLASH_EraseInitTypeDef EraseInitStruct; // 擦除初始化结构体

	EraseInitStruct.TypeErase = TYPEERASE_SECTORS; // 擦除类型
	EraseInitStruct.VoltageRange = VOLTAGE_RANGE_3; // 电压范围
	EraseInitStruct.Sector = GetSector(addr); // 扇区
	EraseInitStruct.NbSectors = 1; // 擦除数量
	if (HAL_FLASHEx_Erase(&EraseInitStruct, &SECTORError) != HAL_OK) // 擦除Flash
		return HAL_ERROR; // 返回错误
	return HAL_OK;
}
/**
 * @brief 写入Flash
 * @param addr Flash地址
 * @param buffer 缓冲区
 * @param length 
 * @return HAL_StatusTypeDef 
 */
HAL_StatusTypeDef Flash_Write(uint32_t addr, uint8_t *buffer, uint32_t length)
{
	uint32_t DATA_32, i;

	HAL_FLASH_Unlock(); // 解锁Flash
	Flash_Erase(addr, 1); // 擦除Flash
	for (i = 0; i < length; i += 4) // 写入Flash
	{
		DATA_32 = *((unsigned long *)&buffer[i]); // 读取数据
		if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, addr + i, DATA_32) != HAL_OK) // 写入Flash
			return HAL_ERROR; // 返回错误
	}
	HAL_FLASH_Lock(); // 锁定Flash
	return HAL_OK; // 返回成功
}
/**
 * @brief 读取Flash
 * @param addr Flash地址
 * @param buffer 缓冲区
 * @param length 长度
 */
void Flash_Read(uint32_t addr, uint8_t *buffer, uint32_t length)
{
	uint16_t i = 0;	

	for (i = 0; i < length; i++)
	{
		buffer[i] = *(__IO uint8_t *)addr;
		addr += 1;
	}
}
/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @param  file: The file name as string.
 * @param  line: The line in file as a number.
 * @retval None
 */
void Error_Handler(void)
{
	/* USER CODE BEGIN Error_Handler_Debug */
	/* User can add his own implementation to report the HAL error return state */
	while (1)
	{
	}
	/* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
	/* USER CODE BEGIN 6 */
	/* User can add his own implementation to report the file name and line number,
	   tex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
	/* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/**
 * @}
 */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
