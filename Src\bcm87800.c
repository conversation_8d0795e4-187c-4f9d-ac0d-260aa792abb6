/**
  ******************************************************************************
  * File Name          : bcm87800.c
  * Description        : This file provides code for the configuration
  *                      of the bcm87800.
  ******************************************************************************
*/

/* Includes ------------------------------------------------------------------*/
#include "bcm87800.h"

#include "string.h"
#include "capi_def.h"
#include "hr_time.h"
#include "access.h"

#include "capi_def.h"
#include "host_diag_util.h"                  /* CMD_SANITY_CHECK() */

#include "common_util.h"
#include "diag_fec_statistics_def.h"

#include "host_power_util.h"
#include "host_log_util.h"
#include "host_chip_wrapper.h"
#include "host_download_util.h"
#include "host_to_chip_ipc.h"
#include "capi.h"
#include "capi_test.h"
#include "capi_diag.h"

#include "master_i2c.h"

#define HIGH 1
#define LOW 0
#define CAPI_PRBS_POLY_SSPRQ 	80
#define CAPI_PRBS_POLY_SQUARE 81
#define CAPI_PRBS_POLY_FIEXD 	83

uint8_t line_tx_chan[] = {1,0,3,2,5,4,7,6};
uint8_t line_rx_chan[] = {0,1,2,3,4,5,6,7};
uint8_t host_tx_chan[] = {1,0,3,2,5,4,7,6};
uint8_t host_rx_chan[] = {0,1,2,3,4,5,6,7};

typedef struct {
    float pre_fec_ber; /**<  pre FEC BER */
    float post_fec_ber; /**<  post FEC BER */
    cw_kp4fec_err_cnt_t cnt; 
} cw_kp4fec_err_state_s;

static float media_dataDate[] = {24.33024e+6,24.8832e+6,25.0e+6,25.781250e+6,26.5625e+6,
																 27.890625e+6,27.952490e+6,28.05e+6,28.125e+6,28.2e+6,28.90e+6,
																 48.66e+6,49.7664e+6,51.5625e+6,53.125e+6,56.0e+6,56.25e+6,64.00e+6};
#define DSP_RESET(die,State) HAL_GPIO_WritePin(RESET_DSP_GPIO_Port,RESET_DSP_Pin,(GPIO_PinState)State);

uint8_t line_poly[] = {CAPI_PRBS_POLY_7, /**< Line PRBS polynomial 7 */
											 CAPI_PRBS_POLY_9,  /**< Line PRBS polynomial 9 */
											 CAPI_PRBS_POLY_10, /**< Line PRBS polynomial 10 */
											 CAPI_PRBS_POLY_11, /**< Line PRBS polynomial 11 */
											 CAPI_PRBS_POLY_13, /**< Line PRBS polynomial 13 */
											 CAPI_PRBS_POLY_15, /**< Line PRBS polynomial 15 */
											 CAPI_PRBS_POLY_20, /**< Line PRBS polynomial 20 */
											 CAPI_PRBS_POLY_23, /**< Line PRBS polynomial 23 */
											 CAPI_PRBS_POLY_31, /**< Line PRBS polynomial 31 */
											 CAPI_PRBS_POLY_49, /**< Line PRBS polynomial 49 */
											 CAPI_PRBS_POLY_58, /**< Line PRBS polynomial 58 */
											 CAPI_PRBS_POLY_SSPRQ,
											 CAPI_PRBS_POLY_SQUARE,
											 CAPI_PRBS_POLY_FIEXD,
};
/* USER CODE BEGIN 1 */

return_result_t set_user_pattern(capi_phy_info_t capi_phy)
{
    int length = 32;

    char pattern[33] = {0};
    uint32_t fixePattern = 0xaaaaaaaa;
    capi_prbs_info_t shared_tx_pattern = {0};
    return_result_t status = RR_SUCCESS;
    
    for(uint32_t i =0;i<32;i++)
    {
        pattern[i] = (fixePattern & 0x80000000)?0x31:0x30;
        fixePattern <<= 1;
    }
		
    shared_tx_pattern.gen_switch = CAPI_SWITCH_ON;
    shared_tx_pattern.ptype = CAPI_PRBS_SHARED_TX_PATTERN_GENERATOR;
    shared_tx_pattern.op.shared_tx_ptrn.length = length;
    util_memcpy((void*) (shared_tx_pattern.op.shared_tx_ptrn.pattern), (void*) pattern, sizeof(pattern) - 1);

    status = capi_set_prbs_info(&capi_phy, &shared_tx_pattern);
    return status;
}


/* ========= HOST =============================================*/
return_result_t set_host_tx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;

	//PPG Config
	uint8_t idx = host_tx_chan[ch];//C4->SignalMode!=2? host_tx_chan[ch]:ch;//= host_tx_chan[ch];
	capi_phy.lane_mask = (1 << idx);
	//Turn ON LINE PRBS GEN
	util_memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
	
	capi_prbs.gen_switch = CAPI_SWITCH_ON;
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	if(line_poly[pattern] == CAPI_PRBS_POLY_SSPRQ)
	{
		capi_prbs.ptype = CAPI_PRBS_SSPRQ_GENERATOR;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	else if(line_poly[pattern] == CAPI_PRBS_POLY_SQUARE)
	{
		capi_prbs.ptype = CAPI_PRBS_SQUARE_WAVE_GENERATOR;
		capi_prbs.gen_switch = CAPI_SWITCH_ON;
		uint8_t swtype = 3; //"\n** Please choose square wave type: 0- 1 consecutive 1s; 1- 2 consecutive 1s;2- 4 consecutive 1s;3- 8 consecutive 1s; 4- 16 consecutive 1s
		capi_prbs.op.sqr_wave.ptrn_wave_type = (square_wave_pattern_t) swtype;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	else if(line_poly[pattern] == CAPI_PRBS_POLY_FIEXD)
	{
		capi_prbs.ptype = CAPI_PRBS_SHARED_TX_PATTERN_GENERATOR;
		status = set_user_pattern(capi_phy);
	}
	else
	{
		capi_prbs.ptype = CAPI_PRBS_GENERATOR;
		capi_prbs.op.pcfg.poly.poly_type = (capi_prbs_poly_type_t)line_poly[pattern];
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	return status;
}

return_result_t set_host_user_pattern(uint16_t ch)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;

	//PPG Config
	uint8_t idx = host_tx_chan[ch];
	capi_phy.lane_mask = (1 << idx);
		
	status = set_user_pattern(capi_phy);
	return status;
}

return_result_t set_host_rx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;

	//PPG Config
	uint8_t idx = host_rx_chan[ch];
	capi_phy.lane_mask = (1 << idx);
	//Turn ON LINE PRBS GEN
	util_memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
	
	capi_prbs.gen_switch = CAPI_SWITCH_ON;
	if(pattern<=10){
		capi_prbs.ptype = CAPI_PRBS_MONITOR;
		capi_prbs.op.pcfg.poly.poly_type = (capi_prbs_poly_type_t)line_poly[pattern];
	}
	else
	{
		capi_prbs.ptype = CAPI_PRBS_SSPRQ_MONITOR;
	}
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	status = capi_set_prbs_info(&capi_phy, &capi_prbs);

	// Configure is done, now do checking....
  // Check LINE first
  if(pattern<=10)
		capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
	else
		capi_prbs_status.prbs_type = CAPI_PRBS_SSPRQ_MONITOR;
  capi_get_prbs_status(&capi_phy, &capi_prbs_status);
	return status;
}

return_result_t set_host_disable_prbs(void)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;
	capi_phy.lane_mask = 0xff;
	
	capi_prbs.ptype = CAPI_PRBS_MONITOR;
	capi_prbs.gen_switch = CAPI_SWITCH_OFF;
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	if(status != RR_SUCCESS) return RR_ERROR;
	
	capi_prbs.ptype = CAPI_PRBS_GENERATOR;
	capi_prbs.gen_switch = CAPI_SWITCH_OFF;
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	if(status != RR_SUCCESS) return RR_ERROR;
	
	return status;
}

return_result_t set_host_tx_emphasis(uint16_t ch)
{
    return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_phy.core_ip = CORE_IP_HOST_DSP;
    capi_lane_config_info_t lane_config_info = {0};

    uint8_t lane = host_tx_chan[ch];//C4->SignalMode!=2? host_tx_chan[ch]:ch;//= host_tx_chan[ch];
    capi_phy.lane_mask = (1 << lane);

    lane_config_info.type.lane_tx_info.param.content = 0x3FF;
    lane_config_info.type.lane_tx_info.param.is.hs_dac_cur = 0;
    lane_config_info.type.lane_tx_info.param.is.tx_bias  = 0;
    lane_config_info.lane_config_type = LANE_CONFIG_TYPE_LANE_TX_INFO;
    return_result = capi_get_lane_config_info(&capi_phy, &lane_config_info);

    lane_config_info.type.lane_tx_info.value.txfir.tap[0] = BER[C4->Interface][ch].line_taps[0];
    lane_config_info.type.lane_tx_info.value.txfir.tap[1] = BER[C4->Interface][ch].line_taps[1];
    lane_config_info.type.lane_tx_info.value.txfir.tap[2] = BER[C4->Interface][ch].line_taps[2];
    lane_config_info.type.lane_tx_info.value.txfir.tap[3] = BER[C4->Interface][ch].line_taps[3];
    lane_config_info.type.lane_tx_info.value.txfir.tap[4] = BER[C4->Interface][ch].line_taps[4];
    lane_config_info.type.lane_tx_info.value.txfir.tap[5] = BER[C4->Interface][ch].line_taps[5];
    lane_config_info.type.lane_tx_info.value.txfir.tap[6] = BER[C4->Interface][ch].line_taps[6];
    lane_config_info.type.lane_tx_info.value.txfir.numb_of_taps = BER[C4->Interface][ch].lane_num_taps == 1?TXFIR_TAPS_7TAP:TXFIR_TAPS_4TAP;
    lane_config_info.type.lane_tx_info.param.is.txfir = 1;

    return_result = capi_set_lane_config_info(&capi_phy, &lane_config_info);
    return return_result;
}

return_result_t set_host_rx_info(uint16_t ch)
{
    return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_phy.core_ip = CORE_IP_HOST_DSP;
    capi_lane_config_info_t lane_config_info = {0}, lane_config_info_rd = {0};

    uint8_t lane = host_rx_chan[ch];
    capi_phy.lane_mask = (1 << lane);

    util_memset(&(lane_config_info.type.lane_rx_info), 0, sizeof(lane_rx_info_t));
    lane_config_info_rd.lane_config_type = LANE_CONFIG_TYPE_LANE_RX_INFO;
    lane_config_info_rd.type.lane_rx_info.param.is.eq2_info = 1;
    return_result = capi_get_lane_config_info(&capi_phy, &lane_config_info_rd);

    lane_config_info.type.lane_rx_info.value.eq2_info = lane_config_info_rd.type.lane_rx_info.value.eq2_info;
    lane_config_info.type.lane_rx_info.param.is.eq2_info = 1;
    lane_config_info.type.lane_rx_info.value.eq2_info.eq2_on = 1;
    if(lane_config_info.type.lane_rx_info.value.eq2_info.eq2_on)
    {
      lane_config_info.type.lane_rx_info.value.eq2_info.dsp_eq2_adapt_off = 0;
      lane_config_info.type.lane_rx_info.value.eq2_info.dsp_eq2_type = 0;
    }
    lane_config_info.type.lane_rx_info.param.is.vga = 1;
    lane_config_info.type.lane_rx_info.value.vga = C4->vga;

    lane_config_info.type.lane_rx_info.param.is.dsp_autopeaking_en = 1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.autopeaking_en = 1;

    lane_config_info.type.lane_rx_info.param.is.peaking_filter_info = 1;
    lane_config_info.type.lane_rx_info.value.peaking_filter_info.value = C4->peaking_filter;

    lane_config_info.type.lane_rx_info.param.is.dsp_eq_tap_sel=1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.eq_tap_sel=1;

    lane_config_info.lane_config_type = LANE_CONFIG_TYPE_LANE_RX_INFO;

    lane_config_info.type.lane_rx_info.param.is.dsp_rx_bw_value = 1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.rx_bw_value = C4->host_rx_bw_value;
    return_result = capi_set_lane_config_info(&capi_phy, &lane_config_info);
    return return_result;
}

return_result_t set_host_tx_squelch(uint16_t ch,bool squelch)
{
	return_result_t return_result = RR_SUCCESS;
	capi_phy_info_t capi_phy = { 0 };
	capi_lane_ctrl_info_t capi_ctrl;
	util_memset(&capi_ctrl, 0, sizeof(capi_lane_ctrl_info_t));

	uint8_t lane = host_tx_chan[ch];//C4->SignalMode!=2? host_tx_chan[ch]:ch;
	capi_phy.core_ip = CORE_IP_HOST_DSP;
	capi_phy.lane_mask = (1 << lane);
	capi_ctrl.param.is.tx_squelch = 1;
	if (squelch)
			capi_ctrl.cmd_value.is.tx_squelch = 1;
	else
			capi_ctrl.cmd_value.is.tx_squelch = 0;

	return_result = capi_set_lane_ctrl_info(&capi_phy, &capi_ctrl);

	return return_result;
}

return_result_t set_host_tx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_polarity_info_t capi_polarity;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	util_memset((void*)&capi_polarity, 0, sizeof(capi_polarity_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;
	
    uint8_t lane = host_tx_chan[ch];//C4->SignalMode!=2? host_tx_chan[ch]:ch;//= ch;
	capi_phy.lane_mask =(1 <<lane);
	
    capi_polarity.direction = DIR_INGRESS;//DIR_EGRESS; //egress
	capi_polarity.action = inverted?1:0;

	if (capi_set_polarity(&capi_phy, &capi_polarity) == RR_SUCCESS)
			status = RR_SUCCESS;
	else
			status = RR_ERROR;

	status = capi_get_polarity(&capi_phy, &capi_polarity);
	return status;
}

return_result_t set_host_rx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_polarity_info_t capi_polarity;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	util_memset((void*)&capi_polarity, 0, sizeof(capi_polarity_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;
	
    uint8_t lane = ch;
	capi_phy.lane_mask =(1 <<lane);
	
    capi_polarity.direction = DIR_EGRESS;//DIR_INGRESS;
	
	capi_polarity.action = inverted?1:0;
	
	status = capi_set_polarity(&capi_phy, &capi_polarity);
	
	status = capi_get_polarity(&capi_phy, &capi_polarity);
	return status;
}

return_result_t get_host_Checker_Status(uint8_t ch)
{
    return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status = {0};
	uint8_t lane = host_rx_chan[ch];
	float data_rate;
	uint64_t prbs_total_bit_count;
	
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;
	capi_phy.lane_mask =(1 <<lane);

	data_rate = media_dataDate[C4->DataRate];
	
	capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
	if (capi_get_prbs_status(&capi_phy, &capi_prbs_status) != RR_SUCCESS) 
	{
		return RR_ERROR;
	}

	BER[C4->Interface][ch].EDLockFlag = capi_prbs_status.lock;
	if (capi_prbs_status.lock) 
	{
		prbs_total_bit_count = Get_Time_Diff(bert_TimeCountStart[C4->Interface][ch])*data_rate;
		
		BER[C4->Interface][ch].EDLockFlag = capi_prbs_status.lock;
		BER[C4->Interface][ch].RealErrCount_lsb += capi_prbs_status.err.ml_err.lsb_err;
		BER[C4->Interface][ch].RealErrCount_msb += capi_prbs_status.err.ml_err.msb_err;
		BER[C4->Interface][ch].RealBitCount += prbs_total_bit_count;
	
		BER[C4->Interface][ch].AccumErrCount_lsb += BER[C4->Interface][ch].RealErrCount_lsb;
		BER[C4->Interface][ch].AccumErrCount_msb += BER[C4->Interface][ch].RealErrCount_msb;
		BER[C4->Interface][ch].AccumBitCount += BER[C4->Interface][ch].RealBitCount;
	}
    return status;
}

return_result_t set_enable_host_checker(uint8_t ch)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_HOST_DSP;

	// checker config
	for(uint8_t n=0;n<2;n++)
	{
		uint8_t idx = host_rx_chan[ch];
		capi_phy.lane_mask = (1 << idx);
		//turn on LINE PRBS MON
		memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
		capi_prbs.gen_switch = CAPI_SWITCH_ON;
		capi_prbs.ptype = CAPI_PRBS_MONITOR;
		capi_prbs.op.pcfg.poly.poly_type =  (capi_prbs_poly_type_t)line_poly[C4->HostRxPattern[ch]];
		capi_prbs.op.pcfg.rx_invert = 0;
		capi_prbs.op.pcfg.tx_invert = 0;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
		// Configure is done, now do checking....
		// Check LINE first
		HAL_Delay(20);
		capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
		status = capi_get_prbs_status(&capi_phy, &capi_prbs_status);
		if(capi_prbs_status.lock) break ;
	}
	return status;
}

/* ============= END HOST ===================================*/

/* ============== LINE ======================================*/
return_result_t set_line_tx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	//PPG Config
	uint8_t idx = line_tx_chan[ch];
	capi_phy.lane_mask = (1 << idx);
	//Turn ON LINE PRBS GEN
	util_memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
	
	capi_prbs.gen_switch = CAPI_SWITCH_ON;
	if(line_poly[pattern] == CAPI_PRBS_POLY_SSPRQ)
	{
		capi_prbs.ptype = CAPI_PRBS_SSPRQ_GENERATOR;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	else if(line_poly[pattern] == CAPI_PRBS_POLY_SQUARE)
	{
		capi_prbs.ptype = CAPI_PRBS_SQUARE_WAVE_GENERATOR;
		capi_prbs.gen_switch = CAPI_SWITCH_ON;
		uint8_t swtype = 3; //"\n** Please choose square wave type: 0- 1 consecutive 1s; 1- 2 consecutive 1s;2- 4 consecutive 1s;3- 8 consecutive 1s; 4- 16 consecutive 1s
		capi_prbs.op.sqr_wave.ptrn_wave_type = (square_wave_pattern_t) swtype;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	else if(line_poly[pattern] == CAPI_PRBS_POLY_FIEXD)
	{
		capi_prbs.ptype = CAPI_PRBS_SHARED_TX_PATTERN_GENERATOR;
		status = set_user_pattern(capi_phy);
	}
	else
	{
		capi_prbs.ptype = CAPI_PRBS_GENERATOR;
		capi_prbs.op.pcfg.poly.poly_type = (capi_prbs_poly_type_t)line_poly[pattern];
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	}
	return status;
}

return_result_t set_line_user_pattern(uint16_t ch)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	//PPG Config
	uint8_t idx = line_tx_chan[ch];
	capi_phy.lane_mask = (1 << idx);
		
	status = set_user_pattern(capi_phy);
	return status;
}

return_result_t set_line_rx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	//checker config
    uint8_t idx = line_rx_chan[ch]; 
    capi_phy.lane_mask = (1 << idx);
    //turn on LINE PRBS MON
    memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
    capi_prbs.gen_switch = CAPI_SWITCH_ON;
	if(pattern<=10){
		capi_prbs.ptype = CAPI_PRBS_MONITOR;
		capi_prbs.op.pcfg.poly.poly_type = (capi_prbs_poly_type_t)line_poly[pattern];
	}
	else
	{
		capi_prbs.ptype = CAPI_PRBS_SSPRQ_MONITOR;
	}
    capi_prbs.op.pcfg.rx_invert = 0;
    capi_prbs.op.pcfg.tx_invert = 0;
    status = capi_set_prbs_info(&capi_phy, &capi_prbs);

    // Configure is done, now do checking....
    // Check LINE first
    if(pattern<=10)
        capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
    else
        capi_prbs_status.prbs_type = CAPI_PRBS_SSPRQ_MONITOR;
    status = capi_get_prbs_status(&capi_phy, &capi_prbs_status);

	return status;
}

return_result_t set_line_disable_prbs(void)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	capi_phy.lane_mask = 0xff;
	
	capi_prbs.ptype = CAPI_PRBS_MONITOR;
	capi_prbs.gen_switch = CAPI_SWITCH_OFF;
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	if(status != RR_SUCCESS) return RR_ERROR;
	
	capi_prbs.ptype = CAPI_PRBS_GENERATOR;
	capi_prbs.gen_switch = CAPI_SWITCH_OFF;
	capi_prbs.op.pcfg.rx_invert = 0;
	capi_prbs.op.pcfg.tx_invert = 0;
	status = capi_set_prbs_info(&capi_phy, &capi_prbs);
	if(status != RR_SUCCESS) return RR_ERROR;
	
	return status;
}


return_result_t set_line_tx_emphasis(uint16_t ch)
{
    return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_phy.core_ip = CORE_IP_MEDIA_DSP;
    capi_phy.lane_mask = 0xF;
    capi_lane_config_info_t lane_config_info = {0};

    uint8_t lane = line_tx_chan[ch];
    capi_phy.lane_mask = (1 << lane);
		
//		lane_config_info.type.lane_tx_info.param.is.symbol_swap = 1;//C4->gain_boost;//
//		lane_config_info.type.lane_tx_info.value.symbol_swap = 1;

    lane_config_info.type.lane_tx_info.param.content = 0x3FF;
    lane_config_info.lane_config_type = LANE_CONFIG_TYPE_LANE_TX_INFO;
    return_result = capi_get_lane_config_info(&capi_phy, &lane_config_info);
    
    lane_config_info.type.lane_tx_info.value.txfir.tap[0] = BER[C4->Interface][ch].line_taps[0];
    lane_config_info.type.lane_tx_info.value.txfir.tap[1] = BER[C4->Interface][ch].line_taps[1];
    lane_config_info.type.lane_tx_info.value.txfir.tap[2] = BER[C4->Interface][ch].line_taps[2];
    lane_config_info.type.lane_tx_info.value.txfir.tap[3] = BER[C4->Interface][ch].line_taps[3];
    lane_config_info.type.lane_tx_info.value.txfir.tap[4] = BER[C4->Interface][ch].line_taps[4];
    lane_config_info.type.lane_tx_info.value.txfir.tap[5] = BER[C4->Interface][ch].line_taps[5];
    lane_config_info.type.lane_tx_info.value.txfir.tap[6] = BER[C4->Interface][ch].line_taps[6];
		
    lane_config_info.type.lane_tx_info.value.txfir.numb_of_taps = BER[C4->Interface][ch].lane_num_taps == 1?TXFIR_TAPS_7TAP:TXFIR_TAPS_4TAP;
    lane_config_info.type.lane_tx_info.param.is.txfir = 1;
		
    return_result = capi_set_lane_config_info(&capi_phy, &lane_config_info);
    return return_result;
}


return_result_t set_line_rx_info(uint16_t ch)
{
    return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_phy.core_ip = CORE_IP_MEDIA_DSP;
    capi_lane_config_info_t lane_config_info = {0}, lane_config_info_rd = {0};

    uint8_t lane = line_rx_chan[ch];
    capi_phy.lane_mask = (1 << lane);
    
    util_memset(&(lane_config_info.type.lane_rx_info), 0, sizeof(lane_rx_info_t));
    lane_config_info_rd.lane_config_type = LANE_CONFIG_TYPE_LANE_RX_INFO;
    lane_config_info_rd.type.lane_rx_info.param.is.eq2_info = 1;
    return_result = capi_get_lane_config_info(&capi_phy, &lane_config_info_rd);
    lane_config_info.type.lane_rx_info.value.eq2_info = lane_config_info_rd.type.lane_rx_info.value.eq2_info;
//		lane_config_info.type.lane_rx_info.param.is.eq2_info = 1;
//		lane_config_info.type.lane_rx_info.value.eq2_info.eq2_on = 1;
//		if(lane_config_info.type.lane_rx_info.value.eq2_info.eq2_on){
//		lane_config_info.type.lane_rx_info.value.eq2_info.dsp_eq2_adapt_off = 0;
//				lane_config_info.type.lane_rx_info.value.eq2_info.dsp_eq2_type = 0;
//		}
    lane_config_info.type.lane_rx_info.param.is.vga = 1;
    lane_config_info.type.lane_rx_info.value.vga = C4->vga;

    lane_config_info.type.lane_rx_info.param.is.peaking_filter_info = 1;
    lane_config_info.type.lane_rx_info.value.peaking_filter_info.value = C4->peaking_filter;

    lane_config_info.type.lane_rx_info.param.is.dsp_gain_boost = 1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.gain_boost = C4->gain_boost;//0;

    lane_config_info.type.lane_rx_info.param.is.dsp_rx_bw_value = 1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.rx_bw_value = C4->lane_rx_bw_value;


    lane_config_info.type.lane_rx_info.param.is.dsp_eq_tap_sel=1;
    lane_config_info.type.lane_rx_info.value.core_ip.dsp.eq_tap_sel=1;
    //swap  msb and lsb
//		lane_config_info.type.lane_rx_info.param.is.symbol_swap = 1;//C4->gain_boost;//		
//		lane_config_info.type.lane_rx_info.value.symbol_swap = 1;

    lane_config_info.lane_config_type = LANE_CONFIG_TYPE_LANE_RX_INFO;
		
    return_result = capi_set_lane_config_info(&capi_phy, &lane_config_info);
    return return_result;
}


return_result_t set_line_tx_squelch(uint16_t ch,bool squelch)
{
	return_result_t return_result = RR_SUCCESS;
	capi_phy_info_t capi_phy = { 0 };
	capi_lane_ctrl_info_t capi_ctrl;
	util_memset(&capi_ctrl, 0, sizeof(capi_lane_ctrl_info_t));

	uint8_t idx = line_tx_chan[ch];
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	capi_phy.lane_mask = (1 << idx);
	capi_ctrl.param.is.tx_squelch = 1;
	if (squelch)
        capi_ctrl.cmd_value.is.tx_squelch = 1;
    else
        capi_ctrl.cmd_value.is.tx_squelch = 0;

	return_result = capi_set_lane_ctrl_info(&capi_phy, &capi_ctrl);

	return return_result;
}

return_result_t set_line_tx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_polarity_info_t capi_polarity;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	util_memset((void*)&capi_polarity, 0, sizeof(capi_polarity_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	
    uint8_t lane = line_tx_chan[ch];
	capi_phy.lane_mask =(1 <<lane);
	
    capi_polarity.direction = DIR_EGRESS; //egress
	capi_polarity.action = inverted?1:0;
	if (capi_set_polarity(&capi_phy, &capi_polarity) == RR_SUCCESS)
			status = RR_SUCCESS;
	else
			status = RR_ERROR;

	status = capi_get_polarity(&capi_phy, &capi_polarity);
	return status;
}

return_result_t set_line_rx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_polarity_info_t capi_polarity;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	util_memset((void*)&capi_polarity, 0, sizeof(capi_polarity_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	
    uint8_t lane = line_rx_chan[ch];
	capi_phy.lane_mask =(1 <<lane);
	
    capi_polarity.direction = DIR_INGRESS;
	capi_polarity.action = inverted?1:0;

	if (capi_set_polarity(&capi_phy, &capi_polarity) == RR_SUCCESS)
        status = RR_SUCCESS;
    else
        status = RR_ERROR;

	status = capi_get_polarity(&capi_phy, &capi_polarity);
	return status;
}


return_result_t get_line_Checker_Status(uint8_t ch)
{
    return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status = {0};
	uint8_t lane = line_rx_chan[ch];
	float data_rate;
	uint64_t prbs_total_bit_count;
	
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	capi_phy.lane_mask =(1 <<lane);

	data_rate = media_dataDate[C4->DataRate];
	
	capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
	if (capi_get_prbs_status(&capi_phy, &capi_prbs_status) != RR_SUCCESS) 
	{
		return RR_ERROR;
	}

	BER[C4->Interface][ch].EDLockFlag = capi_prbs_status.lock;
	if (capi_prbs_status.lock) 
	{
		prbs_total_bit_count = Get_Time_Diff(bert_TimeCountStart[C4->Interface][ch])*data_rate;
		
		BER[C4->Interface][ch].EDLockFlag = capi_prbs_status.lock;
		BER[C4->Interface][ch].RealErrCount_lsb += capi_prbs_status.err.ml_err.lsb_err;
		BER[C4->Interface][ch].RealErrCount_msb += capi_prbs_status.err.ml_err.msb_err;
		BER[C4->Interface][ch].RealBitCount += prbs_total_bit_count;
	
		BER[C4->Interface][ch].AccumErrCount_lsb += BER[C4->Interface][ch].RealErrCount_lsb;
		BER[C4->Interface][ch].AccumErrCount_msb += BER[C4->Interface][ch].RealErrCount_msb;
		BER[C4->Interface][ch].AccumBitCount += BER[C4->Interface][ch].RealBitCount;
		
		//berp->prbs_ber = (float)((berp->prbs_err_LSB+berp->prbs_err_MSB) / (data_rate) /(1000000000.) / (i*interval_ms/1000.));
	}
    return status;
}


return_result_t set_enable_line_checker(uint8_t ch)
{
	return_result_t status = RR_SUCCESS;
	capi_prbs_info_t capi_prbs;
	capi_phy_info_t capi_phy;
	capi_prbs_status_t capi_prbs_status;

	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	for(uint8_t n=0;n<2;n++)
	{
		// checker config
		uint8_t idx = line_rx_chan[ch];
		capi_phy.lane_mask = (1 << idx);
		//turn on LINE PRBS MON
		memset((void*)&capi_prbs, 0, sizeof(capi_prbs_info_t));
		capi_prbs.ptype = CAPI_PRBS_MONITOR;

		capi_prbs.op.pcfg.poly.poly_type = (capi_prbs_poly_type_t)line_poly[C4->LaneRxPattern[ch]];
		capi_prbs.op.pcfg.rx_invert = 0;
		capi_prbs.op.pcfg.tx_invert = 0;
		capi_prbs.gen_switch = CAPI_SWITCH_ON;
		status = capi_set_prbs_info(&capi_phy, &capi_prbs);
		// Configure is done, now do checking....
		// Check LINE first
		HAL_Delay(20);
		capi_prbs_status.prbs_type = CAPI_PRBS_MONITOR;
		status = capi_get_prbs_status(&capi_phy, &capi_prbs_status);
		if(capi_prbs_status.lock) break;
	}
	return status;
}

/* ==================== END LINE ====================*/
void chip_internal_dvdd(void) 
{    
	capi_phy_info_t capi_phy;
	return_result_t ret;
	capi_fixed_voltage_config_info_t capi_fixed_voltage_config;
	uint32_t voltage = 650;
	uint32_t fail_cnt =0;

	memset(&capi_fixed_voltage_config, 0, sizeof(capi_fixed_voltage_config_info_t));
	capi_phy.phy_id = 0;

	capi_fixed_voltage_config.type_of_regulator = CAPI_REGULATOR_INTERNAL;
	/* first */
	capi_fixed_voltage_config.fixed_voltage =voltage; /* mv */
	capi_fixed_voltage_config.fixed_voltage_enable = CAPI_ENABLE;
	capi_fixed_voltage_config.capi_set_voltage_with_fw = CAPI_SET_VOLTAGE_NO_FIRMWARE;
	ret = capi_set_voltage_config(&capi_phy, &capi_fixed_voltage_config);
	if(ret!=RR_SUCCESS)
	{
		fail_cnt++; 
	}
	
}

void chip_internal_vddm_0p75(void) 
{    

	capi_phy_info_t capi_phy;
    uint32_t fail_cnt = 0;
    return_result_t ret;
    uint32_t voltage = 780;

    capi_internal_regulator_voltage_t int_reg_voltage;

    memset(&int_reg_voltage, 0, sizeof(capi_internal_regulator_voltage_t));
    capi_phy.phy_id = 0;

    int_reg_voltage.int_reg_component_type=CAPI_INTERNAL_REGULATOR_COMPONENT_VDDM;
    int_reg_voltage.while_fw_running=CAPI_YES;
    int_reg_voltage.voltage = voltage; /* mv */

    ret = capi_set_internal_regulator_voltage(&capi_phy,&int_reg_voltage);

    if((ret!=RR_SUCCESS))
    {
        fail_cnt++; 
    }
}


void chip_internal_avdd_0p9(void) 
{
	capi_phy_info_t capi_phy;
    uint32_t fail_cnt = 0;
    return_result_t ret;
    uint32_t voltage = 940;
    uint32_t sync_with_firmware = 0;
    capi_internal_regulator_voltage_t int_reg_voltage;
   
    memset(&int_reg_voltage, 0, sizeof(capi_internal_regulator_voltage_t));
    capi_phy.phy_id = 0;

    int_reg_voltage.int_reg_component_type=CAPI_INTERNAL_REGULATOR_COMPONENT_AVDD;
    int_reg_voltage.while_fw_running=sync_with_firmware ? CAPI_YES: CAPI_NO;
    int_reg_voltage.voltage = voltage; /* mv */

    ret = capi_set_internal_regulator_voltage(&capi_phy,&int_reg_voltage);

    if((ret!=RR_SUCCESS))
    {
        fail_cnt++; 
    }
}

return_result_t test_fec_pgen_seq (capi_phy_info_t* phy_info_ptr, capi_prbs_info_t* prbs_info_ptr,capi_switch_t option) 
{
	return_result_t status = RR_SUCCESS;

	/*1  FEC prbs config set */
	prbs_info_ptr->gen_switch =  option;
	if(prbs_info_ptr->gen_switch)
	{
	#if 1
			prbs_info_ptr->op.pcfg.poly.fec_pgen_poly = CAPI_FEC_PRBS_GEN_POLY_58;
			prbs_info_ptr->op.pcfg.tx_invert = 0;
	#endif
	}
	uint16_t lane_mask = phy_info_ptr->lane_mask;
	uint8_t lane_id;
	for (lane_id = 0; lane_id < 8; lane_id++) 
	{
		if (lane_mask & (1 << lane_id)) 
		{
			//dprintf("\n*** Test FEC PRBS monitor lane %d \r\n", lane_id);

			phy_info_ptr->lane_mask = (1 << lane_id);

			if (capi_set_prbs_info(phy_info_ptr, prbs_info_ptr) != RR_SUCCESS) 
			{
				//dprintf("\n*** capi_set_prbs_info: FAILED \r\n");
				return RR_ERROR;
			} 
			
			uint16_t ptype = prbs_info_ptr->ptype;
			util_memset((void *)prbs_info_ptr, 0, sizeof(capi_prbs_info_t));
			prbs_info_ptr->ptype = ptype;
			if (capi_get_prbs_info(phy_info_ptr, prbs_info_ptr) != RR_SUCCESS) 
			{
					//dprintf("\n*** capi_get_prbs_info: FAILED \r\n");
				return RR_ERROR;
			} 
			else 
			{
					//dprintf("\n*** capi_get_prbs_info: PASSED \r\n");
			}
		}
	}
	return status;
}


/**
 * The following example describes the process of configuring
 * the ASIC to generate and check PRBS on the line side interface.
 *
 * NOTE: This feature does not yet work!
 *
 * @return RR_SUCCESS on success, INPHI_ERROR on failure
 */
return_result_t example_line_prbs(uint16_t setvcc)
{
    return_result_t status = RR_SUCCESS;
	uint32_t chip_id_lsb ,chip_id_msb;
    capi_phy_info_t capi_phy;
	capi_reg_info_t reg_info;
	uint16_t n=0;
	capi_status_info_t status_info;
//	int line_taps[] = {0,-10,130,-15,0,0,0};
	int host_Taps[] = {0,-30,130,-0,0,0,0};
	
	
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_ALL;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	
	capi_reset_mode_t reset_mode = CAPI_HARD_RESET_MODE;//CAPI_SOFT_RESET_MODE;//
	status = capi_reset(&capi_phy, &reset_mode);
	HAL_Delay(200);
	
	do
	{
		status = capi_get_firmware_status(&capi_phy, &status_info);
		if(status==RR_SUCCESS) break;  //||(status==RR_ERROR_CRC32_MISMATCH)
		HAL_Delay(100);
		n++;
	}while(n<10);

	reg_info.reg_address = 0x5201D000;
	capi_read_register(&capi_phy, &reg_info);
	chip_id_lsb = reg_info.content;
	reg_info.reg_address = 0x5201D004;
	capi_read_register(&capi_phy, &reg_info);
	chip_id_msb = reg_info.reg_address;
	
	if(setvcc)
	{
		chip_internal_vddm_0p75();
		chip_internal_avdd_0p9();
	}
	
    //Wait_AVS_Ready();	
    capi_config_info_t chip_mode_nrz_cfg[]={

        //4.1 -- CHIP_MODE_1x25G_KR4NRZ_TO_1x25G_KR4NRZ:Repeater Bit_Mux
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CHIP_MODE_25G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_25_78125, CAPI_LW_BR_25_78125,
        {CAPI_MODULATION_NRZ,  0xff}, {CAPI_MODULATION_NRZ,  0xff}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},
            
        //5.1 -- CHIP_MODE_1X26G_KP4NRZ_TO_1X26G_KP4NRZ:Repeater Bit_Mux",
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CHIP_MODE_25G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_26_5625, CAPI_LW_BR_26_5625,
        {CAPI_MODULATION_NRZ,  0xff}, {CAPI_MODULATION_NRZ,  0xff}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},

        //29.1 -- CHIP_MODE_1X53G_NRZ_TO_1X53G_NRZ:Repeater Bit_Mux
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CAPI_MODE_50G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_53_125, CAPI_LW_BR_53_125,
        {CAPI_MODULATION_NRZ,  0xff}, {CAPI_MODULATION_NRZ,  0xff}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},
    };
    capi_config_info_t chip_mode_pam_cfg[]={
        //3.1 -- CHIP_MODE_1X51G_KR4PAM_TO_1X51G_KR4PAM:Repeater Bit_Mux
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CAPI_MODE_50G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_51_565, CAPI_LW_BR_51_565,
        {CAPI_MODULATION_PAM4,  0xFF}, {CAPI_MODULATION_PAM4,  0xFF}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},
         
        //2.1 -- CHIP_MODE_1X53G_KP4PAM_TO_1X53G_KP4PAM:Repeater Bit_Mux
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CAPI_MODE_50G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_53_125, CAPI_LW_BR_53_125,
        {CAPI_MODULATION_PAM4,  0xFF}, {CAPI_MODULATION_PAM4,  0xFF}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},
        
        //1.1 -- CHIP_MODE_1X106G_KP4PAM_TO_1X106G_KP4PAM:Repeater Bit_Mux
        {CAPI_REF_CLK_FRQ_156_25_MHZ_ETHERNET, CAPI_MODE_100G, CAPI_LANE_MUX_BIT_MUX, CAPI_LANE_FEC_TERM_BYPASS, CAPI_LINE_FEC_TYPE_NA, CAPI_HOST_FEC_TYPE_NA, CAPI_LW_BR_106_25, CAPI_LW_BR_106_25,
        {CAPI_MODULATION_PAM4,  0xFF}, {CAPI_MODULATION_PAM4,  0xFF}, CAPI_PORT_CONFIG_STATUS_SUCCESS, CAPI_PORT_POWER_DOWN_STATUS_POWER_UP},
    };
	
	if(C4->SignalMode>=CAPI_MODULATION_PAM4)
	{
		if(C4->DataRate<=3)
		{
			status = capi_set_config(&capi_phy, &chip_mode_pam_cfg[0]);
		}
		else if(C4->DataRate>3 && C4->DataRate<=10)
		{
			status = capi_set_config(&capi_phy, &chip_mode_pam_cfg[1]);
		}
		else if(C4->DataRate>10)
		{
			status = capi_set_config(&capi_phy, &chip_mode_pam_cfg[2]);
		}
	}
	else
	{
		C4->hostline_Taps[1] = 0; C4->hostline_Taps[3] = -10;
		host_Taps[1] = 0; host_Taps[3] = -10;
		if(C4->DataRate<=3)
		{
			status = capi_set_config(&capi_phy, &chip_mode_nrz_cfg[0]);
		}
		else if(C4->DataRate>3 && C4->DataRate<=10)
		{
			status = capi_set_config(&capi_phy, &chip_mode_nrz_cfg[1]);
		}
		else if(C4->DataRate>10)
		{
			status = capi_set_config(&capi_phy, &chip_mode_nrz_cfg[2]);
			C4->hostline_Taps[1] = -10; C4->hostline_Taps[3] = -10; C4->hostline_Taps[4] = -5;
			host_Taps[1] = -10; host_Taps[3] = -10; host_Taps[4] = -5;
		}
	}
	for(uint8_t i=0;i<8;i++)
	{
		for(uint16_t j=0;j<7;j++)
		{
			BER[0][i].line_taps[j] = C4->hostline_Taps[j];
			BER[1][i].line_taps[j] = host_Taps[j];
		}
	}
    if(status==RR_SUCCESS)
	{
		for(uint8_t lane = 0;lane<8;lane++)
		{
			if(C4->Interface==0)
			{
				set_line_tx_prbs(lane,C4->LaneTxPattern[lane]);
				set_line_tx_polarity(lane,(C4->TxInvert[lane]==0)?true:false);//C4->TxInvert[lane]);
				set_line_rx_prbs(lane,C4->LaneRxPattern[lane]);
				set_line_rx_polarity(lane,C4->RxInvert[lane]==0?true:false);//C4->RxInvert[lane]);
				set_line_tx_emphasis(lane);
				set_line_rx_info(lane);
				set_host_user_pattern(lane);
			}
			else
			{				
				set_host_tx_prbs(lane,C4->HostTxPattern[lane]);
				set_host_tx_polarity(lane,C4->TxInvert[lane]);
				set_host_rx_prbs(lane,C4->HostRxPattern[lane]);
				set_host_rx_polarity(lane,C4->RxInvert[lane]);
				set_host_tx_emphasis(lane);
				set_host_rx_info(lane);
				set_line_user_pattern(lane);
			}
			bcm87800_set_fec_init(lane,0);
		}

		if(C4->SignalMode==2)
		{
			bcm87800_disable_prbs();
			bcm87800_set_fec_pgen(1);
			for(uint8_t n = 0;n<8;n++)
			{
				bcm87800_set_fec_init(n,1);
				bcm87800_set_fec_clear(n);
			}
		}
		bcm87800_clock_divider_vco();
		bcm87800_clock_divider2();
	}
	return status;
}

bool bcm87800_download_firmware(void)
{
	bool ok = true;

	DSP_RESET(0,HIGH);
	HAL_Delay(1500);
	
	/* USER CODE BEGIN 2 */
	return ok;
}

bool bcm87800_disable_prbs(void)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_disable_prbs();
	else
		status = set_host_disable_prbs();
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_init_prbs(uint16_t setvcc)
{
	return_result_t status = RR_SUCCESS;
	uint16_t div = 0;
	float clkref = 0,data_rate;

	data_rate = media_dataDate[C4->DataRate]/1000.0;
	if(C4->DataRate<=3)
	{
		div = 165;
		if(C4->SignalMode >= CAPI_MODULATION_PAM4) div =330;
	}
	else if(C4->DataRate>3 && C4->DataRate<=10)
	{
		div = 170;
		if(C4->SignalMode >= CAPI_MODULATION_PAM4) div =340;
	}
	else if(C4->DataRate>10 && C4->DataRate<=13 )
	{
		div = 330;
	}
	else if(C4->DataRate>13)
	{
		div = 340;
	}
	
	clkref = data_rate/div;
	SetOutputRefClock(C4->DataRate);
	HAL_Delay(500);
	status = example_line_prbs(setvcc);
	if(status == RR_SUCCESS) return true;
	else return false;
}

bool bcm87800_clock_divider(void)
{
	return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_test_command_info_t test_command_info;
	capi_recovered_clock_info_t  *rclock_ptr;

	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	rclock_ptr = &test_command_info.type.recovered_clock_info;
	util_memset((void *)rclock_ptr, 0, sizeof(capi_recovered_clock_info_t));
	test_command_info.command_id = COMMAND_ID_GET_RECOVERED_CLOCK_INFO;
	rclock_ptr->command.is.config_analog_pin_0 = 1;
	capi_phy.lane_mask = 0x80;
	return_result = capi_test_set_command(&capi_phy, &test_command_info);

	util_memset((void *)rclock_ptr, 0, sizeof(capi_recovered_clock_info_t));
	rclock_ptr->command.is.config_analog_pin_0 = 1;
	rclock_ptr->value.config_analog_pin[0].enable    = 1;
	test_command_info.command_id = COMMAND_ID_SET_RECOVERED_CLOCK_INFO;

	if(rclock_ptr->value.config_analog_pin[0].enable){
			rclock_ptr->value.config_analog_pin[0].rclk_type = 0;
			rclock_ptr->value.config_analog_pin[0].div_ratio = C4->ClkDivide;
	}
    capi_phy.core_ip = CORE_IP_MEDIA_DSP;
    capi_phy.lane_mask = 0x80;   // support receoverd clock on Lane 3 only
    return_result = capi_test_set_command(&capi_phy, &test_command_info);
	
	return (return_result == RR_SUCCESS)? true:false;
}

bool bcm87800_clock_divider_vco(void)
{
	return_result_t return_result = RR_SUCCESS;
    capi_phy_info_t capi_phy;
    capi_test_command_info_t test_command_info;
	capi_recovered_clock_info_t  *rclock_ptr;

	capi_phy.core_ip = CORE_IP_MEDIA_DSP;

	rclock_ptr = &test_command_info.type.recovered_clock_info;
	util_memset((void *)rclock_ptr, 0, sizeof(capi_recovered_clock_info_t));
	test_command_info.command_id = COMMAND_ID_GET_RECOVERED_CLOCK_INFO;
	rclock_ptr->command.is.config_vco_pin = 1;
	return_result = capi_test_set_command(&capi_phy, &test_command_info);

	util_memset((void *)rclock_ptr, 0, sizeof(capi_recovered_clock_info_t));
	rclock_ptr->command.is.config_vco_pin = 1;
	rclock_ptr->value.config_vco_pin.enable = 1;
	test_command_info.command_id = COMMAND_ID_SET_RECOVERED_CLOCK_INFO;
	capi_phy.core_ip = CORE_IP_MEDIA_DSP;
	return_result = capi_test_set_command(&capi_phy, &test_command_info);

	util_memset((void *)rclock_ptr, 0, sizeof(capi_recovered_clock_info_t));
	test_command_info.command_id = COMMAND_ID_GET_RECOVERED_CLOCK_INFO;
	rclock_ptr->command.is.config_vco_pin = 1;
	return_result = capi_test_set_command(&capi_phy, &test_command_info);

	return (return_result == RR_SUCCESS)? true:false;
}

bool bcm87800_clock_divider2(void)
{
	return_result_t status = RR_SUCCESS;
	capi_phy_info_t capi_phy;
	int length = 32;
	
	memset((void*)&capi_phy, 0, sizeof(capi_phy_info_t));
	capi_phy.phy_id = 0;
	capi_phy.core_ip = C4->Interface==0?CORE_IP_HOST_DSP:CORE_IP_MEDIA_DSP;
	//PPG Config
	capi_phy.lane_mask = 0xff;
    
	char pattern[33] = {0};
	
	uint32_t fixePattern = 0xaaaaaaaa;
	switch(C4->ClkDivide)
	{
		case 0:
			fixePattern = 0xAAAAAAAA;
			break;
		case 1:
			fixePattern = 0xCCCCCCCC;
			break;
		case 2:
			fixePattern = 0xF0F0F0F0;
			break;
		case 3:
			fixePattern = 0xFF00FF00;
			break;
		case 4:
			fixePattern = 0xFFFF0000;
			break;
	  case 5:
			fixePattern = 0xFFFFFFFF;
			break;
	}
	
	capi_prbs_info_t shared_tx_pattern = {0};
	
	for(uint32_t i =0;i<32;i++)
	{
		pattern[i] = (fixePattern & 0x80000000)?0x31:0x30;
		fixePattern <<= 1;
	}
	
	shared_tx_pattern.gen_switch = CAPI_SWITCH_ON;
	shared_tx_pattern.ptype = CAPI_PRBS_SHARED_TX_PATTERN_GENERATOR;
	shared_tx_pattern.op.shared_tx_ptrn.length = length;
	util_memcpy((void*) (shared_tx_pattern.op.shared_tx_ptrn.pattern), (void*) pattern, sizeof(pattern) - 1);

	status = capi_set_prbs_info(&capi_phy, &shared_tx_pattern);
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_tx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_tx_prbs(ch,pattern);
	else
		status = set_host_tx_prbs(ch,pattern);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_rx_prbs(uint16_t ch,uint16_t pattern)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_rx_prbs(ch,pattern);
	else
		status = set_host_rx_prbs(ch,pattern);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_tx_emphasis(uint16_t ch)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_tx_emphasis(ch);
	else
		status = set_host_tx_emphasis(ch);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_tx_squelch(uint16_t ch,bool squelch)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_tx_squelch(ch,squelch);
	else
		status = set_host_tx_squelch(ch,squelch);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_tx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_tx_polarity(ch,inverted);
	else
		status = set_host_tx_polarity(ch,inverted);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_rx_polarity(uint8_t ch,bool inverted)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_line_rx_polarity(ch,inverted);
	else
		status = set_host_rx_polarity(ch,inverted);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_enable_checker(uint8_t ch)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = set_enable_line_checker(ch);
	else
		status = set_enable_host_checker(ch);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_Checker_Status(uint8_t ch)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
		status = get_line_Checker_Status(ch);
	else
		status = get_host_Checker_Status(ch);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_set_rx_info(uint8_t ch)
{
	return_result_t status = RR_SUCCESS;
	if(C4->Interface==0)
	{
		status = set_line_rx_info(ch);
	}
	else
		status = set_host_rx_info(ch);
	
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_set_fec_pgen(uint8_t option)
{
	capi_phy_info_t capi_phy;
	capi_prbs_info_t capi_prbs;
	uint16_t fec_lane_mask = 0xff;
	
	capi_phy.phy_id = 0;
    capi_phy.core_ip = CORE_IP_CW;

	//line fec pg on/off;
	capi_phy.lane_mask = fec_lane_mask;
	util_memset((void *)&capi_prbs, 0, sizeof(capi_prbs_info_t));
	capi_prbs.ptype =(C4->Interface==0)?CAPI_PRBS_KP4_MEDIA_GENERATOR:CAPI_PRBS_KP4_HOST_GENERATOR;

	test_fec_pgen_seq(&capi_phy, &capi_prbs,(capi_switch_t)option); //CAPI_SWITCH_ON : CAPI_SWITCH_OFF;
	
	return true;
}

bool bcm87800_set_fec_init(uint16_t ch,boolean enable)
{
	capi_phy_info_t capi_phy;
	uint8_t fec_mon_sampling_rate;
	static capi_fec_dump_status_t capi_fec_dump_status;
	return_result_t status = RR_SUCCESS;
	
	/*Fill in the CAPI PHY information*/
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_CW;
	
	if(enable) fec_mon_sampling_rate = 50;
	else fec_mon_sampling_rate = 0;
                
	util_memset(&capi_fec_dump_status, 0, sizeof(capi_fec_dump_status_t));
	capi_fec_dump_status.fec_mode = (C4->Interface==0)? CAPI_FEC_LINE:CAPI_FEC_CLIENT;
	capi_fec_dump_status.sampling_rate = fec_mon_sampling_rate;

	uint8_t lane = (C4->Interface==0)? line_rx_chan[ch]:host_rx_chan[ch];
	capi_phy.lane_mask = (1 << lane);
	//dprintf("\n  *** Start client side FEC MON lane %d \r\n", lane_id);
	status = capi_init_fec_mon(&capi_phy, &capi_fec_dump_status,enable);

	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_set_fec_clear(uint16_t ch)
{
	capi_phy_info_t capi_phy;
	uint16_t fec_lane_mask = 0xFF;

	static capi_fec_dump_status_t capi_fec_dump_status;
	return_result_t status = RR_SUCCESS;
	
	/*Fill in the CAPI PHY information*/
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_CW;
	uint8_t lane = (C4->Interface==0)? line_rx_chan[ch]:host_rx_chan[ch];
    fec_lane_mask = (1 << lane);
	
	util_memset(&capi_fec_dump_status, 0, sizeof(capi_fec_dump_status_t));
	capi_fec_dump_status.fec_mode = (C4->Interface==0)? CAPI_FEC_LINE:CAPI_FEC_CLIENT;

	capi_phy.lane_mask = fec_lane_mask;
	//dprintf("\n  *** Clear client side FEC MON lane %d \r\n", lane_id);
	status = capi_clear_fec_mon(&capi_phy, &capi_fec_dump_status);
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_set_fec_clear_all(void)
{
	capi_phy_info_t capi_phy;

	static capi_fec_dump_status_t capi_fec_dump_status;
	return_result_t status = RR_SUCCESS;
	
	/*Fill in the CAPI PHY information*/
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_CW;

	util_memset(&capi_fec_dump_status, 0, sizeof(capi_fec_dump_status_t));
	capi_fec_dump_status.fec_mode = (C4->Interface==0)? CAPI_FEC_LINE:CAPI_FEC_CLIENT;
	for (uint16_t lane_id = 0; lane_id < 8; lane_id++) 
	{
		capi_phy.lane_mask = (1 << lane_id);
		//dprintf("\n  *** Clear client side FEC MON lane %d \r\n", lane_id);
		if (capi_clear_fec_mon(&capi_phy, &capi_fec_dump_status) != RR_SUCCESS)
		{
			//dprintf("\n  *** capi_clear_fec_mon call failed. \r\n");
			return false;
		}
	}
	return (status == RR_SUCCESS)? true:false;
}

bool bcm87800_set_fec_status(uint8_t ch)
{
	capi_phy_info_t capi_phy;

	static capi_fec_dump_status_t capi_fec_dump_status;
	return_result_t status = RR_SUCCESS;
	uint32_t error = 0;
	/*Fill in the CAPI PHY information*/
	capi_phy.phy_id = 0;
	capi_phy.core_ip = CORE_IP_CW;
	
	capi_fec_dump_status_t capi_fec_dump_status_bk={0};
               
	util_memset(&capi_fec_dump_status, 0, sizeof(capi_fec_dump_status_t));
	capi_fec_dump_status.fec_mode = (C4->Interface==0)? CAPI_FEC_LINE:CAPI_FEC_CLIENT;

	uint8_t lane = (C4->Interface==0)? line_rx_chan[ch]:host_rx_chan[ch];
	capi_phy.lane_mask = (1 << lane);
	if (capi_get_fec_info(&capi_phy, &capi_fec_dump_status) != RR_SUCCESS)
	{
		status = RR_ERROR;
		//dprintf("\n  *** capi_get_fec_info call failed. \r\n");
	}
	BER[C4->Interface][ch].fec_lock_flag = capi_fec_dump_status.fec_sts.fec_algn_stat;
	BER[C4->Interface][ch].pre_fec_ber = capi_fec_dump_status.fec_ber.pre_fec_ber;
	BER[C4->Interface][ch].post_fec_ber = capi_fec_dump_status.fec_ber.post_fec_ber;
	BER[C4->Interface][ch].tot_frame_rev_cnt = capi_fec_dump_status.fec_a_err_cnt.tot_frame_rev_cnt;
	BER[C4->Interface][ch].tot_frame_corr_cnt = capi_fec_dump_status.fec_a_err_cnt.tot_frame_corr_cnt;
	BER[C4->Interface][ch].tot_frame_uncorr_cnt = capi_fec_dump_status.fec_a_err_cnt.tot_frame_uncorr_cnt;
	BER[C4->Interface][ch].tot_symbols_corr_cnt = capi_fec_dump_status.fec_a_err_cnt.tot_symbols_corr_cnt;
	
	BER[C4->Interface][ch].fec_lock_flag = capi_fec_dump_status.fec_sts.fec_algn_lol_sticky;
	BER[C4->Interface][ch].fec_lock_flag &= capi_fec_dump_status.fec_a_err_cnt.tot_frame_rev_cnt>0?1:0;

	//test_fec_status_dump(&capi_fec_dump_status);    /* display fec status */
	if(capi_fec_dump_status_bk.fec_a_err_cnt.tot_frame_rev_cnt > capi_fec_dump_status.fec_a_err_cnt.tot_frame_rev_cnt ||
        capi_fec_dump_status_bk.fec_a_err_cnt.tot_frame_corr_cnt > capi_fec_dump_status.fec_a_err_cnt.tot_frame_corr_cnt ||
        capi_fec_dump_status_bk.fec_a_err_cnt.tot_frame_uncorr_cnt > capi_fec_dump_status.fec_a_err_cnt.tot_frame_uncorr_cnt ||
        capi_fec_dump_status_bk.fec_a_err_cnt.tot_symbols_corr_cnt > capi_fec_dump_status.fec_a_err_cnt.tot_symbols_corr_cnt ||
        capi_fec_dump_status_bk.fec_b_err_cnt.tot_frame_rev_cnt > capi_fec_dump_status.fec_b_err_cnt.tot_frame_rev_cnt ||
        capi_fec_dump_status_bk.fec_b_err_cnt.tot_frame_corr_cnt > capi_fec_dump_status.fec_b_err_cnt.tot_frame_corr_cnt ||
        capi_fec_dump_status_bk.fec_b_err_cnt.tot_frame_uncorr_cnt > capi_fec_dump_status.fec_b_err_cnt.tot_frame_uncorr_cnt ||
        capi_fec_dump_status_bk.fec_b_err_cnt.tot_symbols_corr_cnt > capi_fec_dump_status.fec_b_err_cnt.tot_symbols_corr_cnt)
        //dprintf("\n ********** FAIL with frame counter \r\n");
	for(uint16_t sidx=0; sidx<FEC_TOT_BITS_CORR_NUM; sidx++)
	{
        if(capi_fec_dump_status_bk.fec_a_err_cnt.tot_bits_corr_cnt[sidx] > capi_fec_dump_status.fec_a_err_cnt.tot_bits_corr_cnt[sidx] ||
                capi_fec_dump_status_bk.fec_b_err_cnt.tot_bits_corr_cnt[sidx] > capi_fec_dump_status.fec_b_err_cnt.tot_bits_corr_cnt[sidx])
        //dprintf("\n ********** FAIL with tot_bits_corr_cnt %d counter \r\n", sidx);
        error++;
	}
	for(uint16_t sidx=0; sidx<FEC_TOT_FRAMES_ERR_NUM; sidx++){
		BER[C4->Interface][ch].tot_frames_err_cnt[sidx] = capi_fec_dump_status.fec_a_err_cnt.tot_frames_err_cnt[sidx];
        if(capi_fec_dump_status_bk.fec_a_err_cnt.tot_frames_err_cnt[sidx] > capi_fec_dump_status.fec_a_err_cnt.tot_frames_err_cnt[sidx] ||
                capi_fec_dump_status_bk.fec_b_err_cnt.tot_frames_err_cnt[sidx] > capi_fec_dump_status.fec_b_err_cnt.tot_frames_err_cnt[sidx])
        //dprintf("\n ********** FAIL with tot_frames_err_cnt %d counter \r\n", sidx);
        error++;
	}
	util_memcpy((void *)&capi_fec_dump_status_bk, (void *)&capi_fec_dump_status, sizeof(capi_fec_dump_status_t));
	return status;
}

bool bcm87800_set_fec_enabled(bool enabled)
{
    if(!enabled)
    {
        for(uint8_t lane = 0;lane<8;lane++)
        {
            set_line_tx_prbs(lane,C4->LaneTxPattern[lane]);
            set_line_tx_polarity(lane,(C4->TxInvert[lane]==0)?true:false);//C4->TxInvert[lane]);
            set_line_rx_prbs(lane,C4->LaneRxPattern[lane]);
            set_line_rx_polarity(lane,C4->RxInvert[lane]==0?true:false);//C4->LaneRxPattern[lane]);
            set_line_tx_emphasis(lane);
            set_line_rx_info(lane);
            
            bcm87800_set_fec_init(lane,0);
        }
    }
    else
    {
        bcm87800_disable_prbs();
        bcm87800_set_fec_pgen(1);
        for(uint8_t n = 0;n<8;n++)
        {
            bcm87800_set_fec_init(n,1);
            bcm87800_set_fec_clear(n);
        }
    }
    return true;
}
/* USER CODE END 1 */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

