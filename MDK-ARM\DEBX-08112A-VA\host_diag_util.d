.\debx-08112a-va\host_diag_util.o: ..\api\capi\src\host_diag_util.c
.\debx-08112a-va\host_diag_util.o: D:\stm32\ARM\ARMCC\Bin\..\include\stdio.h
.\debx-08112a-va\host_diag_util.o: D:\stm32\ARM\ARMCC\Bin\..\include\stdlib.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\access.h
.\debx-08112a-va\host_diag_util.o: ..\api\platform\reg_access\inc\reg_access.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\type_defns.h
.\debx-08112a-va\host_diag_util.o: ..\api\platform\utils\inc\hr_time.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\common_def.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\dep\common\inc\chip_mode_def.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\chip_config_def.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\capi_def.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\capi_test_def.h
.\debx-08112a-va\host_diag_util.o: ..\api\capi\inc\host_diag_util.h
.\debx-08112a-va\host_diag_util.o: ..\api\capi\inc\host_log_util.h
.\debx-08112a-va\host_diag_util.o: ..\api\capi\inc\host_chip_wrapper.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\common_util.h
.\debx-08112a-va\host_diag_util.o: ..\api\capi\inc\hw_mutex_handler.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\indep\inc\chip_common_config_ind.h
.\debx-08112a-va\host_diag_util.o: ..\api\capi\inc\host_to_chip_ipc.h
.\debx-08112a-va\host_diag_util.o: ..\api\chip\dep\common\inc\dsp_utils.h
