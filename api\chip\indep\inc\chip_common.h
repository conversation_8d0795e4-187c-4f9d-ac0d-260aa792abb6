/****************************************************************************
*
*     Copyright (c) 2016 Broadcom Limited
*           All Rights Reserved
*
*     No portions of this material may be reproduced in any form without the
*     written permission of:
*
*           Broadcom Limited 
*           1320 Ridder Park Dr.
*        	San Jose, California 95131
*        	United States
*
*     All information contained in this document is Broadcom Limited 
*     company private, proprietary, and trade secret.
*
****************************************************************************/

/**
 *       Centenario chip common header file
 *
 * @file chip_common.h
 * <AUTHOR>
 * @date 12-8-2016
 * @brief This file includes Centenario chip level common header
 */

#ifndef CHIP_COMMON_H
#define CHIP_COMMON_H

#include "chip_common_regs_dep.h"
#include "hw_chip_common_def_dep.h"
#include "chip_common_config_ind.h"
//#include "lw_common_regs_dep.h"
#include "lw_common_config_ind.h"
#include "ipc_regs.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif /*CHIP_COMMON_H*/
