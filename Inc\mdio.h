/**
  ******************************************************************************
  * @file           : mdio.h
  * @brief          : Header for mdio.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MDIO_H__
#define __MDIO_H__

/* USER CODE END Includes */
/* ########################## Assert Selection ############################## */
/**
  * @brief Uncomment the line below to expanse the "assert_param" macro in the 
  *        HAL drivers code
  */
/* #define USE_FULL_ASSERT    1U */
/* USER CODE BEGIN Private defines */


/* USER CODE END Private defines */

#ifdef __cplusplus
 extern "C" {
#endif

/* USER CODE BEGIN Includes */
#include "stm32f2xx_hal.h"
#include "main.h"
/* USER CODE END Includes */
#define EVAL_I2Cx_TIMEOUT_MAX                   3000
extern SPI_HandleTypeDef hspi1;
/* USER CODE BEGIN Private defines */
/*dsp bsc slave indirect define*/ 
#define  sal_current_mdio_dev  0x1F // 0x1 < implied MDIO device address
#define  sal_current_mdio_port 0

#define IND_ADDRL  0x00
#define IND_ADDRH  0x01
#define IND_DATAL  0x02
#define IND_DATAH  0x03
#define IND_CTRL   0x04
#define MDIO_STAT  0x05
/*dsp bsc slave indirect define*/ 

#define IND_ADDR0_I2C  0x80
#define IND_ADDR1_I2C  0x81
#define IND_ADDR2_I2C  0x82
#define IND_ADDR3_I2C  0x83
#define IND_LEN0_I2C   0x84
#define IND_LEN1_I2C   0x85
#define IND_CTRL_I2C   0x86
#define WFIFO_I2C   0x87
#define RFIFO_I2C   0x90 //16bit bit0 start


/* USER CODE END Private defines */

void MX_SPI1_Init(void);
uint16_t mdio_write(uint16_t phyad,uint16_t devadr, uint16_t regadr, uint16_t value);
uint16_t mdio_read(uint16_t phyad,uint16_t devadr,uint16_t regadr);
extern void SPIx_Send_byte(uint16_t data);
#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
