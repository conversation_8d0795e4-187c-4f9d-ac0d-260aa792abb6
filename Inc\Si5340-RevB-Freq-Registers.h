/*
 * Si5340 Rev B Configuration Register Export Header File
 *
 * This file represents a series of Silicon Labs Si5340 Rev B 
 * register writes that can be performed to load a single configuration 
 * on a device. It was created by a Silicon Labs ClockBuilder Pro
 * export tool.
 *
 * Part:		                                       Si5340 Rev B
 * Design ID:                                          
 * Includes Pre/Post Download Control Register Writes: No
 * Created By:                                         ClockBuilder Pro v2.4 [2015-12-08]
 * Timestamp:                                          2018-03-12 17:50:14 GMT+08:00
 *
 * A complete design report corresponding to this export is included at the end 
 * of this header file.
 *
 */

#ifndef __SI5340_REVB_FREQ_REGISTERS_H__
#define __SI5340_REVB_FREQ_REGISTERS_H__

#define SI5340_REVB_REG_CONFIG_NUM_REGS				45

#ifdef __cplusplus
 extern "C" {
#endif

typedef struct
{
	unsigned int address; /* 16-bit register address */
	unsigned char value; /* 8-bit register data */

} si5340_revb_register_t;

si5340_revb_register_t const si5340_StartConfig_preamble[2] =
{
    /* Start configuration preamble */
	{ 0x0B24, 0xD8 },
	{ 0x0B25, 0x00 },

	/* End configuration preamble */
    /* Delay 300 msec */
	/*    Delay is worst case time for device to complete any calibration */
	/*    that is running due to device state change previous to this script */
	/*    being processed. */
};

si5340_revb_register_t const si5340_revb_registers[SI5340_REVB_REG_CONFIG_NUM_REGS] =
{
	{ 0x000B, 0x74 },
	{ 0x0017, 0x10 },
	{ 0x0018, 0xFF },
	{ 0x0021, 0x0F },
	{ 0x0022, 0x00 },
	{ 0x002B, 0x02 },
	{ 0x002C, 0x20 },
	{ 0x0102, 0x01 },
	{ 0x0112, 0x06 },
	{ 0x0113, 0x19 },
	{ 0x0114, 0x3D },
	{ 0x0115, 0x00 },
	{ 0x0117, 0x06 },
	{ 0x0118, 0x19 },
	{ 0x0119, 0x3D },
	{ 0x011A, 0x00 },
	{ 0x0126, 0x06 },
	{ 0x0127, 0x19 },
	{ 0x0128, 0x3D },
	{ 0x0129, 0x00 },
	{ 0x012B, 0x06 },
	{ 0x012C, 0x19 },
	{ 0x012D, 0x3D },
	{ 0x0141, 0x40 },
	{ 0x0238, 0xD8 },
	{ 0x0239, 0xD6 },
	{ 0x023A, 0x00 },
	{ 0x023E, 0xC0 },
	{ 0x0306, 0x16 },
	{ 0x030B, 0x80 },
	{ 0x0339, 0x1F },
	{ 0x090E, 0x02 },
	{ 0x091C, 0x04 },
	{ 0x0943, 0x01 },
	{ 0x0949, 0x08 },
	{ 0x094A, 0x80 },
	{ 0x0A02, 0x00 },
	{ 0x0A03, 0x01 },
	{ 0x0A04, 0x01 },
	{ 0x0A05, 0x01 },
	{ 0x0B44, 0x07 },
	{ 0x0B4A, 0x0E },
	{ 0x001C, 0x01 },
	{ 0x0B24, 0xDB },
	{ 0x0B25, 0x02 },
};

si5340_revb_register_t const si5340_ResetConfig[3] =
{
    //Soft reset
	{ 0x001C, 0x01 },
    /* End configuration preamble */
	{ 0x0B24, 0xDB },
	{ 0x0B25, 0x02 },
};

si5340_revb_register_t const si5340_FreqConfig[25][7] =
{
/*------ 96M ------*/
    {{ 0x0238, 0x00 },
		 { 0x0239, 0x90 },
     { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },                                                                                                                                                                                                                         
     { 0x0306, 0x24 },},
    /*------ 101.25M ------*/
    {{ 0x0238, 0x54 },
	   { 0x0239, 0x8D },
     { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x80 },
     { 0x0306, 0x21 },},
   /*----105.2MHz ------*/
    {{ 0x0238, 0xB0 },
	   { 0x0239, 0xD5 },
     { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0xC0 },
	   { 0x0305, 0x80 },
     { 0x0306, 0x20 },},

   /*----106.25M ------*/
    {{ 0x0238, 0x80 },
	   { 0x0239, 0xD4 },
     { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0xC0 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x20 },},
     
    /*----107.1MHz ------*/
    {{ 0x0238, 0x80 },
	   { 0x0239, 0xB2 },
     { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0xA0 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x20 },},
     
    /*----110.5MHz ------*/
    {{ 0x0238, 0x18 },
     { 0x0239, 0xD6 },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0xC0 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x1F },},
     
    /*----111MHz ------*/
    {{ 0x0238, 0x10 },
     { 0x0239, 0x8D },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x80 },
     { 0x0306, 0x1E },},
     
    /*----113MHz ------*/
    {{ 0x0238, 0x40 },
     { 0x0239, 0x8D },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x1E },},
     
    /*----113.2MHz ------*/
    {{ 0x0238, 0x80 },
     { 0x0239, 0x8D },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x1E },},
     
    /*----115MHz ------*/
    {{ 0x0238, 0xC0 },
     { 0x0239, 0x8F },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x1E },},
     
    /*----117MHz ------*/
    {{ 0x0238, 0x60 },
     { 0x0239, 0x8D },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },
     { 0x0306, 0x1D },},
     
    /*----120MHz ------*/
    {{ 0x0238, 0x80 },
     { 0x0239, 0x8E },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x80 },
     { 0x0306, 0x1C },},
     
    /*----125MHz ------*/
    {{ 0x0238, 0xA0 },
	   { 0x0239, 0x8C },
	   { 0x023A, 0x00 },
     { 0x023D, 0x00 },
     { 0x023E, 0x80 },
	   { 0x0305, 0x00 },
	   { 0x0306, 0x1B },},
   /*----126.5625MHz ------*/
   {{ 0x0238, 0x62 },
	  { 0x0239, 0x8E },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0x80 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x1B },},
    /*----126.7MHz ------*/
   {{ 0x0238, 0x2C },
	  { 0x0239, 0xB2 },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xA0 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x1B },},
   /*----135MHz ------*/
   {{ 0x0238, 0xA0 },
	  { 0x0239, 0x8C },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0x80 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x19 },},
    /*----136MHz ------*/
   {{ 0x0238, 0x80 },
	  { 0x0239, 0xD4 },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xC0 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x19 },},
    
   /*----140.25MHz ------*/
   {{ 0x0238, 0x2C },
	  { 0x0239, 0x8F },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xA0 },
	  { 0x0305, 0x80 },
	  { 0x0306, 0x18 },},
    
   /*----140.65MHz ------*/
   {{ 0x0238, 0xD0 },
	  { 0x0239, 0xAF },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xA0 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x18 },},
    
   /*----142.5MHz ------*/
   {{ 0x0238, 0x80 },
	  { 0x0239, 0x8E },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0x80 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x18 },},
    
    /*----145MHz ------*/
   {{ 0x0238, 0xF8 },
	  { 0x0239, 0xD4 },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xC0 },
	  { 0x0305, 0x80 },
	  { 0x0306, 0x17 },},
   
    /*----150MHz ------*/
   {{ 0x0238, 0xA0 },
	  { 0x0239, 0x8C },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0x80 },
	  { 0x0305, 0x80 },
	  { 0x0306, 0x16 },},
    
    /*----153.6MHz ------*/
   {{ 0x0238, 0x00 },
	  { 0x0239, 0x90 },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0x80 },
	  { 0x0305, 0x80 },
	  { 0x0306, 0x16 },},
    
   /*----155.52MHz ------*/
   {{ 0x0238, 0xC0 },
	  { 0x0239, 0xDE },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xC8 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x16 },},
    
   /*----156.25MHz ------*/
   {{ 0x0238, 0xD8 },
	  { 0x0239, 0xD6 },
	  { 0x023A, 0x00 },
    { 0x023D, 0x00 },
	  { 0x023E, 0xC0 },
	  { 0x0305, 0x00 },
	  { 0x0306, 0x16 },},
};
#ifdef __cplusplus
}
#endif
#endif /* __MAIN_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
