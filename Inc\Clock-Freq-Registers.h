/*
 * Si5340 Rev B Configuration Register Export Header File
 *
 * This file represents a series of Silicon Labs Si5340 Rev B 
 * register writes that can be performed to load a single configuration 
 * on a device. It was created by a Silicon Labs ClockBuilder Pro
 * export tool.
 *
 * Part:		                                       Si5340 Rev B
 * Design ID:                                          
 * Includes Pre/Post Download Control Register Writes: No
 * Created By:                                         ClockBuilder Pro v2.4 [2015-12-08]
 * Timestamp:                                          2018-03-12 17:50:14 GMT+08:00
 *
 * A complete design report corresponding to this export is included at the end 
 * of this header file.
 *
 */

#ifndef __CLOCK_FREQ_REGISTERS_H__
#define __CLOCK_FREQ_REGISTERS_H__

#ifdef __cplusplus
 extern "C" {
#endif

typedef struct
{
	unsigned int address; /* 16-bit register address */
	unsigned char value; /* 8-bit register data */

} clock_register_t;

clock_register_t const Clock_FreqConfig[18][20] =
{
	  /*------ 147.456 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x0c },
     { 0x20, 0x35 },
     { 0x16, 0x00 },
     { 0x17, 0x25 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x06 },
		 { 0x1d, 0xd2 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x0f },
		 { 0x21, 0x03 },
		 { 0x23, 0x07 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
	  /*------ 150.807 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x61 },
     { 0x20, 0xa8 },
     { 0x16, 0x00 },
     { 0x17, 0x24 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x1c },
		 { 0x1d, 0x5f },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 151.515 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x13 },
     { 0x20, 0x88 },
     { 0x16, 0x00 },
     { 0x17, 0x24 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x0a },
		 { 0x1d, 0xa7 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
    /*------ 156.25 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x01 },
     { 0x16, 0x00 },
     { 0x17, 0x20 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x00 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x32 },
		 { 0x25, 0x00 },
		 { 0x27, 0x00 },
		 { 0x24, 0x08 },
		 { 0x26, 0x00 },
		 { 0x22, 0x28 },
		 { 0x21, 0x0c },
		 { 0x21, 0x0c },
		 { 0x23, 0x03 },
		 { 0x23, 0x03 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 156.25 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x01 },
     { 0x16, 0x00 },
     { 0x17, 0x20 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x00 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x32 },
		 { 0x25, 0x00 },
		 { 0x27, 0x00 },
		 { 0x24, 0x08 },
		 { 0x26, 0x00 },
		 { 0x22, 0x28 },
		 { 0x21, 0x0c },
		 { 0x21, 0x0c },
		 { 0x23, 0x03 },
		 { 0x23, 0x03 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 164.0625 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x40 },
     { 0x16, 0x00 },
     { 0x17, 0x1d },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x25 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x2f },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x18 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 164.4264 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x3d },
     { 0x20, 0x09 },
     { 0x16, 0x00 },
     { 0x17, 0x20 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x25 },
		 { 0x1d, 0xa0 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x34 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x02 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 165 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x14 },
     { 0x16, 0x00 },
     { 0x17, 0x21 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x09 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x02 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 165.441 ------*/
    {{ 0x1e, 0x01 },
		 { 0x1f, 0x86 },
     { 0x20, 0xa0 },
     { 0x16, 0x00 },
     { 0x17, 0x21 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0xe8 },
		 { 0x1d, 0xa1 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 165.882 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x30 },
     { 0x20, 0xd4 },
     { 0x16, 0x00 },
     { 0x17, 0x1c },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x15 },
		 { 0x1d, 0xd3 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x2e },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x18 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 170 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x01 },
     { 0x16, 0x00 },
     { 0x17, 0x1e },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x00 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x33 },
		 { 0x25, 0x00 },
		 { 0x27, 0x00 },
		 { 0x24, 0x08 },
		 { 0x26, 0x00 },
		 { 0x22, 0x28 },
		 { 0x21, 0x00 },
		 { 0x21, 0x1c },
		 { 0x23, 0x23 },
		 { 0x23, 0x03 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 147.456 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x0c },
     { 0x20, 0x35 },
     { 0x16, 0x00 },
     { 0x17, 0x25 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x06 },
		 { 0x1d, 0xd2 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x0f },
		 { 0x21, 0x03 },
		 { 0x23, 0x07 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
	  /*------ 150.807 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x61 },
     { 0x20, 0xa8 },
     { 0x16, 0x00 },
     { 0x17, 0x24 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x1c },
		 { 0x1d, 0x5f },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 156.25 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x01 },
     { 0x16, 0x00 },
     { 0x17, 0x20 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x00 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x32 },
		 { 0x25, 0x00 },
		 { 0x27, 0x00 },
		 { 0x24, 0x08 },
		 { 0x26, 0x00 },
		 { 0x22, 0x28 },
		 { 0x21, 0x0c },
		 { 0x21, 0x0c },
		 { 0x23, 0x03 },
		 { 0x23, 0x03 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 156.25 ------*/
    {{ 0x1e, 0x00 },
		 { 0x1f, 0x00 },
     { 0x20, 0x01 },
     { 0x16, 0x00 },
     { 0x17, 0x20 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0x00 },
		 { 0x1d, 0x00 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x32 },
		 { 0x25, 0x00 },
		 { 0x27, 0x00 },
		 { 0x24, 0x08 },
		 { 0x26, 0x00 },
		 { 0x22, 0x28 },
		 { 0x21, 0x0c },
		 { 0x21, 0x0c },
		 { 0x23, 0x03 },
		 { 0x23, 0x03 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 164.7058 ------*/
    {{ 0x1e, 0x01 },
		 { 0x1f, 0x86 },
     { 0x20, 0xa0 },
     { 0x16, 0x00 },
     { 0x17, 0x1e },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0xa0 },
		 { 0x1d, 0xd9 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x31 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x18 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
		
		/*------ 165.441 ------*/
    {{ 0x1e, 0x01 },
		 { 0x1f, 0x86 },
     { 0x20, 0xa0 },
     { 0x16, 0x00 },
     { 0x17, 0x21 },
	   { 0x1b, 0x00 },                                                                                                                                                                                                                         
     { 0x1c, 0xe8 },
		 { 0x1d, 0xa1 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
    
    //188.23529
    {{ 0x1e, 0x3f },
		 { 0x1f, 0xff },
     { 0x20, 0xff },
     { 0x16, 0x00 },
     { 0x17, 0x1d },
	   { 0x1b, 0x25 },                                                                                                                                                                                                                         
     { 0x1c, 0xa5 },
		 { 0x1d, 0xa0 },
		 { 0x19, 0x00 },
		 { 0x1a, 0x36 },
		 { 0x25, 0x02 },
		 { 0x27, 0x07 },
		 { 0x24, 0x14 },
		 { 0x26, 0x00 },
		 { 0x22, 0x24 },
		 { 0x21, 0x03 },
		 { 0x21, 0x03 },
		 { 0x23, 0x27 },
		 { 0x23, 0x27 },
		 
		 { 0x48, 0x02 },
		},
    
};
#ifdef __cplusplus
}
#endif
#endif /* __MAIN_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
