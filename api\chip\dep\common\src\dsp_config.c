/**
 *
 * @file       dsp_internal_config.c
 * <AUTHOR> Firmware Team
 * @date       02/15/2020
 * @version    1.0
 *
 * @property 
 * $Copyright: (c) 2020 Broadcom.
 *             Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief      The CAPI files contain all the CAPIs used by the the module and line card products.
 *
 * @section  description
 *
 */
#include "type_defns.h"
#include "regs_common.h"
#include "chip_mode_def.h"
#include "capi_def.h"
#include "dsp_config.h"

#define DSP_MEDIA_SIDE_GPR_OFFSET                             0x00000600                         /**< DSP MEDIA Side GPR Offset                       */
#define DSP_LANE_0_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START +  0)        /**< DSP Lane 0 Configuration Base Address          */
#define DSP_LANE_1_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START +  4)        /**< DSP Lane 1 Configuration Base Address          */
#define DSP_LANE_2_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START +  8)        /**< DSP Lane 2 Configuration Base Address          */
#define DSP_LANE_3_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START + 12)        /**< DSP Lane 3 Configuration Base Address          */
#define DSP_LANE_4_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START + 16)        /**< DSP Lane 4 Configuration Base Address          */
#define DSP_LANE_5_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START + 20)        /**< DSP Lane 5 Configuration Base Address          */
#define DSP_LANE_6_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START + 24)        /**< DSP Lane 6 Configuration Base Address          */
#define DSP_LANE_7_CONFIG_GPR_BASE_ADDRESS                    (SRAM_GP_REGION_START + 28)        /**< DSP Lane 7 Configuration Base Address          */

/**< dsp lane base address per line id */
const ubaddr_t dsp_lane_bbaddr[PHY_BOTH_SIDES][DSP_LANE_NUM_MAX] =
                   {{ DSP_HOST_SIDE_ADDRESS  + DSP_LANE_0_ADDRESS_OFFSET,    /**< Host DSP Lane 0 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_1_ADDRESS_OFFSET,    /**< Host DSP Lane 1 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_2_ADDRESS_OFFSET,    /**< Host DSP Lane 2 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_3_ADDRESS_OFFSET,    /**< Host DSP Lane 3 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_4_ADDRESS_OFFSET,    /**< Host DSP Lane 4 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_5_ADDRESS_OFFSET,    /**< Host DSP Lane 5 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_6_ADDRESS_OFFSET,    /**< Host DSP Lane 6 Base Address  */
                      DSP_HOST_SIDE_ADDRESS  + DSP_LANE_7_ADDRESS_OFFSET },  /**< Host DSP Lane 7 Base Address  */
                    { DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_0_ADDRESS_OFFSET,    /**< Media DSP Lane 0 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_1_ADDRESS_OFFSET,    /**< Media DSP Lane 1 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_2_ADDRESS_OFFSET,    /**< Media DSP Lane 2 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_3_ADDRESS_OFFSET,    /**< Media DSP Lane 3 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_4_ADDRESS_OFFSET,    /**< Media DSP Lane 34Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_5_ADDRESS_OFFSET,    /**< Media DSP Lane 5 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_6_ADDRESS_OFFSET,    /**< Media DSP Lane 6 Base Address */
                      DSP_MEDIA_SIDE_ADDRESS + DSP_LANE_7_ADDRESS_OFFSET },  /**< Media DSP Lane 7 Base Address */
                    };

/**< DSP GPR lane configuration base address */
const ubaddr_t dsp_lane_config_gpr_bbaddr[PHY_BOTH_SIDES][DSP_LANE_NUM_MAX] =
                   {{ DSP_LANE_0_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 0 Configuration Base Address   */
                      DSP_LANE_1_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 1 Configuration Base Address   */
                      DSP_LANE_2_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 2 Configuration Base Address   */
                      DSP_LANE_3_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 3 Configuration Base Address   */
                      DSP_LANE_4_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 4 Configuration Base Address   */
                      DSP_LANE_5_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 5 Configuration Base Address   */
                      DSP_LANE_6_CONFIG_GPR_BASE_ADDRESS,    /**< DSP Host Side Lane 6 Configuration Base Address   */
                      DSP_LANE_7_CONFIG_GPR_BASE_ADDRESS},   /**< DSP Host Side Lane 7 Configuration Base Address   */
                    { DSP_LANE_0_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 0 Configuration Base Addresss */
                      DSP_LANE_1_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 1 Configuration Base Addresss */
                      DSP_LANE_2_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 2 Configuration Base Addresss */
                      DSP_LANE_3_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 3 Configuration Base Addresss */
                      DSP_LANE_4_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 4 Configuration Base Addresss */
                      DSP_LANE_5_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 5 Configuration Base Addresss */
                      DSP_LANE_6_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET, /**< DSP Media Side Lane 6 Configuration Base Addresss */
                      DSP_LANE_7_CONFIG_GPR_BASE_ADDRESS + DSP_MEDIA_SIDE_GPR_OFFSET},/**< DSP Media Side Lane 7 Configuration Base Addresss */
                    };
