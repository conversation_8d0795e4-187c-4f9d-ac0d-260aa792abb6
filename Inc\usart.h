/**
  ******************************************************************************
  * @file    usart.h
  * @brief   This file contains all the function prototypes for
  *          the usart.c file
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USART_H__
#define __USART_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <string.h>

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

extern UART_HandleTypeDef huart3;

/* USER CODE BEGIN Private defines */
extern void UsartReceive_IDLE(UART_HandleTypeDef *huart);
extern void UsartSendCmd(uint8_t *content);
/* USER CODE END Private defines */

void MX_USART3_UART_Init(void);

/* USER CODE BEGIN Prototypes */
#define DEBUG 1

#if(DEBUG == 1)

#define FINENAME strrchr(__FILE__, '\\') ? (strrchr(__FILE__, '\\') + 1) : __FILE__

#define debug_start() Com_Debug_Start()
#define debug_printf(format, ...) printf("[%15s:%4d] -- " format, FINENAME, __LINE__ , ##__VA_ARGS__)
#define debug_printfln(format, ...) printf("[%15s:%4d] -- " format "\r\n", FINENAME, __LINE__ , ##__VA_ARGS__)
#else

#define debug_start() 
#define debug_printf(format, ...) 
#define debug_printfln(format, ...) 

#endif

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __USART_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
