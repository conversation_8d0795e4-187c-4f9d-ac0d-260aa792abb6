Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.CheckInput_Statue) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.CheckInput_Statue) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.CheckInput_Statue) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.CheckInput_Statue) refers to main.o(.data) for C4
    main.o(i.Flash_Erase) refers to main.o(i.GetSector) for GetSector
    main.o(i.Flash_Erase) refers to stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    main.o(i.Flash_Write) refers to stm32f2xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    main.o(i.Flash_Write) refers to main.o(i.Flash_Erase) for Flash_Erase
    main.o(i.Flash_Write) refers to stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    main.o(i.Flash_Write) refers to stm32f2xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    main.o(i.Get_Time_Diff) refers to main.o(.data) for TimerFlag
    main.o(i.Module_Get_Present_State) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.Module_PlugDetect_Init) refers to main.o(i.Module_Get_Present_State) for Module_Get_Present_State
    main.o(i.Module_PlugDetect_Init) refers to main.o(.data) for C4
    main.o(i.Module_PlugDetect_Process) refers to main.o(i.Module_Get_Present_State) for Module_Get_Present_State
    main.o(i.Module_PlugDetect_Process) refers to main.o(i.Get_Time_Diff) for Get_Time_Diff
    main.o(i.Module_PlugDetect_Process) refers to strrchr.o(.text) for strrchr
    main.o(i.Module_PlugDetect_Process) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Module_PlugDetect_Process) refers to main.o(i.Module_Update_Statistics) for Module_Update_Statistics
    main.o(i.Module_PlugDetect_Process) refers to main.o(.data) for C4
    main.o(i.Module_PlugDetect_Process) refers to main.o(.conststring) for .conststring
    main.o(i.Module_PlugDetect_Reset_Counters) refers to main.o(i.Module_Update_Statistics) for Module_Update_Statistics
    main.o(i.Module_PlugDetect_Reset_Counters) refers to main.o(.data) for C4
    main.o(i.Module_Print_Detailed_Stats) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Module_Print_Detailed_Stats) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.Module_Print_Detailed_Stats) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.Module_Print_Detailed_Stats) refers to main.o(.data) for C4
    main.o(i.Module_Update_Statistics) refers to main.o(i.Flash_Write) for Flash_Write
    main.o(i.Module_Update_Statistics) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.Module_Update_Statistics) refers to main.o(.bss) for C4_0_255_Table
    main.o(i.Monitor) refers to master_i2c_pm.o(i.I2C_ReadWord_PM) for I2C_ReadWord_PM
    main.o(i.Monitor) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.Monitor) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.Monitor) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.Monitor) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(i.Monitor) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.Monitor) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.Monitor) refers to fdiv.o(.text) for __aeabi_fdiv
    main.o(i.Monitor) refers to fflti.o(.text) for __aeabi_i2f
    main.o(i.Monitor) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.Monitor) refers to ffixui.o(.text) for __aeabi_f2uiz
    main.o(i.Monitor) refers to main.o(i.ReadADCAverageValue) for ReadADCAverageValue
    main.o(i.Monitor) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.Monitor) refers to main.o(.data) for C4
    main.o(i.Monitor_Temp) refers to main.o(i.ReadADCAverageValue) for ReadADCAverageValue
    main.o(i.Monitor_Temp) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.Monitor_Temp) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.Monitor_Temp) refers to dadd.o(.text) for __aeabi_dsub
    main.o(i.Monitor_Temp) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.Monitor_Temp) refers to fflti.o(.text) for __aeabi_i2f
    main.o(i.Monitor_Temp) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.Monitor_Temp) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.Monitor_Temp) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(i.Monitor_Temp) refers to main.o(.data) for C4
    main.o(i.Process_ReceiveData) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.Process_ReceiveData) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.Process_ReceiveData) refers to main.o(i.getcommand) for getcommand
    main.o(i.Process_ReceiveData) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.Process_ReceiveData) refers to main.o(i.SendData) for SendData
    main.o(i.Process_ReceiveData) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.Process_ReceiveData) refers to main.o(i.__set_FAULTMASK) for __set_FAULTMASK
    main.o(i.Process_ReceiveData) refers to main.o(i.NVIC_SystemReset) for NVIC_SystemReset
    main.o(i.Process_ReceiveData) refers to main.o(i.splitcmd) for splitcmd
    main.o(i.Process_ReceiveData) refers to strncmp.o(.text) for strncmp
    main.o(i.Process_ReceiveData) refers to main.o(i.getcommand_value_int) for getcommand_value_int
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_init_prbs) for bcm87800_init_prbs
    main.o(i.Process_ReceiveData) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_download_firmware) for bcm87800_download_firmware
    main.o(i.Process_ReceiveData) refers to main.o(i.getcommand_value_float) for getcommand_value_float
    main.o(i.Process_ReceiveData) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(i.Process_ReceiveData) refers to main.o(i.SetOutputRefClock) for SetOutputRefClock
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_set_fec_pgen) for bcm87800_set_fec_pgen
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_clock_divider2) for bcm87800_clock_divider2
    main.o(i.Process_ReceiveData) refers to main.o(i.getcommand_value_hexint) for getcommand_value_hexint
    main.o(i.Process_ReceiveData) refers to master_i2c.o(i.I2C_WriteBytes) for I2C_WriteBytes
    main.o(i.Process_ReceiveData) refers to master_i2c.o(i.I2C_ReadBytes) for I2C_ReadBytes
    main.o(i.Process_ReceiveData) refers to usbd_cdc_if.o(.data) for RxFlag
    main.o(i.Process_ReceiveData) refers to main.o(.constdata) for .constdata
    main.o(i.Process_ReceiveData) refers to main.o(.data) for C4
    main.o(i.Process_ReceiveData) refers to usbd_cdc_if.o(.bss) for UserRxBufferFS
    main.o(i.Process_ReceiveData) refers to strcpy.o(.text) for strcpy
    main.o(i.Process_ReceiveData) refers to strcat.o(.text) for strcat
    main.o(i.Process_ReceiveData) refers to main.o(i.CheckInput_Statue) for CheckInput_Statue
    main.o(i.Process_ReceiveData) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.Process_ReceiveData) refers to main.o(i.SPI_DAC_Output) for SPI_DAC_Output
    main.o(i.Process_ReceiveData) refers to main.o(.bss) for C4_0_255_Table
    main.o(i.Process_ReceiveData) refers to main.o(i.Flash_Write) for Flash_Write
    main.o(i.Process_ReceiveData) refers to main.o(i.Module_Update_Statistics) for Module_Update_Statistics
    main.o(i.Process_ReceiveData) refers to main.o(i.Module_PlugDetect_Reset_Counters) for Module_PlugDetect_Reset_Counters
    main.o(i.Process_ReceiveData) refers to main.o(i.Module_Get_Present_State) for Module_Get_Present_State
    main.o(i.Process_ReceiveData) refers to main.o(i.Module_Print_Detailed_Stats) for Module_Print_Detailed_Stats
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_tx_squelch) for bcm87800_tx_squelch
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_tx_prbs) for bcm87800_tx_prbs
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_tx_emphasis) for bcm87800_tx_emphasis
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_tx_polarity) for bcm87800_tx_polarity
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_rx_prbs) for bcm87800_rx_prbs
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_rx_polarity) for bcm87800_rx_polarity
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_set_rx_info) for bcm87800_set_rx_info
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_enable_checker) for bcm87800_enable_checker
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_set_fec_init) for bcm87800_set_fec_init
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_set_fec_clear) for bcm87800_set_fec_clear
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_Checker_Status) for bcm87800_Checker_Status
    main.o(i.Process_ReceiveData) refers to dfltul.o(.text) for __aeabi_ul2d
    main.o(i.Process_ReceiveData) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.Process_ReceiveData) refers to bcm87800.o(i.bcm87800_set_fec_status) for bcm87800_set_fec_status
    main.o(i.Process_ReceiveData) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.ReadADCAverageValue) refers to main.o(.bss) for ADC_ConvertedValue
    main.o(i.SPI_DAC_Output) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.SPI_DAC_Output) refers to fflti.o(.text) for __aeabi_i2f
    main.o(i.SPI_DAC_Output) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.SPI_DAC_Output) refers to ffixui.o(.text) for __aeabi_f2uiz
    main.o(i.SPI_DAC_Output) refers to dfltui.o(.text) for __aeabi_ui2d
    main.o(i.SPI_DAC_Output) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.SPI_DAC_Output) refers to dadd.o(.text) for __aeabi_drsub
    main.o(i.SPI_DAC_Output) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(i.SPI_DAC_Output) refers to dac.o(i.WriteOutputDAC) for WriteOutputDAC
    main.o(i.SPI_DAC_Output) refers to main.o(.data) for C4
    main.o(i.SendData) refers to usart.o(i.UsartSendCmd) for UsartSendCmd
    main.o(i.SendData) refers to strlen.o(.text) for strlen
    main.o(i.SendData) refers to usbd_cdc_if.o(i.CDC_Transmit_FS) for CDC_Transmit_FS
    main.o(i.SendData) refers to main.o(.data) for interface
    main.o(i.SetOutputRefClock) refers to master_i2c.o(i.I2C_WriteByte) for I2C_WriteByte
    main.o(i.SetOutputRefClock) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.SetOutputRefClock) refers to main.o(.constdata) for Clock_FreqConfig
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    main.o(i.SystemClock_Config) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    main.o(i.Update_ErrorCount) refers to bcm87800.o(i.get_line_Checker_Status) for get_line_Checker_Status
    main.o(i.Update_ErrorCount) refers to bcm87800.o(i.get_host_Checker_Status) for get_host_Checker_Status
    main.o(i.Update_ErrorCount) refers to main.o(.bss) for BER
    main.o(i.getcommand) refers to strlen.o(.text) for strlen
    main.o(i.getcommand) refers to strncmp.o(.text) for strncmp
    main.o(i.getcommand) refers to main.o(i.split) for split
    main.o(i.getcommand) refers to usbd_cdc_if.o(.bss) for UserRxBufferFS
    main.o(i.getcommand_value) refers to _scanf_int.o(.text) for _scanf_int
    main.o(i.getcommand_value) refers to strlen.o(.text) for strlen
    main.o(i.getcommand_value) refers to strncmp.o(.text) for strncmp
    main.o(i.getcommand_value) refers to main.o(i.split) for split
    main.o(i.getcommand_value) refers to strstr.o(.text) for strstr
    main.o(i.getcommand_value) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.getcommand_value) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.getcommand_value) refers to usbd_cdc_if.o(.bss) for UserRxBufferFS
    main.o(i.getcommand_value_float) refers to scanf_fp.o(.text) for _scanf_real
    main.o(i.getcommand_value_float) refers to strlen.o(.text) for strlen
    main.o(i.getcommand_value_float) refers to strncmp.o(.text) for strncmp
    main.o(i.getcommand_value_float) refers to main.o(i.split) for split
    main.o(i.getcommand_value_float) refers to strstr.o(.text) for strstr
    main.o(i.getcommand_value_float) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.getcommand_value_hexint) refers to _scanf_int.o(.text) for _scanf_int
    main.o(i.getcommand_value_hexint) refers to strlen.o(.text) for strlen
    main.o(i.getcommand_value_hexint) refers to strncmp.o(.text) for strncmp
    main.o(i.getcommand_value_hexint) refers to main.o(i.split) for split
    main.o(i.getcommand_value_hexint) refers to strstr.o(.text) for strstr
    main.o(i.getcommand_value_hexint) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.getcommand_value_int) refers to _scanf_int.o(.text) for _scanf_int
    main.o(i.getcommand_value_int) refers to strlen.o(.text) for strlen
    main.o(i.getcommand_value_int) refers to strncmp.o(.text) for strncmp
    main.o(i.getcommand_value_int) refers to main.o(i.split) for split
    main.o(i.getcommand_value_int) refers to strstr.o(.text) for strstr
    main.o(i.getcommand_value_int) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.main) refers to stm32f2xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to mdio.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to main.o(i.Flash_Read) for Flash_Read
    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to strlen.o(.text) for strlen
    main.o(i.main) refers to memcpya.o(.text) for __aeabi_memcpy
    main.o(i.main) refers to stm32f2xx_hal_rng.o(i.HAL_RNG_GetRandomNumber) for HAL_RNG_GetRandomNumber
    main.o(i.main) refers to main.o(i.Flash_Write) for Flash_Write
    main.o(i.main) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to usb_device.o(i.MX_USB_DEVICE_Init) for MX_USB_DEVICE_Init
    main.o(i.main) refers to main.o(i.SPI_DAC_Output) for SPI_DAC_Output
    main.o(i.main) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(i.main) refers to master_i2c_pm.o(i.I2C_WriteWord_PM) for I2C_WriteWord_PM
    main.o(i.main) refers to bcm87800.o(i.bcm87800_download_firmware) for bcm87800_download_firmware
    main.o(i.main) refers to bcm87800.o(i.bcm87800_init_prbs) for bcm87800_init_prbs
    main.o(i.main) refers to main.o(i.Module_PlugDetect_Init) for Module_PlugDetect_Init
    main.o(i.main) refers to strrchr.o(.text) for strrchr
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to main.o(i.Process_ReceiveData) for Process_ReceiveData
    main.o(i.main) refers to main.o(i.Monitor) for Monitor
    main.o(i.main) refers to main.o(i.Monitor_Temp) for Monitor_Temp
    main.o(i.main) refers to main.o(i.Module_PlugDetect_Process) for Module_PlugDetect_Process
    main.o(i.main) refers to main.o(.bss) for MCUID
    main.o(i.main) refers to main.o(.data) for C4
    main.o(i.main) refers to rng.o(.bss) for hrng
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to main.o(.conststring) for .conststring
    main.o(i.split) refers to strtok.o(.text) for strtok
    main.o(i.split) refers to strcpy.o(.text) for strcpy
    main.o(i.splitcmd) refers to strtok.o(.text) for strtok
    main.o(i.splitcmd) refers to strcpy.o(.text) for strcpy
    main.o(i.splitcmd) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.splitcmd) refers to strstr.o(.text) for strstr
    main.o(i.splitcmd) refers to strcat.o(.text) for strcat
    main.o(.data) refers to main.o(.bss) for C4_0_255_Table
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Init) for USBD_Init
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_RegisterClass) for USBD_RegisterClass
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_cdc.o(i.USBD_CDC_RegisterInterface) for USBD_CDC_RegisterInterface
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_core.o(i.USBD_Start) for USBD_Start
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_desc.o(.data) for FS_Desc
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usb_device.o(.bss) for hUsbDeviceFS
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_cdc.o(.data) for USBD_CDC
    usb_device.o(i.MX_USB_DEVICE_Init) refers to usbd_cdc_if.o(.data) for USBD_Interface_fops_FS
    usbd_conf.o(i.HAL_PCD_ConnectCallback) refers to usbd_core.o(i.USBD_LL_DevConnected) for USBD_LL_DevConnected
    usbd_conf.o(i.HAL_PCD_ConnectCallback) refers to main.o(.data) for hid_State
    usbd_conf.o(i.HAL_PCD_DataInStageCallback) refers to usbd_core.o(i.USBD_LL_DataInStage) for USBD_LL_DataInStage
    usbd_conf.o(i.HAL_PCD_DataOutStageCallback) refers to usbd_core.o(i.USBD_LL_DataOutStage) for USBD_LL_DataOutStage
    usbd_conf.o(i.HAL_PCD_DisconnectCallback) refers to usbd_core.o(i.USBD_LL_DevDisconnected) for USBD_LL_DevDisconnected
    usbd_conf.o(i.HAL_PCD_DisconnectCallback) refers to main.o(.data) for hid_State
    usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoINIncomplete) for USBD_LL_IsoINIncomplete
    usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback) refers to usbd_core.o(i.USBD_LL_IsoOUTIncomplete) for USBD_LL_IsoOUTIncomplete
    usbd_conf.o(i.HAL_PCD_MspDeInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usbd_conf.o(i.HAL_PCD_MspDeInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbd_conf.o(i.HAL_PCD_MspInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_SetSpeed) for USBD_LL_SetSpeed
    usbd_conf.o(i.HAL_PCD_ResetCallback) refers to usbd_core.o(i.USBD_LL_Reset) for USBD_LL_Reset
    usbd_conf.o(i.HAL_PCD_ResumeCallback) refers to usbd_core.o(i.USBD_LL_Resume) for USBD_LL_Resume
    usbd_conf.o(i.HAL_PCD_ResumeCallback) refers to main.o(.data) for hid_State
    usbd_conf.o(i.HAL_PCD_SOFCallback) refers to usbd_core.o(i.USBD_LL_SOF) for USBD_LL_SOF
    usbd_conf.o(i.HAL_PCD_SetupStageCallback) refers to usbd_core.o(i.USBD_LL_SetupStage) for USBD_LL_SetupStage
    usbd_conf.o(i.HAL_PCD_SuspendCallback) refers to usbd_core.o(i.USBD_LL_Suspend) for USBD_LL_Suspend
    usbd_conf.o(i.HAL_PCD_SuspendCallback) refers to main.o(.data) for hid_State
    usbd_conf.o(i.USBD_LL_ClearStallEP) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) for HAL_PCD_EP_ClrStall
    usbd_conf.o(i.USBD_LL_CloseEP) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Close) for HAL_PCD_EP_Close
    usbd_conf.o(i.USBD_LL_DeInit) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_DeInit) for HAL_PCD_DeInit
    usbd_conf.o(i.USBD_LL_Delay) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    usbd_conf.o(i.USBD_LL_FlushEP) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Flush) for HAL_PCD_EP_Flush
    usbd_conf.o(i.USBD_LL_GetRxDataSize) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount) for HAL_PCD_EP_GetRxCount
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) for HAL_PCD_Init
    usbd_conf.o(i.USBD_LL_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f2xx_hal_pcd_ex.o(i.HAL_PCDEx_SetRxFiFo) for HAL_PCDEx_SetRxFiFo
    usbd_conf.o(i.USBD_LL_Init) refers to stm32f2xx_hal_pcd_ex.o(i.HAL_PCDEx_SetTxFiFo) for HAL_PCDEx_SetTxFiFo
    usbd_conf.o(i.USBD_LL_Init) refers to usbd_conf.o(.bss) for hpcd_USB_OTG_FS
    usbd_conf.o(i.USBD_LL_OpenEP) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Open) for HAL_PCD_EP_Open
    usbd_conf.o(i.USBD_LL_PrepareReceive) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Receive) for HAL_PCD_EP_Receive
    usbd_conf.o(i.USBD_LL_SetUSBAddress) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_SetAddress) for HAL_PCD_SetAddress
    usbd_conf.o(i.USBD_LL_StallEP) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) for HAL_PCD_EP_SetStall
    usbd_conf.o(i.USBD_LL_Start) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_Start) for HAL_PCD_Start
    usbd_conf.o(i.USBD_LL_Stop) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop) for HAL_PCD_Stop
    usbd_conf.o(i.USBD_LL_Transmit) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) for HAL_PCD_EP_Transmit
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) refers to usbd_desc.o(.bss) for USBD_StrDesc
    usbd_desc.o(i.USBD_FS_DeviceDescriptor) refers to usbd_desc.o(.data) for USBD_FS_DeviceDesc
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) refers to usbd_desc.o(.bss) for USBD_StrDesc
    usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) refers to usbd_desc.o(.data) for USBD_LangIDDesc
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) refers to usbd_desc.o(.bss) for USBD_StrDesc
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_ProductStrDescriptor) refers to usbd_desc.o(.bss) for USBD_StrDesc
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_ctlreq.o(i.USBD_GetString) for USBD_GetString
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to usbd_desc.o(.bss) for USBD_StrDesc
    usbd_desc.o(i.USBD_FS_SerialStrDescriptor) refers to main.o(.data) for C4
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_DeviceDescriptor) for USBD_FS_DeviceDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) for USBD_FS_LangIDStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) for USBD_FS_ManufacturerStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ProductStrDescriptor) for USBD_FS_ProductStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_SerialStrDescriptor) for USBD_FS_SerialStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) for USBD_FS_ConfigStrDescriptor
    usbd_desc.o(.data) refers to usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) for USBD_FS_InterfaceStrDescriptor
    stm32f2xx_it.o(i.DMA1_Stream1_IRQHandler) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f2xx_it.o(i.DMA1_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f2xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f2xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f2xx_it.o(i.OTG_FS_IRQHandler) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) for HAL_PCD_IRQHandler
    stm32f2xx_it.o(i.OTG_FS_IRQHandler) refers to usbd_conf.o(.bss) for hpcd_USB_OTG_FS
    stm32f2xx_it.o(i.SysTick_Handler) refers to stm32f2xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f2xx_it.o(i.SysTick_Handler) refers to stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) for HAL_SYSTICK_IRQHandler
    stm32f2xx_it.o(i.SysTick_Handler) refers to main.o(.data) for TimerFlag
    stm32f2xx_it.o(i.USART3_IRQHandler) refers to stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f2xx_it.o(i.USART3_IRQHandler) refers to usart.o(i.UsartReceive_IDLE) for UsartReceive_IDLE
    stm32f2xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.start) for start
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.write_8bit) for write_8bit
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.slave_ack) for slave_ack
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.read_8bit) for read_8bit
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.master_no_ack) for master_no_ack
    master_i2c.o(i.I2C_ReadByte) refers to master_i2c.o(i.stop) for stop
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.start) for start
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.write_8bit) for write_8bit
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.slave_ack) for slave_ack
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.read_8bit) for read_8bit
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.master_ack) for master_ack
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.master_no_ack) for master_no_ack
    master_i2c.o(i.I2C_ReadBytes) refers to master_i2c.o(i.stop) for stop
    master_i2c.o(i.I2C_WriteByte) refers to master_i2c.o(i.start) for start
    master_i2c.o(i.I2C_WriteByte) refers to master_i2c.o(i.write_8bit) for write_8bit
    master_i2c.o(i.I2C_WriteByte) refers to master_i2c.o(i.slave_ack) for slave_ack
    master_i2c.o(i.I2C_WriteByte) refers to master_i2c.o(i.stop) for stop
    master_i2c.o(i.I2C_WriteBytes) refers to master_i2c.o(i.start) for start
    master_i2c.o(i.I2C_WriteBytes) refers to master_i2c.o(i.write_8bit) for write_8bit
    master_i2c.o(i.I2C_WriteBytes) refers to master_i2c.o(i.slave_ack) for slave_ack
    master_i2c.o(i.I2C_WriteBytes) refers to master_i2c.o(i.stop) for stop
    master_i2c.o(i.master_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.master_ack) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.master_ack) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.master_no_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.master_no_ack) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.master_no_ack) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.read_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.read_8bit) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.read_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    master_i2c.o(i.read_8bit) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.slave_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.slave_ack) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.slave_ack) refers to master_i2c.o(i.stop) for stop
    master_i2c.o(i.slave_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    master_i2c.o(i.slave_ack) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.start) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.start) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.start) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.stop) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.stop) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.stop) refers to master_i2c.o(.data) for CLK_WIDE
    master_i2c.o(i.write_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c.o(i.write_8bit) refers to master_i2c.o(i.Delay_ns) for Delay_ns
    master_i2c.o(i.write_8bit) refers to master_i2c.o(.data) for CLK_WIDE
    mdio.o(i.HAL_SPI_MspDeInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    mdio.o(i.HAL_SPI_MspInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    mdio.o(i.MX_SPI1_Init) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    mdio.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    mdio.o(i.MX_SPI1_Init) refers to mdio.o(i.SPI_Cmd) for SPI_Cmd
    mdio.o(i.MX_SPI1_Init) refers to mdio.o(.bss) for hspi1
    mdio.o(i.SPIx_Receive_byte) refers to mdio.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    mdio.o(i.SPIx_Receive_byte) refers to mdio.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    mdio.o(i.SPIx_Receive_byte) refers to mdio.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    mdio.o(i.SPIx_Receive_byte) refers to mdio.o(.bss) for hspi1
    mdio.o(i.SPIx_Send_byte) refers to mdio.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    mdio.o(i.SPIx_Send_byte) refers to mdio.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    mdio.o(i.SPIx_Send_byte) refers to mdio.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    mdio.o(i.SPIx_Send_byte) refers to mdio.o(.bss) for hspi1
    mdio.o(i.mdio_read) refers to mdio.o(i.SPIx_Send_byte) for SPIx_Send_byte
    mdio.o(i.mdio_read) refers to mdio.o(i.SPIx_Receive_byte) for SPIx_Receive_byte
    mdio.o(i.mdio_write) refers to mdio.o(i.SPIx_Send_byte) for SPIx_Send_byte
    mdio.o(i.rd_reg) refers to mdio.o(i.sal_rd_reg) for sal_rd_reg
    mdio.o(i.rd_reg_ex) refers to mdio.o(i.rd_reg) for rd_reg
    mdio.o(i.sal_rd_reg) refers to mdio.o(i.sal_rd_reg_ex) for sal_rd_reg_ex
    mdio.o(i.sal_rd_reg_ex) refers to mdio.o(i.sal_rdmdio) for sal_rdmdio
    mdio.o(i.sal_rdmdio) refers to mdio.o(i.mdio_write) for mdio_write
    mdio.o(i.sal_rdmdio) refers to mdio.o(i.mdio_read) for mdio_read
    mdio.o(i.sal_wr_reg) refers to mdio.o(i.sal_wr_reg_ex) for sal_wr_reg_ex
    mdio.o(i.sal_wr_reg_ex) refers to mdio.o(i.sal_wrmdio) for sal_wrmdio
    mdio.o(i.sal_wrmdio) refers to mdio.o(i.mdio_write) for mdio_write
    mdio.o(i.wr_reg) refers to mdio.o(i.sal_wr_reg) for sal_wr_reg
    mdio.o(i.wr_reg_ex) refers to mdio.o(i.wr_reg) for wr_reg
    usbd_cdc_if.o(i.CDC_Control_FS) refers to usbd_cdc_if.o(.data) for LineCoding
    usbd_cdc_if.o(i.CDC_Init_FS) refers to usbd_cdc.o(i.USBD_CDC_SetTxBuffer) for USBD_CDC_SetTxBuffer
    usbd_cdc_if.o(i.CDC_Init_FS) refers to usbd_cdc.o(i.USBD_CDC_SetRxBuffer) for USBD_CDC_SetRxBuffer
    usbd_cdc_if.o(i.CDC_Init_FS) refers to usbd_cdc_if.o(.bss) for UserTxBufferFS
    usbd_cdc_if.o(i.CDC_Init_FS) refers to usb_device.o(.bss) for hUsbDeviceFS
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to usbd_cdc.o(i.USBD_CDC_SetRxBuffer) for USBD_CDC_SetRxBuffer
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to usbd_cdc.o(i.USBD_CDC_ReceivePacket) for USBD_CDC_ReceivePacket
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to memcpya.o(.text) for __aeabi_memcpy
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to strstr.o(.text) for strstr
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to usb_device.o(.bss) for hUsbDeviceFS
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to usbd_cdc_if.o(.data) for p_TempBuf
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to usbd_cdc_if.o(.bss) for UserRxBufferFS
    usbd_cdc_if.o(i.CDC_Receive_FS) refers to main.o(.data) for interface
    usbd_cdc_if.o(i.CDC_Transmit_FS) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    usbd_cdc_if.o(i.CDC_Transmit_FS) refers to usbd_cdc.o(i.USBD_CDC_SetTxBuffer) for USBD_CDC_SetTxBuffer
    usbd_cdc_if.o(i.CDC_Transmit_FS) refers to usbd_cdc.o(i.USBD_CDC_TransmitPacket) for USBD_CDC_TransmitPacket
    usbd_cdc_if.o(i.CDC_Transmit_FS) refers to usb_device.o(.bss) for hUsbDeviceFS
    usbd_cdc_if.o(.data) refers to usbd_cdc_if.o(i.CDC_Init_FS) for CDC_Init_FS
    usbd_cdc_if.o(.data) refers to usbd_cdc_if.o(i.CDC_DeInit_FS) for CDC_DeInit_FS
    usbd_cdc_if.o(.data) refers to usbd_cdc_if.o(i.CDC_Control_FS) for CDC_Control_FS
    usbd_cdc_if.o(.data) refers to usbd_cdc_if.o(i.CDC_Receive_FS) for CDC_Receive_FS
    bcm87800.o(i.bcm87800_Checker_Status) refers to bcm87800.o(i.get_line_Checker_Status) for get_line_Checker_Status
    bcm87800.o(i.bcm87800_Checker_Status) refers to bcm87800.o(i.get_host_Checker_Status) for get_host_Checker_Status
    bcm87800.o(i.bcm87800_Checker_Status) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_clock_divider) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_clock_divider) refers to capi_test.o(i.capi_test_set_command) for capi_test_set_command
    bcm87800.o(i.bcm87800_clock_divider) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_clock_divider2) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.bcm87800_clock_divider2) refers to common_util.o(i.util_memcpy) for util_memcpy
    bcm87800.o(i.bcm87800_clock_divider2) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.bcm87800_clock_divider2) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_clock_divider_vco) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_clock_divider_vco) refers to capi_test.o(i.capi_test_set_command) for capi_test_set_command
    bcm87800.o(i.bcm87800_disable_prbs) refers to bcm87800.o(i.set_line_disable_prbs) for set_line_disable_prbs
    bcm87800.o(i.bcm87800_disable_prbs) refers to bcm87800.o(i.set_host_disable_prbs) for set_host_disable_prbs
    bcm87800.o(i.bcm87800_disable_prbs) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_download_firmware) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bcm87800.o(i.bcm87800_download_firmware) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    bcm87800.o(i.bcm87800_enable_checker) refers to bcm87800.o(i.set_enable_line_checker) for set_enable_line_checker
    bcm87800.o(i.bcm87800_enable_checker) refers to bcm87800.o(i.set_enable_host_checker) for set_enable_host_checker
    bcm87800.o(i.bcm87800_enable_checker) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_init_prbs) refers to f2d.o(.text) for __aeabi_f2d
    bcm87800.o(i.bcm87800_init_prbs) refers to ddiv.o(.text) for __aeabi_ddiv
    bcm87800.o(i.bcm87800_init_prbs) refers to d2f.o(.text) for __aeabi_d2f
    bcm87800.o(i.bcm87800_init_prbs) refers to ffltui.o(.text) for __aeabi_ui2f
    bcm87800.o(i.bcm87800_init_prbs) refers to fdiv.o(.text) for __aeabi_fdiv
    bcm87800.o(i.bcm87800_init_prbs) refers to main.o(i.SetOutputRefClock) for SetOutputRefClock
    bcm87800.o(i.bcm87800_init_prbs) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    bcm87800.o(i.bcm87800_init_prbs) refers to bcm87800.o(i.example_line_prbs) for example_line_prbs
    bcm87800.o(i.bcm87800_init_prbs) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_init_prbs) refers to bcm87800.o(.data) for media_dataDate
    bcm87800.o(i.bcm87800_rx_polarity) refers to bcm87800.o(i.set_line_rx_polarity) for set_line_rx_polarity
    bcm87800.o(i.bcm87800_rx_polarity) refers to bcm87800.o(i.set_host_rx_polarity) for set_host_rx_polarity
    bcm87800.o(i.bcm87800_rx_polarity) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_rx_prbs) refers to bcm87800.o(i.set_line_rx_prbs) for set_line_rx_prbs
    bcm87800.o(i.bcm87800_rx_prbs) refers to bcm87800.o(i.set_host_rx_prbs) for set_host_rx_prbs
    bcm87800.o(i.bcm87800_rx_prbs) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_clear) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_set_fec_clear) refers to capi_diag.o(i.capi_clear_fec_mon) for capi_clear_fec_mon
    bcm87800.o(i.bcm87800_set_fec_clear) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_clear) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.bcm87800_set_fec_clear) refers to bcm87800.o(.bss) for capi_fec_dump_status
    bcm87800.o(i.bcm87800_set_fec_clear_all) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_set_fec_clear_all) refers to capi_diag.o(i.capi_clear_fec_mon) for capi_clear_fec_mon
    bcm87800.o(i.bcm87800_set_fec_clear_all) refers to bcm87800.o(.bss) for capi_fec_dump_status
    bcm87800.o(i.bcm87800_set_fec_clear_all) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_tx_prbs) for set_line_tx_prbs
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_tx_polarity) for set_line_tx_polarity
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_rx_prbs) for set_line_rx_prbs
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_rx_polarity) for set_line_rx_polarity
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_tx_emphasis) for set_line_tx_emphasis
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.set_line_rx_info) for set_line_rx_info
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.bcm87800_set_fec_init) for bcm87800_set_fec_init
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.bcm87800_disable_prbs) for bcm87800_disable_prbs
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.bcm87800_set_fec_pgen) for bcm87800_set_fec_pgen
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to bcm87800.o(i.bcm87800_set_fec_clear) for bcm87800_set_fec_clear
    bcm87800.o(i.bcm87800_set_fec_enabled) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_init) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_set_fec_init) refers to capi_diag.o(i.capi_init_fec_mon) for capi_init_fec_mon
    bcm87800.o(i.bcm87800_set_fec_init) refers to bcm87800.o(.bss) for capi_fec_dump_status
    bcm87800.o(i.bcm87800_set_fec_init) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_init) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.bcm87800_set_fec_pgen) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_set_fec_pgen) refers to bcm87800.o(i.test_fec_pgen_seq) for test_fec_pgen_seq
    bcm87800.o(i.bcm87800_set_fec_pgen) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_status) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.bcm87800_set_fec_status) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.bcm87800_set_fec_status) refers to capi_diag.o(i.capi_get_fec_info) for capi_get_fec_info
    bcm87800.o(i.bcm87800_set_fec_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    bcm87800.o(i.bcm87800_set_fec_status) refers to bcm87800.o(.bss) for capi_fec_dump_status
    bcm87800.o(i.bcm87800_set_fec_status) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_set_fec_status) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.bcm87800_set_fec_status) refers to main.o(.bss) for BER
    bcm87800.o(i.bcm87800_set_rx_info) refers to bcm87800.o(i.set_line_rx_info) for set_line_rx_info
    bcm87800.o(i.bcm87800_set_rx_info) refers to bcm87800.o(i.set_host_rx_info) for set_host_rx_info
    bcm87800.o(i.bcm87800_set_rx_info) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_tx_emphasis) refers to bcm87800.o(i.set_line_tx_emphasis) for set_line_tx_emphasis
    bcm87800.o(i.bcm87800_tx_emphasis) refers to bcm87800.o(i.set_host_tx_emphasis) for set_host_tx_emphasis
    bcm87800.o(i.bcm87800_tx_emphasis) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_tx_polarity) refers to bcm87800.o(i.set_line_tx_polarity) for set_line_tx_polarity
    bcm87800.o(i.bcm87800_tx_polarity) refers to bcm87800.o(i.set_host_tx_polarity) for set_host_tx_polarity
    bcm87800.o(i.bcm87800_tx_polarity) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_tx_prbs) refers to bcm87800.o(i.set_line_tx_prbs) for set_line_tx_prbs
    bcm87800.o(i.bcm87800_tx_prbs) refers to bcm87800.o(i.set_host_tx_prbs) for set_host_tx_prbs
    bcm87800.o(i.bcm87800_tx_prbs) refers to main.o(.data) for C4
    bcm87800.o(i.bcm87800_tx_squelch) refers to bcm87800.o(i.set_line_tx_squelch) for set_line_tx_squelch
    bcm87800.o(i.bcm87800_tx_squelch) refers to bcm87800.o(i.set_host_tx_squelch) for set_host_tx_squelch
    bcm87800.o(i.bcm87800_tx_squelch) refers to main.o(.data) for C4
    bcm87800.o(i.chip_internal_avdd_0p9) refers to capi.o(i.capi_set_internal_regulator_voltage) for capi_set_internal_regulator_voltage
    bcm87800.o(i.chip_internal_dvdd) refers to capi.o(i.capi_set_voltage_config) for capi_set_voltage_config
    bcm87800.o(i.chip_internal_vddm_0p75) refers to capi.o(i.capi_set_internal_regulator_voltage) for capi_set_internal_regulator_voltage
    bcm87800.o(i.example_line_prbs) refers to memcpya.o(.text) for __aeabi_memcpy4
    bcm87800.o(i.example_line_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.example_line_prbs) refers to capi.o(i.capi_reset) for capi_reset
    bcm87800.o(i.example_line_prbs) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    bcm87800.o(i.example_line_prbs) refers to capi.o(i.capi_get_firmware_status) for capi_get_firmware_status
    bcm87800.o(i.example_line_prbs) refers to capi.o(i.capi_read_register) for capi_read_register
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.chip_internal_vddm_0p75) for chip_internal_vddm_0p75
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.chip_internal_avdd_0p9) for chip_internal_avdd_0p9
    bcm87800.o(i.example_line_prbs) refers to capi.o(i.capi_set_config) for capi_set_config
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_tx_prbs) for set_line_tx_prbs
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_tx_polarity) for set_line_tx_polarity
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_rx_prbs) for set_line_rx_prbs
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_rx_polarity) for set_line_rx_polarity
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_tx_emphasis) for set_line_tx_emphasis
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_rx_info) for set_line_rx_info
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_user_pattern) for set_host_user_pattern
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_tx_prbs) for set_host_tx_prbs
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_tx_polarity) for set_host_tx_polarity
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_rx_prbs) for set_host_rx_prbs
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_rx_polarity) for set_host_rx_polarity
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_tx_emphasis) for set_host_tx_emphasis
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_host_rx_info) for set_host_rx_info
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.set_line_user_pattern) for set_line_user_pattern
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_set_fec_init) for bcm87800_set_fec_init
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_disable_prbs) for bcm87800_disable_prbs
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_set_fec_pgen) for bcm87800_set_fec_pgen
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_set_fec_clear) for bcm87800_set_fec_clear
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_clock_divider_vco) for bcm87800_clock_divider_vco
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(i.bcm87800_clock_divider2) for bcm87800_clock_divider2
    bcm87800.o(i.example_line_prbs) refers to bcm87800.o(.constdata) for .constdata
    bcm87800.o(i.example_line_prbs) refers to main.o(.data) for C4
    bcm87800.o(i.example_line_prbs) refers to main.o(.bss) for BER
    bcm87800.o(i.get_host_Checker_Status) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.get_host_Checker_Status) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.get_host_Checker_Status) refers to main.o(i.Get_Time_Diff) for Get_Time_Diff
    bcm87800.o(i.get_host_Checker_Status) refers to ffltui.o(.text) for __aeabi_ui2f
    bcm87800.o(i.get_host_Checker_Status) refers to fmul.o(.text) for __aeabi_fmul
    bcm87800.o(i.get_host_Checker_Status) refers to ffixul.o(.text) for __aeabi_f2ulz
    bcm87800.o(i.get_host_Checker_Status) refers to bcm87800.o(.data) for host_rx_chan
    bcm87800.o(i.get_host_Checker_Status) refers to main.o(.data) for C4
    bcm87800.o(i.get_host_Checker_Status) refers to main.o(.bss) for BER
    bcm87800.o(i.get_line_Checker_Status) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.get_line_Checker_Status) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.get_line_Checker_Status) refers to main.o(i.Get_Time_Diff) for Get_Time_Diff
    bcm87800.o(i.get_line_Checker_Status) refers to ffltui.o(.text) for __aeabi_ui2f
    bcm87800.o(i.get_line_Checker_Status) refers to fmul.o(.text) for __aeabi_fmul
    bcm87800.o(i.get_line_Checker_Status) refers to ffixul.o(.text) for __aeabi_f2ulz
    bcm87800.o(i.get_line_Checker_Status) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.get_line_Checker_Status) refers to main.o(.data) for C4
    bcm87800.o(i.get_line_Checker_Status) refers to main.o(.bss) for BER
    bcm87800.o(i.set_enable_host_checker) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_enable_host_checker) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_enable_host_checker) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    bcm87800.o(i.set_enable_host_checker) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.set_enable_host_checker) refers to bcm87800.o(.data) for host_rx_chan
    bcm87800.o(i.set_enable_host_checker) refers to main.o(.data) for C4
    bcm87800.o(i.set_enable_line_checker) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_enable_line_checker) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_enable_line_checker) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    bcm87800.o(i.set_enable_line_checker) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.set_enable_line_checker) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.set_enable_line_checker) refers to main.o(.data) for C4
    bcm87800.o(i.set_host_disable_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_disable_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_host_rx_info) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_rx_info) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_rx_info) refers to capi.o(i.capi_get_lane_config_info) for capi_get_lane_config_info
    bcm87800.o(i.set_host_rx_info) refers to memcpya.o(.text) for __aeabi_memcpy
    bcm87800.o(i.set_host_rx_info) refers to capi.o(i.capi_set_lane_config_info) for capi_set_lane_config_info
    bcm87800.o(i.set_host_rx_info) refers to bcm87800.o(.data) for host_rx_chan
    bcm87800.o(i.set_host_rx_info) refers to main.o(.data) for C4
    bcm87800.o(i.set_host_rx_polarity) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_rx_polarity) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_rx_polarity) refers to capi.o(i.capi_set_polarity) for capi_set_polarity
    bcm87800.o(i.set_host_rx_polarity) refers to capi.o(i.capi_get_polarity) for capi_get_polarity
    bcm87800.o(i.set_host_rx_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_rx_prbs) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_rx_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_host_rx_prbs) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.set_host_rx_prbs) refers to bcm87800.o(.data) for host_rx_chan
    bcm87800.o(i.set_host_tx_emphasis) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_tx_emphasis) refers to capi.o(i.capi_get_lane_config_info) for capi_get_lane_config_info
    bcm87800.o(i.set_host_tx_emphasis) refers to capi.o(i.capi_set_lane_config_info) for capi_set_lane_config_info
    bcm87800.o(i.set_host_tx_emphasis) refers to bcm87800.o(.data) for host_tx_chan
    bcm87800.o(i.set_host_tx_emphasis) refers to main.o(.data) for C4
    bcm87800.o(i.set_host_tx_emphasis) refers to main.o(.bss) for BER
    bcm87800.o(i.set_host_tx_polarity) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_tx_polarity) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_tx_polarity) refers to capi.o(i.capi_set_polarity) for capi_set_polarity
    bcm87800.o(i.set_host_tx_polarity) refers to capi.o(i.capi_get_polarity) for capi_get_polarity
    bcm87800.o(i.set_host_tx_polarity) refers to bcm87800.o(.data) for host_tx_chan
    bcm87800.o(i.set_host_tx_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_tx_prbs) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_tx_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_host_tx_prbs) refers to bcm87800.o(i.set_user_pattern) for set_user_pattern
    bcm87800.o(i.set_host_tx_prbs) refers to bcm87800.o(.data) for host_tx_chan
    bcm87800.o(i.set_host_tx_squelch) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_tx_squelch) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_host_tx_squelch) refers to capi.o(i.capi_set_lane_ctrl_info) for capi_set_lane_ctrl_info
    bcm87800.o(i.set_host_tx_squelch) refers to bcm87800.o(.data) for host_tx_chan
    bcm87800.o(i.set_host_user_pattern) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_host_user_pattern) refers to bcm87800.o(i.set_user_pattern) for set_user_pattern
    bcm87800.o(i.set_host_user_pattern) refers to bcm87800.o(.data) for host_tx_chan
    bcm87800.o(i.set_line_disable_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_disable_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_line_rx_info) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_rx_info) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_line_rx_info) refers to capi.o(i.capi_get_lane_config_info) for capi_get_lane_config_info
    bcm87800.o(i.set_line_rx_info) refers to memcpya.o(.text) for __aeabi_memcpy
    bcm87800.o(i.set_line_rx_info) refers to capi.o(i.capi_set_lane_config_info) for capi_set_lane_config_info
    bcm87800.o(i.set_line_rx_info) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.set_line_rx_info) refers to main.o(.data) for C4
    bcm87800.o(i.set_line_rx_polarity) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_rx_polarity) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_line_rx_polarity) refers to capi.o(i.capi_set_polarity) for capi_set_polarity
    bcm87800.o(i.set_line_rx_polarity) refers to capi.o(i.capi_get_polarity) for capi_get_polarity
    bcm87800.o(i.set_line_rx_polarity) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.set_line_rx_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_rx_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_line_rx_prbs) refers to capi_test.o(i.capi_get_prbs_status) for capi_get_prbs_status
    bcm87800.o(i.set_line_rx_prbs) refers to bcm87800.o(.data) for line_rx_chan
    bcm87800.o(i.set_line_tx_emphasis) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_tx_emphasis) refers to capi.o(i.capi_get_lane_config_info) for capi_get_lane_config_info
    bcm87800.o(i.set_line_tx_emphasis) refers to capi.o(i.capi_set_lane_config_info) for capi_set_lane_config_info
    bcm87800.o(i.set_line_tx_emphasis) refers to bcm87800.o(.data) for line_tx_chan
    bcm87800.o(i.set_line_tx_emphasis) refers to main.o(.data) for C4
    bcm87800.o(i.set_line_tx_emphasis) refers to main.o(.bss) for BER
    bcm87800.o(i.set_line_tx_polarity) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_tx_polarity) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_line_tx_polarity) refers to capi.o(i.capi_set_polarity) for capi_set_polarity
    bcm87800.o(i.set_line_tx_polarity) refers to capi.o(i.capi_get_polarity) for capi_get_polarity
    bcm87800.o(i.set_line_tx_polarity) refers to bcm87800.o(.data) for line_tx_chan
    bcm87800.o(i.set_line_tx_prbs) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_tx_prbs) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_line_tx_prbs) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.set_line_tx_prbs) refers to bcm87800.o(i.set_user_pattern) for set_user_pattern
    bcm87800.o(i.set_line_tx_prbs) refers to bcm87800.o(.data) for line_tx_chan
    bcm87800.o(i.set_line_tx_squelch) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_tx_squelch) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.set_line_tx_squelch) refers to capi.o(i.capi_set_lane_ctrl_info) for capi_set_lane_ctrl_info
    bcm87800.o(i.set_line_tx_squelch) refers to bcm87800.o(.data) for line_tx_chan
    bcm87800.o(i.set_line_user_pattern) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_line_user_pattern) refers to bcm87800.o(i.set_user_pattern) for set_user_pattern
    bcm87800.o(i.set_line_user_pattern) refers to bcm87800.o(.data) for line_tx_chan
    bcm87800.o(i.set_user_pattern) refers to memseta.o(.text) for __aeabi_memclr4
    bcm87800.o(i.set_user_pattern) refers to common_util.o(i.util_memcpy) for util_memcpy
    bcm87800.o(i.set_user_pattern) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.test_fec_pgen_seq) refers to capi_test.o(i.capi_set_prbs_info) for capi_set_prbs_info
    bcm87800.o(i.test_fec_pgen_seq) refers to common_util.o(i.util_memset) for util_memset
    bcm87800.o(i.test_fec_pgen_seq) refers to capi_test.o(i.capi_get_prbs_info) for capi_get_prbs_info
    rng.o(i.MX_RNG_Init) refers to stm32f2xx_hal_rng.o(i.HAL_RNG_Init) for HAL_RNG_Init
    rng.o(i.MX_RNG_Init) refers to main.o(i.Error_Handler) for Error_Handler
    rng.o(i.MX_RNG_Init) refers to rng.o(.bss) for hrng
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.MX_DAC_Init) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for hdac
    dac.o(i.WriteOutputDAC) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    dac.o(i.WriteOutputDAC) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.WriteOutputDAC) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dac.o(i.WriteOutputDAC) refers to dac.o(.bss) for hdac
    dma.o(i.MX_DMA_Init) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for hdma_adc1
    adc.o(i.MX_ADC1_Init) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.start) for start
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.write_8bit) for write_8bit
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.slave_ack) for slave_ack
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.read_8bit) for read_8bit
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.master_ack) for master_ack
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.master_no_ack) for master_no_ack
    master_i2c_pm.o(i.I2C_ReadBytes_PM) refers to master_i2c_pm.o(i.stop) for stop
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.start) for start
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.write_8bit) for write_8bit
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.slave_ack) for slave_ack
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.read_8bit) for read_8bit
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.master_ack) for master_ack
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.master_no_ack) for master_no_ack
    master_i2c_pm.o(i.I2C_ReadWord_PM) refers to master_i2c_pm.o(i.stop) for stop
    master_i2c_pm.o(i.I2C_WriteBytes_PM) refers to master_i2c_pm.o(i.start) for start
    master_i2c_pm.o(i.I2C_WriteBytes_PM) refers to master_i2c_pm.o(i.write_8bit) for write_8bit
    master_i2c_pm.o(i.I2C_WriteBytes_PM) refers to master_i2c_pm.o(i.slave_ack) for slave_ack
    master_i2c_pm.o(i.I2C_WriteBytes_PM) refers to master_i2c_pm.o(i.stop) for stop
    master_i2c_pm.o(i.I2C_WriteWord_PM) refers to master_i2c_pm.o(i.start) for start
    master_i2c_pm.o(i.I2C_WriteWord_PM) refers to master_i2c_pm.o(i.write_8bit) for write_8bit
    master_i2c_pm.o(i.I2C_WriteWord_PM) refers to master_i2c_pm.o(i.slave_ack) for slave_ack
    master_i2c_pm.o(i.I2C_WriteWord_PM) refers to master_i2c_pm.o(i.stop) for stop
    master_i2c_pm.o(i.master_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.master_ack) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.master_ack) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.master_no_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.master_no_ack) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.master_no_ack) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.read_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.read_8bit) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.read_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    master_i2c_pm.o(i.read_8bit) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.slave_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.slave_ack) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.slave_ack) refers to master_i2c_pm.o(i.stop) for stop
    master_i2c_pm.o(i.slave_ack) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    master_i2c_pm.o(i.slave_ack) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.start) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.start) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.start) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.stop) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.stop) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.stop) refers to master_i2c_pm.o(.data) for CLK_WIDE
    master_i2c_pm.o(i.write_8bit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    master_i2c_pm.o(i.write_8bit) refers to master_i2c_pm.o(i.Delay_ns) for Delay_ns
    master_i2c_pm.o(i.write_8bit) refers to master_i2c_pm.o(.data) for CLK_WIDE
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart3_rx
    usart.o(i.MX_USART3_UART_Init) refers to stm32f2xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for huart3
    usart.o(i.UsartReceive_IDLE) refers to stm32f2xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart.o(i.UsartReceive_IDLE) refers to stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    usart.o(i.UsartReceive_IDLE) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.UsartReceive_IDLE) refers to strstr.o(.text) for strstr
    usart.o(i.UsartReceive_IDLE) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.UsartReceive_IDLE) refers to usart.o(.bss) for hdma_usart3_rx
    usart.o(i.UsartReceive_IDLE) refers to usbd_cdc_if.o(.bss) for UserRxBufferFS
    usart.o(i.UsartReceive_IDLE) refers to usbd_cdc_if.o(.data) for RxFlag
    usart.o(i.UsartReceive_IDLE) refers to main.o(.data) for interface
    usart.o(i.UsartSendCmd) refers to strlen.o(.text) for strlen
    usart.o(i.UsartSendCmd) refers to stm32f2xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.UsartSendCmd) refers to usart.o(.bss) for huart3
    usart.o(i.fputc) refers to stm32f2xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for huart3
    stm32f2xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop) for HAL_PCD_Stop
    stm32f2xx_hal_pcd.o(i.HAL_PCD_DeInit) refers to usbd_conf.o(i.HAL_PCD_MspDeInit) for HAL_PCD_MspDeInit
    stm32f2xx_hal_pcd.o(i.HAL_PCD_DevConnect) refers to stm32f2xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f2xx_hal_pcd.o(i.HAL_PCD_DevDisconnect) refers to stm32f2xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Close) refers to stm32f2xx_ll_usb.o(i.USB_DeactivateEndpoint) for USB_DeactivateEndpoint
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall) refers to stm32f2xx_ll_usb.o(i.USB_EPClearStall) for USB_EPClearStall
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Flush) refers to stm32f2xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Open) refers to stm32f2xx_ll_usb.o(i.USB_ActivateEndpoint) for USB_ActivateEndpoint
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Receive) refers to stm32f2xx_ll_usb.o(i.USB_EP0StartXfer) for USB_EP0StartXfer
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Receive) refers to stm32f2xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f2xx_ll_usb.o(i.USB_EPSetStall) for USB_EPSetStall
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_SetStall) refers to stm32f2xx_ll_usb.o(i.USB_EP0_OutStart) for USB_EP0_OutStart
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) refers to stm32f2xx_ll_usb.o(i.USB_EP0StartXfer) for USB_EP0StartXfer
    stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Transmit) refers to stm32f2xx_ll_usb.o(i.USB_EPStartXfer) for USB_EPStartXfer
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_GetMode) for USB_GetMode
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadDevAllOutEpInterrupt) for USB_ReadDevAllOutEpInterrupt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadDevOutEPInterrupt) for USB_ReadDevOutEPInterrupt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_DataOutStageCallback) for HAL_PCD_DataOutStageCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_EP0_OutStart) for USB_EP0_OutStart
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SetupStageCallback) for HAL_PCD_SetupStageCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadDevAllInEpInterrupt) for USB_ReadDevAllInEpInterrupt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadDevInEPInterrupt) for USB_ReadDevInEPInterrupt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_DataInStageCallback) for HAL_PCD_DataInStageCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_hal_pcd.o(i.PCD_WriteEmptyTxFifo) for PCD_WriteEmptyTxFifo
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResumeCallback) for HAL_PCD_ResumeCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SuspendCallback) for HAL_PCD_SuspendCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ActivateSetup) for USB_ActivateSetup
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_GetDevSpeed) for USB_GetDevSpeed
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ResetCallback) for HAL_PCD_ResetCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to stm32f2xx_ll_usb.o(i.USB_ReadPacket) for USB_ReadPacket
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_SOFCallback) for HAL_PCD_SOFCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback) for HAL_PCD_ISOINIncompleteCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback) for HAL_PCD_ISOOUTIncompleteCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_ConnectCallback) for HAL_PCD_ConnectCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_IRQHandler) refers to usbd_conf.o(i.HAL_PCD_DisconnectCallback) for HAL_PCD_DisconnectCallback
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to usbd_conf.o(i.HAL_PCD_MspInit) for HAL_PCD_MspInit
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f2xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f2xx_ll_usb.o(i.USB_CoreInit) for USB_CoreInit
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f2xx_ll_usb.o(i.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f2xx_ll_usb.o(i.USB_DevInit) for USB_DevInit
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Init) refers to stm32f2xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f2xx_hal_pcd.o(i.HAL_PCD_SetAddress) refers to stm32f2xx_ll_usb.o(i.USB_SetDevAddress) for USB_SetDevAddress
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f2xx_ll_usb.o(i.USB_DevConnect) for USB_DevConnect
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Start) refers to stm32f2xx_ll_usb.o(i.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f2xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f2xx_ll_usb.o(i.USB_StopDevice) for USB_StopDevice
    stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop) refers to stm32f2xx_ll_usb.o(i.USB_DevDisconnect) for USB_DevDisconnect
    stm32f2xx_hal_pcd.o(i.PCD_WriteEmptyTxFifo) refers to stm32f2xx_ll_usb.o(i.USB_WritePacket) for USB_WritePacket
    stm32f2xx_ll_usb.o(i.USB_ActivateDedicatedEndpoint) refers to stm32f2xx_ll_usb.o(.data) for debug
    stm32f2xx_ll_usb.o(i.USB_CoreInit) refers to stm32f2xx_ll_usb.o(i.USB_CoreReset) for USB_CoreReset
    stm32f2xx_ll_usb.o(i.USB_DevConnect) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f2xx_ll_usb.o(i.USB_DevDisconnect) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f2xx_ll_usb.o(i.USB_DevInit) refers to stm32f2xx_ll_usb.o(i.USB_SetDevSpeed) for USB_SetDevSpeed
    stm32f2xx_ll_usb.o(i.USB_DevInit) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_ll_usb.o(i.USB_DevInit) refers to stm32f2xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f2xx_ll_usb.o(i.USB_EPStartXfer) refers to stm32f2xx_ll_usb.o(i.USB_WritePacket) for USB_WritePacket
    stm32f2xx_ll_usb.o(i.USB_HC_StartXfer) refers to stm32f2xx_ll_usb.o(i.USB_DoPing) for USB_DoPing
    stm32f2xx_ll_usb.o(i.USB_HC_StartXfer) refers to stm32f2xx_ll_usb.o(i.USB_WritePacket) for USB_WritePacket
    stm32f2xx_ll_usb.o(i.USB_HostInit) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_ll_usb.o(i.USB_HostInit) refers to stm32f2xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f2xx_ll_usb.o(i.USB_HostInit) refers to stm32f2xx_ll_usb.o(i.USB_DriveVbus) for USB_DriveVbus
    stm32f2xx_ll_usb.o(i.USB_HostInit) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f2xx_ll_usb.o(i.USB_ResetPort) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f2xx_ll_usb.o(i.USB_SetCurrentMode) refers to stm32f2xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32f2xx_ll_usb.o(i.USB_StopDevice) refers to stm32f2xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f2xx_ll_usb.o(i.USB_StopDevice) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_ll_usb.o(i.USB_StopHost) refers to stm32f2xx_ll_usb.o(i.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f2xx_ll_usb.o(i.USB_StopHost) refers to stm32f2xx_ll_usb.o(i.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f2xx_ll_usb.o(i.USB_StopHost) refers to stm32f2xx_ll_usb.o(i.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f2xx_ll_usb.o(i.USB_StopHost) refers to stm32f2xx_ll_usb.o(i.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f2xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f2xx_hal_adc.o(i.ADC_DMAError) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f2xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f2xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f2xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f2xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f2xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f2xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f2xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f2xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f2xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f2xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f2xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f2xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f2xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f2xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f2xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f2xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f2xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f2xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f2xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f2xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f2xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_ITError) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_i2c.o(i.I2C_ITError) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_ITError) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f2xx_hal_i2c.o(i.I2C_ITError) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_ITError) refers to stm32f2xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f2xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f2xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f2xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f2xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f2xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f2xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f2xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_spi.o(i.HAL_SPI_DeInit) refers to mdio.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f2xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f2xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f2xx_hal_spi.o(i.HAL_SPI_Init) refers to mdio.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f2xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f2xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f2xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f2xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f2xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) refers to stm32f2xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAError) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY) for SPI_CheckFlag_BSY
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f2xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f2xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f2xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f2xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f2xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal.o(i.HAL_DeInit) refers to stm32f2xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f2xx_hal.o(i.HAL_Delay) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal.o(i.HAL_GetTick) refers to stm32f2xx_hal.o(.data) for uwTick
    stm32f2xx_hal.o(i.HAL_IncTick) refers to stm32f2xx_hal.o(.data) for uwTick
    stm32f2xx_hal.o(i.HAL_Init) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f2xx_hal.o(i.HAL_Init) refers to stm32f2xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal.o(i.HAL_Init) refers to stm32f2xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f2xx_hal.o(i.HAL_InitTick) refers to stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f2xx_hal.o(i.HAL_InitTick) refers to stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f2xx_hal.o(i.HAL_InitTick) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f2xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f2xx.o(.constdata) for AHBPrescTable
    stm32f2xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f2xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f2xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f2xx.o(.constdata) for APBPrescTable
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f2xx.o(.constdata) for APBPrescTable
    stm32f2xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f2xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f2xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f2xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f2xx_hal_cortex.o(i.NVIC_GetPriorityGrouping) for NVIC_GetPriorityGrouping
    stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(i.NVIC_GetPriorityGrouping) for NVIC_GetPriorityGrouping
    stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f2xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f2xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f2xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f2xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f2xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f2xx_hal_flash.o(.bss) for pFlash
    stm32f2xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f2xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f2xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f2xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f2xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f2xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    stm32f2xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f2xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f2xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f2xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f2xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f2xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f2xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f2xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f2xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f2xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f2xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f2xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f2xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f2xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f2xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f2xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f2xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f2xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f2xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f2xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f2xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f2xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f2xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f2xx_hal_rng.o(i.HAL_RNG_DeInit) refers to rng.o(i.HAL_RNG_MspDeInit) for HAL_RNG_MspDeInit
    stm32f2xx_hal_rng.o(i.HAL_RNG_GenerateRandomNumber) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_rng.o(i.HAL_RNG_GetRandomNumber) refers to stm32f2xx_hal_rng.o(i.HAL_RNG_GenerateRandomNumber) for HAL_RNG_GenerateRandomNumber
    stm32f2xx_hal_rng.o(i.HAL_RNG_IRQHandler) refers to stm32f2xx_hal_rng.o(i.HAL_RNG_ErrorCallback) for HAL_RNG_ErrorCallback
    stm32f2xx_hal_rng.o(i.HAL_RNG_IRQHandler) refers to stm32f2xx_hal_rng.o(i.HAL_RNG_ReadyDataCallback) for HAL_RNG_ReadyDataCallback
    stm32f2xx_hal_rng.o(i.HAL_RNG_Init) refers to rng.o(i.HAL_RNG_MspInit) for HAL_RNG_MspInit
    stm32f2xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f2xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f2xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f2xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f2xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f2xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f2xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f2xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f2xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f2xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f2xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f2xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f2xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f2xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f2xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f2xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f2xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f2xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f2xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f2xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f2xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f2xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f2xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f2xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f2xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f2xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f2xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f2xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f2xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f2xx_hal_uart.o(i.UART_DMAError) refers to stm32f2xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f2xx_hal_uart.o(i.UART_DMAError) refers to stm32f2xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f2xx_hal_uart.o(i.UART_DMAError) refers to stm32f2xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f2xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f2xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f2xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f2xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f2xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f2xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f2xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f2xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f2xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f2xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f2xx_hal_uart.o(i.UART_SetConfig) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f2xx_hal_uart.o(i.UART_SetConfig) refers to stm32f2xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f2xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f2xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f2xx.o(i.SystemCoreClockUpdate) refers to system_stm32f2xx.o(.data) for SystemCoreClock
    system_stm32f2xx.o(i.SystemCoreClockUpdate) refers to system_stm32f2xx.o(.constdata) for AHBPrescTable
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_core.o(i.USBD_DeInit) refers to usbd_conf.o(i.USBD_LL_DeInit) for USBD_LL_DeInit
    usbd_core.o(i.USBD_Init) refers to usbd_conf.o(i.USBD_LL_Init) for USBD_LL_Init
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlContinueSendData) for USBD_CtlContinueSendData
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_ioreq.o(i.USBD_CtlReceiveStatus) for USBD_CtlReceiveStatus
    usbd_core.o(i.USBD_LL_DataInStage) refers to usbd_core.o(i.USBD_RunTestMode) for USBD_RunTestMode
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlContinueRx) for USBD_CtlContinueRx
    usbd_core.o(i.USBD_LL_DataOutStage) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_core.o(i.USBD_LL_Reset) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_ParseSetupRequest) for USBD_ParseSetupRequest
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdDevReq) for USBD_StdDevReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdItfReq) for USBD_StdItfReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_ctlreq.o(i.USBD_StdEPReq) for USBD_StdEPReq
    usbd_core.o(i.USBD_LL_SetupStage) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_core.o(i.USBD_Start) refers to usbd_conf.o(i.USBD_LL_Start) for USBD_LL_Start
    usbd_core.o(i.USBD_Stop) refers to usbd_conf.o(i.USBD_LL_Stop) for USBD_LL_Stop
    usbd_ctlreq.o(i.USBD_ClrFeature) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_ClrFeature) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_CtlError) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_GetConfig) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_GetConfig) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_GetDescriptor) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_GetStatus) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_GetStatus) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_GetString) refers to usbd_ctlreq.o(i.USBD_GetLen) for USBD_GetLen
    usbd_ctlreq.o(i.USBD_SetAddress) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_SetAddress) refers to usbd_conf.o(i.USBD_LL_SetUSBAddress) for USBD_LL_SetUSBAddress
    usbd_ctlreq.o(i.USBD_SetAddress) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_SetClassConfig) for USBD_SetClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_core.o(i.USBD_ClrClassConfig) for USBD_ClrClassConfig
    usbd_ctlreq.o(i.USBD_SetConfig) refers to usbd_ctlreq.o(.data) for cfgidx
    usbd_ctlreq.o(i.USBD_SetFeature) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_GetDescriptor) for USBD_GetDescriptor
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_SetAddress) for USBD_SetAddress
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_SetConfig) for USBD_SetConfig
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_GetConfig) for USBD_GetConfig
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_GetStatus) for USBD_GetStatus
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_SetFeature) for USBD_SetFeature
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_ClrFeature) for USBD_ClrFeature
    usbd_ctlreq.o(i.USBD_StdDevReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_StallEP) for USBD_LL_StallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_ClearStallEP) for USBD_LL_ClearStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_conf.o(i.USBD_LL_IsStallEP) for USBD_LL_IsStallEP
    usbd_ctlreq.o(i.USBD_StdEPReq) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ioreq.o(i.USBD_CtlSendStatus) for USBD_CtlSendStatus
    usbd_ctlreq.o(i.USBD_StdItfReq) refers to usbd_ctlreq.o(i.USBD_CtlError) for USBD_CtlError
    usbd_ioreq.o(i.USBD_CtlContinueRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlContinueSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlPrepareRx) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlReceiveStatus) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_ioreq.o(i.USBD_CtlSendData) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_CtlSendStatus) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_ioreq.o(i.USBD_GetRxCount) refers to usbd_conf.o(i.USBD_LL_GetRxDataSize) for USBD_LL_GetRxDataSize
    usbd_cdc.o(i.USBD_CDC_DataOut) refers to usbd_conf.o(i.USBD_LL_GetRxDataSize) for USBD_LL_GetRxDataSize
    usbd_cdc.o(i.USBD_CDC_DeInit) refers to usbd_conf.o(i.USBD_LL_CloseEP) for USBD_LL_CloseEP
    usbd_cdc.o(i.USBD_CDC_DeInit) refers to malloc.o(i.free) for free
    usbd_cdc.o(i.USBD_CDC_GetDeviceQualifierDescriptor) refers to usbd_cdc.o(.data) for USBD_CDC_DeviceQualifierDesc
    usbd_cdc.o(i.USBD_CDC_GetFSCfgDesc) refers to usbd_cdc.o(.data) for USBD_CDC_CfgFSDesc
    usbd_cdc.o(i.USBD_CDC_GetHSCfgDesc) refers to usbd_cdc.o(.data) for USBD_CDC_CfgHSDesc
    usbd_cdc.o(i.USBD_CDC_GetOtherSpeedCfgDesc) refers to usbd_cdc.o(.data) for USBD_CDC_OtherSpeedCfgDesc
    usbd_cdc.o(i.USBD_CDC_Init) refers to usbd_conf.o(i.USBD_LL_OpenEP) for USBD_LL_OpenEP
    usbd_cdc.o(i.USBD_CDC_Init) refers to malloc.o(i.malloc) for malloc
    usbd_cdc.o(i.USBD_CDC_Init) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_cdc.o(i.USBD_CDC_ReceivePacket) refers to usbd_conf.o(i.USBD_LL_PrepareReceive) for USBD_LL_PrepareReceive
    usbd_cdc.o(i.USBD_CDC_Setup) refers to usbd_ioreq.o(i.USBD_CtlSendData) for USBD_CtlSendData
    usbd_cdc.o(i.USBD_CDC_Setup) refers to usbd_ioreq.o(i.USBD_CtlPrepareRx) for USBD_CtlPrepareRx
    usbd_cdc.o(i.USBD_CDC_Setup) refers to usbd_cdc.o(.data) for ifalt
    usbd_cdc.o(i.USBD_CDC_TransmitPacket) refers to usbd_conf.o(i.USBD_LL_Transmit) for USBD_LL_Transmit
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_Init) for USBD_CDC_Init
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_DeInit) for USBD_CDC_DeInit
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_Setup) for USBD_CDC_Setup
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_EP0_RxReady) for USBD_CDC_EP0_RxReady
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_DataIn) for USBD_CDC_DataIn
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_DataOut) for USBD_CDC_DataOut
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_GetHSCfgDesc) for USBD_CDC_GetHSCfgDesc
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_GetFSCfgDesc) for USBD_CDC_GetFSCfgDesc
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_GetOtherSpeedCfgDesc) for USBD_CDC_GetOtherSpeedCfgDesc
    usbd_cdc.o(.data) refers to usbd_cdc.o(i.USBD_CDC_GetDeviceQualifierDescriptor) for USBD_CDC_GetDeviceQualifierDescriptor
    startup_stm32f205xx.o(RESET) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    startup_stm32f205xx.o(RESET) refers to startup_stm32f205xx.o(.text) for Reset_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f205xx.o(RESET) refers to stm32f2xx_it.o(i.OTG_FS_IRQHandler) for OTG_FS_IRQHandler
    startup_stm32f205xx.o(.text) refers to system_stm32f2xx.o(i.SystemInit) for SystemInit
    startup_stm32f205xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    capi.o(i._capi_drop_xbar_ports) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i._capi_drop_xbar_ports) refers to host_chip_wrapper.o(i.host_get_chip_info) for host_get_chip_info
    capi.o(i._capi_drop_xbar_ports) refers to capi.o(i._capi_set_port) for _capi_set_port
    capi.o(i._capi_set_port) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i._capi_set_port) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i._capi_set_port) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i._capi_set_port) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_command_request) refers to host_diag_util.o(i.host_diag_cmd_sanity_checker) for host_diag_cmd_sanity_checker
    capi.o(i.capi_command_request) refers to host_to_chip_ipc.o(i.intf_capi2fw_command_Handler) for intf_capi2fw_command_Handler
    capi.o(i.capi_disable_all_regs) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_disable_all_regs) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_disable_all_regs) refers to host_power_util.o(i.host_util_ana_disable_all_regs) for host_util_ana_disable_all_regs
    capi.o(i.capi_disable_avdd_slave) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_disable_avdd_slave) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_disable_avdd_slave) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_disable_avdd_slave) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_disable_avdd_slave) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_disable_avdd_slave) refers to host_power_util.o(i.top_supspend_resume) for top_supspend_resume
    capi.o(i.capi_disable_avdd_slave) refers to host_power_util.o(i.chal_ana_reg_disable_avdd_slave) for chal_ana_reg_disable_avdd_slave
    capi.o(i.capi_disable_avdd_slave) refers to host_power_util.o(i.chal_ana_reg_enable_avdd_slave) for chal_ana_reg_enable_avdd_slave
    capi.o(i.capi_disable_avs_slaves) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_disable_avs_slaves) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_disable_avs_slaves) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_disable_avs_slaves) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_disable_avs_slaves) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_disable_avs_slaves) refers to host_power_util.o(i.top_supspend_resume) for top_supspend_resume
    capi.o(i.capi_disable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_disable_avs_1_slaves) for chal_ana_reg_disable_avs_1_slaves
    capi.o(i.capi_disable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_disable_avs_2_slaves) for chal_ana_reg_disable_avs_2_slaves
    capi.o(i.capi_disable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_disable_avs_3_slaves) for chal_ana_reg_disable_avs_3_slaves
    capi.o(i.capi_disable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_disable_avs_slaves) for chal_ana_reg_disable_avs_slaves
    capi.o(i.capi_disable_vddm_slave) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_disable_vddm_slave) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_disable_vddm_slave) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_disable_vddm_slave) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_disable_vddm_slave) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_disable_vddm_slave) refers to host_power_util.o(i.top_supspend_resume) for top_supspend_resume
    capi.o(i.capi_disable_vddm_slave) refers to host_power_util.o(i.chal_ana_reg_disable_vddm_slave) for chal_ana_reg_disable_vddm_slave
    capi.o(i.capi_disable_vddm_slave) refers to host_power_util.o(i.chal_ana_reg_enable_vddm_slave) for chal_ana_reg_enable_vddm_slave
    capi.o(i.capi_enable_avs_slaves) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_enable_avs_slaves) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_enable_avs_slaves) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_enable_avs_slaves) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_enable_avs_slaves) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.top_supspend_resume) for top_supspend_resume
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_enable_avs_slave1) for chal_ana_reg_enable_avs_slave1
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_enable_avs_slave2) for chal_ana_reg_enable_avs_slave2
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_enable_avs_slave3) for chal_ana_reg_enable_avs_slave3
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_enable_avs_slave4) for chal_ana_reg_enable_avs_slave4
    capi.o(i.capi_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_enable_avs_slaves) for chal_ana_reg_enable_avs_slaves
    capi.o(i.capi_get_avs_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_get_avs_config) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_get_chip_command) refers to host_gpio_util.o(i.host_get_gpio_info) for host_get_gpio_info
    capi.o(i.capi_get_chip_command) refers to host_chip_wrapper.o(i.host_get_lpm_st) for host_get_lpm_st
    capi.o(i.capi_get_chip_command) refers to host_chip_wrapper.o(i.host_get_spi_info) for host_get_spi_info
    capi.o(i.capi_get_chip_command) refers to host_chip_wrapper.o(i.host_get_capi_feature_info) for host_get_capi_feature_info
    capi.o(i.capi_get_chip_command) refers to host_lw_wrapper.o(i.host_lw_get_pll_configuration) for host_lw_get_pll_configuration
    capi.o(i.capi_get_chip_info) refers to host_chip_wrapper.o(i.host_get_chip_info) for host_get_chip_info
    capi.o(i.capi_get_chip_status) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_get_chip_status) refers to capi.o(i.capi_get_download_status) for capi_get_download_status
    capi.o(i.capi_get_chip_status) refers to host_avs.o(i.host_get_avs_status) for host_get_avs_status
    capi.o(i.capi_get_chip_status) refers to host_chip_wrapper.o(i.util_wait_for_uc_ready) for util_wait_for_uc_ready
    capi.o(i.capi_get_config) refers to memseta.o(.text) for __aeabi_memclr4
    capi.o(i.capi_get_config) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_get_config) refers to memcpya.o(.text) for __aeabi_memcpy
    capi.o(i.capi_get_config) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_download_crc_status) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_get_download_crc_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_get_download_status) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_get_download_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_get_download_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_get_firmware_status) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_get_firmware_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_get_firmware_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_get_lane_config_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi.o(i.capi_get_lane_config_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_lane_ctrl_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_lane_info) refers to memcpya.o(.text) for __aeabi_memcpy
    capi.o(i.capi_get_lane_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_polarity) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_port_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_status) refers to host_chip_wrapper.o(i.host_get_gpr_lane_status) for host_get_gpr_lane_status
    capi.o(i.capi_get_status_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi.o(i.capi_get_status_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_get_temperture_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_get_temperture_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_get_temperture_status) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_get_voltage_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_get_voltage_status) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_get_voltage_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_override_upgrade_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_read_register) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_reset) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_reset) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_reset) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_reset) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_set_avs_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_set_chip_command) refers to host_gpio_util.o(i.host_set_gpio_info) for host_set_gpio_info
    capi.o(i.capi_set_chip_command) refers to host_chip_wrapper.o(i.host_set_spi_info) for host_set_spi_info
    capi.o(i.capi_set_chip_command) refers to host_chip_wrapper.o(i.host_set_capi_feature_info) for host_set_capi_feature_info
    capi.o(i.capi_set_chip_command) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_set_config) refers to memseta.o(.text) for __aeabi_memclr4
    capi.o(i.capi_set_config) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_set_config) refers to capi.o(i._capi_drop_xbar_ports) for _capi_drop_xbar_ports
    capi.o(i.capi_set_config) refers to memcpya.o(.text) for __aeabi_memcpy
    capi.o(i.capi_set_config) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_set_config) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_set_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_set_internal_regulator_voltage) refers to host_power_util.o(i.host_set_internal_regulator_voltage) for host_set_internal_regulator_voltage
    capi.o(i.capi_set_lane_config_info) refers to memseta.o(.text) for __aeabi_memclr4
    capi.o(i.capi_set_lane_config_info) refers to capi.o(i.capi_get_chip_info) for capi_get_chip_info
    capi.o(i.capi_set_lane_config_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_set_lane_config_info) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_set_lane_config_info) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_set_lane_config_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_set_lane_config_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi.o(i.capi_set_lane_config_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_set_lane_ctrl_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_set_polarity) refers to capi.o(i.capi_command_request) for capi_command_request
    capi.o(i.capi_set_port) refers to capi.o(i._capi_is_mixed_mode_xbar_required) for _capi_is_mixed_mode_xbar_required
    capi.o(i.capi_set_port) refers to capi.o(i._capi_drop_xbar_ports) for _capi_drop_xbar_ports
    capi.o(i.capi_set_port) refers to capi.o(i._capi_set_port) for _capi_set_port
    capi.o(i.capi_set_regulator_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_set_regulator_info) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_set_regulator_info) refers to host_power_util.o(i.host_util_ana_disable_all_regs) for host_util_ana_disable_all_regs
    capi.o(i.capi_set_voltage_config) refers to common_util.o(i.util_memset) for util_memset
    capi.o(i.capi_set_voltage_config) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi.o(i.capi_set_voltage_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi.o(i.capi_set_voltage_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi.o(i.capi_set_voltage_config) refers to hr_time.o(i.delay_ms) for delay_ms
    capi.o(i.capi_set_voltage_config) refers to host_power_util.o(i.chal_ana_avs_set_volt) for chal_ana_avs_set_volt
    capi.o(i.capi_write_register) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    capi_custom.o(i.capi_custom_command_request) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    capi_custom.o(i.capi_custom_command_request) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    capi_custom.o(i.capi_custom_command_request) refers to dsp_utils.o(i.lw_util_init_lane_hw_base_addr) for lw_util_init_lane_hw_base_addr
    capi_custom.o(i.capi_custom_command_request) refers to host_lw_wrapper.o(i.host_lw_tx_fir_7tap_prog_28_lut) for host_lw_tx_fir_7tap_prog_28_lut
    capi_custom.o(i.capi_custom_command_request) refers to host_lw_wrapper.o(i.host_lw_tx_fir_4tap_prog_256_lut) for host_lw_tx_fir_4tap_prog_256_lut
    capi_custom.o(i.capi_custom_command_request) refers to host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) for host_lw_tx_fir_7tap_read_28_lut
    capi_custom.o(i.capi_custom_command_request) refers to host_lw_wrapper.o(i.host_lw_tx_fir_4tap_read_256_lut) for host_lw_tx_fir_4tap_read_256_lut
    capi_diag.o(i._chk_chip_mode_fec_st_support) refers to capi_diag.o(i._chk_rprt_mode_fec_st_support) for _chk_rprt_mode_fec_st_support
    capi_diag.o(i._chk_chip_mode_fec_st_support) refers to capi_diag.o(i._chk_rtmr_mode_fec_st_support) for _chk_rtmr_mode_fec_st_support
    capi_diag.o(i.capi_clear_fec_mon) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_clear_fec_mon) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi_diag.o(i.capi_clear_fec_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    capi_diag.o(i.capi_clear_fec_mon) refers to capi_diag.o(i._chk_chip_mode_fec_st_support) for _chk_chip_mode_fec_st_support
    capi_diag.o(i.capi_clear_fec_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) for host_diag_cw_fec_stat_clear
    capi_diag.o(i.capi_diag_command_request_info) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_diag_command_request_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi_diag.o(i.capi_diag_command_request_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.host_get_memory_payload) for host_get_memory_payload
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.sanity_checker_mpi_config_info) for sanity_checker_mpi_config_info
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_set_mpi_config) for host_set_mpi_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_mpi_config) for host_get_mpi_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_mpi_state) for host_get_mpi_state
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.dsp_mission_mpi_cfg_sanity_checker) for dsp_mission_mpi_cfg_sanity_checker
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_set_mpi_dynamic_config) for host_set_mpi_dynamic_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_mpi_dynamic_config) for host_get_mpi_dynamic_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_dynamic_mpi_state) for host_get_dynamic_mpi_state
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.dsp_traffic_mode_switch_detect_cfg_sanity_checker) for dsp_traffic_mode_switch_detect_cfg_sanity_checker
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_set_tmsd_config) for host_set_tmsd_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_tmsd_config) for host_get_tmsd_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_tmsd_state) for host_get_tmsd_state
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.dsp_mpi_canceller_cfg_sanity_checker) for dsp_mpi_canceller_cfg_sanity_checker
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_set_mpi_canceller_config) for host_set_mpi_canceller_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_mpi_canceller_config) for host_get_mpi_canceller_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag_util.o(i.dsp_hw_gain2_adapt_cfg_sanity_checker) for dsp_hw_gain2_adapt_cfg_sanity_checker
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_set_hw_gain2_a_config) for host_set_hw_gain2_a_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_get_hw_gain2_a_config) for host_get_hw_gain2_a_config
    capi_diag.o(i.capi_diag_command_request_info) refers to host_diag.o(i.host_dump_fw_events) for host_dump_fw_events
    capi_diag.o(i.capi_diag_command_request_info) refers to stdout.o(.data) for __stdout
    capi_diag.o(i.capi_fec_ber_cmis_latch_release) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_fec_ber_cmis_latch_release) refers to capi_diag.o(i.capi_get_fec_info) for capi_get_fec_info
    capi_diag.o(i.capi_fec_ber_cmis_latch_release) refers to capi_diag.o(i.capi_clear_fec_mon) for capi_clear_fec_mon
    capi_diag.o(i.capi_fec_ber_cmis_latch_release) refers to host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) for host_diag_cw_calculate_ber_between_latch
    capi_diag.o(i.capi_fec_ber_cmis_latch_request) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_fec_ber_cmis_latch_request) refers to capi_diag.o(i.capi_clear_fec_mon) for capi_clear_fec_mon
    capi_diag.o(i.capi_fec_ber_cmis_latch_request) refers to capi_diag.o(i.capi_get_fec_info) for capi_get_fec_info
    capi_diag.o(i.capi_get_fec_info) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_get_fec_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi_diag.o(i.capi_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    capi_diag.o(i.capi_get_fec_info) refers to capi_diag.o(i._chk_chip_mode_fec_st_support) for _chk_chip_mode_fec_st_support
    capi_diag.o(i.capi_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) for host_diag_cw_get_fec_info
    capi_diag.o(i.capi_get_usr_diagnostics) refers to host_diag.o(i.host_lw_get_usr_cmis_diagnostics) for host_lw_get_usr_cmis_diagnostics
    capi_diag.o(i.capi_get_usr_diagnostics) refers to host_diag.o(i.host_lw_get_usr_diagnostics) for host_lw_get_usr_diagnostics
    capi_diag.o(i.capi_init_fec_mon) refers to common_util.o(i.util_memset) for util_memset
    capi_diag.o(i.capi_init_fec_mon) refers to common_util.o(i.util_memcpy) for util_memcpy
    capi_diag.o(i.capi_init_fec_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    capi_diag.o(i.capi_init_fec_mon) refers to capi_diag.o(i._chk_chip_mode_fec_st_support) for _chk_chip_mode_fec_st_support
    capi_diag.o(i.capi_init_fec_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) for host_diag_cw_fec_stat_init
    capi_diag.o(i.capi_init_fec_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) for host_diag_cw_fec_stat_deinit
    capi_test.o(i.capi_clear_prbs_status) refers to host_fec_prbs.o(i.host_fec_clear_prbs_status) for host_fec_clear_prbs_status
    capi_test.o(i.capi_clear_prbs_status) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi_test.o(i.capi_clear_prbs_status) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_get_loopback) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_get_prbs_info) refers to host_fec_prbs.o(i.host_fec_get_prbs_gen) for host_fec_get_prbs_gen
    capi_test.o(i.capi_get_prbs_info) refers to host_fec_prbs.o(i.host_fec_get_prbs_mon) for host_fec_get_prbs_mon
    capi_test.o(i.capi_get_prbs_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi_test.o(i.capi_get_prbs_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_get_prbs_status) refers to host_fec_prbs.o(i.host_fec_get_prbs_status) for host_fec_get_prbs_status
    capi_test.o(i.capi_get_prbs_status) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi_test.o(i.capi_get_prbs_status) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_prbs_inject_error) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_set_loopback) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_set_prbs_info) refers to host_fec_prbs.o(i.host_fec_set_prbs_gen) for host_fec_set_prbs_gen
    capi_test.o(i.capi_set_prbs_info) refers to host_fec_prbs.o(i.host_fec_set_prbs_mon) for host_fec_set_prbs_mon
    capi_test.o(i.capi_set_prbs_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    capi_test.o(i.capi_set_prbs_info) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_test_set_command) refers to capi.o(i.capi_command_request) for capi_command_request
    capi_test.o(i.capi_test_set_command) refers to host_lw_wrapper.o(i.host_lw_set_snr_threshold) for host_lw_set_snr_threshold
    capi_test.o(i.capi_test_set_command) refers to host_lw_wrapper.o(i.host_lw_get_snr_threshold) for host_lw_get_snr_threshold
    capi_test.o(i.capi_test_set_command) refers to host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) for host_dsp_set_pll_fracn_dynamic_adjust
    capi_test.o(i.capi_test_set_command) refers to host_lw_wrapper.o(i.host_dsp_get_pll_fracn_dynamic_adjust) for host_dsp_get_pll_fracn_dynamic_adjust
    host_avs.o(i.host_get_avs_status) refers to common_util.o(i.util_memset) for util_memset
    host_avs.o(i.host_get_avs_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_avs.o(i.host_get_avs_status) refers to hr_time.o(i.delay_ms) for delay_ms
    host_chip_wrapper.o(i.host_get_capi_feature_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_chip_wrapper.o(i.host_get_capi_feature_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_get_chip_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_get_gpr_lane_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_chip_wrapper.o(i.host_get_gpr_lane_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_get_gpr_lane_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_chip_wrapper.o(i.host_get_gpr_lane_status) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_chip_wrapper.o(i.host_get_gpr_lane_status) refers to printfa.o(i.__0printf) for __2printf
    host_chip_wrapper.o(i.host_get_lpm_st) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_get_spi_info) refers to memseta.o(.text) for __aeabi_memclr4
    host_chip_wrapper.o(i.host_get_spi_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_chip_wrapper.o(i.host_get_spi_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_set_capi_feature_info) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_chip_wrapper.o(i.host_set_capi_feature_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_set_capi_feature_info) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_chip_wrapper.o(i.host_set_spi_info) refers to memseta.o(.text) for __aeabi_memclr4
    host_chip_wrapper.o(i.host_set_spi_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_chip_wrapper.o(i.host_set_spi_info) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.host_set_spi_info) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_chip_wrapper.o(i.util_wait_for_uc_ready) refers to common_util.o(i.util_memset) for util_memset
    host_chip_wrapper.o(i.util_wait_for_uc_ready) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_chip_wrapper.o(i.util_wait_for_uc_ready) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_wrapper.o(i.util_wait_for_uc_ready) refers to hr_time.o(i.delay_ms) for delay_ms
    host_chip_wrapper.o(i.util_wait_for_uc_ready) refers to printfa.o(i.__0printf) for __2printf
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to common_util.o(i.signext) for signext
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to f2d.o(.text) for __aeabi_f2d
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to printfa.o(i.__0fprintf) for __2fprintf
    host_diag.o(i._lw_decode_fw_cu_rx_event) refers to host_diag.o(.data) for fsm_wo_lt_cu_rstr
    host_diag.o(i.host_dump_fw_events) refers to printfa.o(i.__0fprintf) for __2fprintf
    host_diag.o(i.host_dump_fw_events) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_dump_fw_events) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_dump_fw_events) refers to host_diag.o(i.host_get_event_type_string) for host_get_event_type_string
    host_diag.o(i.host_dump_fw_events) refers to host_diag.o(i.host_lw_calculate_pam_snr) for host_lw_calculate_pam_snr
    host_diag.o(i.host_dump_fw_events) refers to host_diag.o(i._lw_decode_fw_cu_rx_event) for _lw_decode_fw_cu_rx_event
    host_diag.o(i.host_dump_fw_events) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag.o(i.host_dump_fw_events) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag.o(i.host_dump_fw_events) refers to f2d.o(.text) for __aeabi_f2d
    host_diag.o(i.host_dump_fw_events) refers to common_util.o(i.signext) for signext
    host_diag.o(i.host_get_dynamic_mpi_state) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag.o(i.host_get_dynamic_mpi_state) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_dynamic_mpi_state) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_dynamic_mpi_state) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_get_hw_gain2_a_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_hw_gain2_a_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_mpi_canceller_config) refers to dsp_utils.o(i.lw_util_init_lane_hw_base_addr) for lw_util_init_lane_hw_base_addr
    host_diag.o(i.host_get_mpi_canceller_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_mpi_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_mpi_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_mpi_config) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag.o(i.host_get_mpi_config) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag.o(i.host_get_mpi_dynamic_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_mpi_dynamic_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_mpi_state) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag.o(i.host_get_mpi_state) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_mpi_state) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_mpi_state) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_get_mpi_state) refers to dflti.o(.text) for __aeabi_i2d
    host_diag.o(i.host_get_mpi_state) refers to sqrt.o(i.sqrt) for sqrt
    host_diag.o(i.host_get_mpi_state) refers to d2f.o(.text) for __aeabi_d2f
    host_diag.o(i.host_get_mpi_state) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag.o(i.host_get_mpi_state) refers to dfltui.o(.text) for __aeabi_ui2d
    host_diag.o(i.host_get_mpi_state) refers to fmul.o(.text) for __aeabi_fmul
    host_diag.o(i.host_get_mpi_state) refers to fadd.o(.text) for __aeabi_fadd
    host_diag.o(i.host_get_mpi_state) refers to f2d.o(.text) for __aeabi_f2d
    host_diag.o(i.host_get_mpi_state) refers to dmul.o(.text) for __aeabi_dmul
    host_diag.o(i.host_get_mpi_state) refers to dadd.o(.text) for __aeabi_dsub
    host_diag.o(i.host_get_mpi_state) refers to fflti.o(.text) for __aeabi_i2f
    host_diag.o(i.host_get_tmsd_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_tmsd_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_tmsd_state) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag.o(i.host_get_tmsd_state) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_get_tmsd_state) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_get_tmsd_state) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_lw_calculate_pam_snr) refers to dfltui.o(.text) for __aeabi_ui2d
    host_diag.o(i.host_lw_calculate_pam_snr) refers to ddiv.o(.text) for __aeabi_ddiv
    host_diag.o(i.host_lw_calculate_pam_snr) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    host_diag.o(i.host_lw_calculate_pam_snr) refers to log10.o(i.log10) for log10
    host_diag.o(i.host_lw_calculate_pam_snr) refers to dmul.o(.text) for __aeabi_dmul
    host_diag.o(i.host_lw_calculate_pam_snr) refers to dadd.o(.text) for __aeabi_dsub
    host_diag.o(i.host_lw_get_asnr_from_gp) refers to memseta.o(.text) for __aeabi_memclr4
    host_diag.o(i.host_lw_get_asnr_from_gp) refers to common_util.o(i.util_memset) for util_memset
    host_diag.o(i.host_lw_get_asnr_from_gp) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_lw_get_asnr_from_gp) refers to host_diag_util.o(i.host_get_memory_payload) for host_get_memory_payload
    host_diag.o(i.host_lw_get_snr_from_gp) refers to memseta.o(.text) for __aeabi_memclr4
    host_diag.o(i.host_lw_get_snr_from_gp) refers to common_util.o(i.util_memset) for util_memset
    host_diag.o(i.host_lw_get_snr_from_gp) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_lw_get_snr_from_gp) refers to host_diag_util.o(i.host_get_memory_payload) for host_get_memory_payload
    host_diag.o(i.host_lw_get_snr_from_gp) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to common_util.o(i.util_memset) for util_memset
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to capi.o(i.capi_command_request) for capi_command_request
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to f2d.o(.text) for __aeabi_f2d
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to sqrt.o(i.sqrt) for sqrt
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to d2f.o(.text) for __aeabi_d2f
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to fadd.o(.text) for __aeabi_fadd
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to cfcmple.o(.text) for __aeabi_cfcmpeq
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to log10.o(i.log10) for log10
    host_diag.o(i.host_lw_get_usr_cmis_diagnostics) refers to dmul.o(.text) for __aeabi_dmul
    host_diag.o(i.host_lw_get_usr_diagnostics) refers to host_diag.o(i.host_lw_get_snr_from_gp) for host_lw_get_snr_from_gp
    host_diag.o(i.host_lw_get_usr_diagnostics) refers to host_diag.o(i.host_lw_get_asnr_from_gp) for host_lw_get_asnr_from_gp
    host_diag.o(i.host_set_hw_gain2_a_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_set_hw_gain2_a_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_set_hw_gain2_a_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_set_mpi_canceller_config) refers to dsp_utils.o(i.lw_util_init_lane_hw_base_addr) for lw_util_init_lane_hw_base_addr
    host_diag.o(i.host_set_mpi_canceller_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_set_mpi_canceller_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_set_mpi_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_set_mpi_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_set_mpi_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_set_mpi_config) refers to f2d.o(.text) for __aeabi_f2d
    host_diag.o(i.host_set_mpi_config) refers to dadd.o(.text) for __aeabi_dadd
    host_diag.o(i.host_set_mpi_config) refers to dmul.o(.text) for __aeabi_dmul
    host_diag.o(i.host_set_mpi_config) refers to dfixui.o(.text) for __aeabi_d2uiz
    host_diag.o(i.host_set_mpi_dynamic_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_set_mpi_dynamic_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_set_mpi_dynamic_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag.o(i.host_set_tmsd_config) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_diag.o(i.host_set_tmsd_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag.o(i.host_set_tmsd_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_clr_fault_status) for host_diag_cw_rtmr_rcv_pfifo_clsn_clr_fault_status
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_am_los_lock_clr_status) for host_diag_cw_rtmr_fec_am_los_lock_clr_status
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xdec_gbox_collision_clr_fault_status) for host_diag_cw_rtmr_xdec_gbox_collision_clr_fault_status
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xenc_gbox_collision_clr_fault_status) for host_diag_cw_rtmr_xenc_gbox_collision_clr_fault_status
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_clr_fault_status) for host_diag_cw_rtmr_tmt_pfifo_clsn_clr_fault_status
    host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_clr_status) for host_diag_cw_rtmr_fec_los_sync_fault_clr_status
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to dfltui.o(.text) for __aeabi_ui2d
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to dfltul.o(.text) for __aeabi_ul2d
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to ddiv.o(.text) for __aeabi_ddiv
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to dflti.o(.text) for __aeabi_i2d
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to log10.o(i.log10) for log10
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to pow.o(i.pow) for pow
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to dmul.o(.text) for __aeabi_dmul
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to dadd.o(.text) for __aeabi_dadd
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to ffltui.o(.text) for __aeabi_ui2f
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to ffltul.o(.text) for __aeabi_ul2f
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to fmul.o(.text) for __aeabi_fmul
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to fadd.o(.text) for __aeabi_fadd
    host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) refers to d2f.o(.text) for __aeabi_d2f
    host_diag_fec_statistics.o(i._diag_cw_fec_prbs_calculate_ber) refers to memseta.o(.text) for __aeabi_memclr4
    host_diag_fec_statistics.o(i._diag_cw_fec_prbs_calculate_ber) refers to host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_accumulate_err_count) for _diag_cw_fec_error_analyzer_accumulate_err_count
    host_diag_fec_statistics.o(i._diag_cw_fec_prbs_calculate_ber) refers to host_diag_fec_statistics.o(i._diag_cw_fec_error_analyzer_compute_proj) for _diag_cw_fec_error_analyzer_compute_proj
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_get_fault_status) for host_diag_cw_rtmr_rcv_pfifo_clsn_get_fault_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xdec_gbox_collision_get_fault_status) for host_diag_cw_rtmr_xdec_gbox_collision_get_fault_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xenc_gbox_collision_get_fault_status) for host_diag_cw_rtmr_xenc_gbox_collision_get_fault_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_get_fault_status) for host_diag_cw_rtmr_tmt_pfifo_clsn_get_fault_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_am_los_lock_get_status) for host_diag_cw_rtmr_fec_am_los_lock_get_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_sync_status) for host_diag_cw_rtmr_fec_sync_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_get_status) for host_diag_cw_rtmr_fec_los_sync_fault_get_status
    host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) refers to host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) for _diag_cw_clear_fec_rtmr_info
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to dflti.o(.text) for __aeabi_i2d
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to dfltul.o(.text) for __aeabi_ul2d
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to ddiv.o(.text) for __aeabi_ddiv
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to dadd.o(.text) for __aeabi_dadd
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to uldiv.o(.text) for __aeabi_uldivmod
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to cdcmple.o(.text) for __aeabi_cdcmple
    host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch) refers to dfltui.o(.text) for __aeabi_ui2d
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control) for host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_suspend_fw_fsm) for host_diag_cw_suspend_fw_fsm
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_clk_gate_control) for host_diag_cw_rtmr_get_fec_clk_gate_control
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_set_fec_clk_gate_control) for host_diag_cw_rtmr_set_fec_clk_gate_control
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) for host_diag_cw_get_bbaddr_kp4deca
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_clr_all) for host_diag_cw_rtmr_kpr4fec_dec_stat_clr_all
    host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_clr_all) for host_diag_cw_rtmr_kpr4fec_dec_stat_md_clr_all
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) for host_diag_cw_clear_fec_stats
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_clear) refers to host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) for _diag_cw_clear_fec_rtmr_info
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) for host_diag_cw_rptr_fec_stats_mon_config_handler
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) for host_diag_cw_get_bbaddr_kp4deca
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_disable) for host_diag_cw_rtmr_kpr4fec_dec_stat_disable
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) for host_diag_cw_clear_fec_stats
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_deinit) refers to host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) for _diag_cw_clear_fec_rtmr_info
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) for host_diag_cw_rptr_fec_stats_mon_config_handler
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) for host_diag_cw_get_bbaddr_kp4deca
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_kpr4fec_dec_stat) for host_diag_cw_rtmr_get_kpr4fec_dec_stat
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_clear_fec_stats) for host_diag_cw_clear_fec_stats
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_enable) for host_diag_cw_rtmr_kpr4fec_dec_stat_enable
    host_diag_fec_statistics.o(i.host_diag_cw_fec_stat_init) refers to host_diag_fec_statistics.o(i._diag_cw_clear_fec_rtmr_info) for _diag_cw_clear_fec_rtmr_info
    host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) refers to ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) for ml_cw_get_quad_swap_enabled
    host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to host_test.o(i.host_test_get_port_lane_mask) for host_test_get_port_lane_mask
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to host_diag_fec_statistics.o(.constdata) for fec_port_config1_regs
    host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) refers to chip_config.o(.constdata) for ports
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_sync_status) for host_diag_cw_rtmr_fec_sync_status
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control) for host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) for host_diag_cw_get_bbaddr_kp4deca
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_kpr4fec_dec_stat) for host_diag_cw_rtmr_get_kpr4fec_dec_stat
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_latch_all) for host_diag_cw_rtmr_kpr4fec_dec_stat_md_latch_all
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_cnt_get) for host_diag_cw_rtmr_kpr4fec_dec_cnt_get
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i._diag_cw_get_fec_rtmr_info) for _diag_cw_get_fec_rtmr_info
    host_diag_fec_statistics.o(i.host_diag_cw_get_fec_info) refers to host_diag_fec_statistics.o(i._diag_cw_fec_prbs_calculate_ber) for _diag_cw_fec_prbs_calculate_ber
    host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) refers to memseta.o(.text) for __aeabi_memclr4
    host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) for ml_cw_rtmr_gen_cw_mode_param
    host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) refers to common_util.o(i.util_memset) for util_memset
    host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) refers to ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) for ml_cw_get_quad_swap_enabled
    host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) refers to memseta.o(.text) for __aeabi_memclr4
    host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) refers to capi_test.o(i.capi_get_prbs_info) for capi_get_prbs_info
    host_diag_fec_statistics.o(i.host_diag_cw_rptr_fec_stats_mon_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_set_rptr_fec_monitor) for host_diag_set_rptr_fec_monitor
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_am_los_lock_clr_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_am_los_lock_get_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_clr_status) refers to host_diag_fec_statistics.o(i._get_cfg_list_array_idx) for _get_cfg_list_array_idx
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_clr_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_clr_status) refers to host_diag_fec_statistics.o(.constdata) for .constdata
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_get_status) refers to host_diag_fec_statistics.o(i._get_cfg_list_array_idx) for _get_cfg_list_array_idx
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_get_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_los_sync_fault_get_status) refers to host_diag_fec_statistics.o(.constdata) for .constdata
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_sync_status) refers to host_diag_fec_statistics.o(i._get_cfg_list_array_idx) for _get_cfg_list_array_idx
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_sync_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_fec_sync_status) refers to host_diag_fec_statistics.o(.constdata) for .constdata
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control) refers to host_diag_fec_statistics.o(i.host_diag_get_lowest_index_from_mask) for host_diag_get_lowest_index_from_mask
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_fec_dec_err_cnt_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_kpr4fec_dec_stat) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_cnt_get) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_cnt_get) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_cnt_get) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_clr_all) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_clr_all) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_disable) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_disable) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_enable) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_enable) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_clr_all) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_clr_all) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_latch_all) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_kpr4fec_dec_stat_md_latch_all) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_clr_fault_status) refers to host_diag_fec_statistics.o(i.host_cw_rtmr_rcv_pfifo_get_lane_mask) for host_cw_rtmr_rcv_pfifo_get_lane_mask
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_clr_fault_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_get_fault_status) refers to host_diag_fec_statistics.o(i.host_cw_rtmr_rcv_pfifo_get_lane_mask) for host_cw_rtmr_rcv_pfifo_get_lane_mask
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_rcv_pfifo_clsn_get_fault_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_set_fec_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_set_fec_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_clr_fault_status) refers to host_diag_fec_statistics.o(i.host_cw_rtmr_tmt_pfifo_get_lane_mask) for host_cw_rtmr_tmt_pfifo_get_lane_mask
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_clr_fault_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_get_fault_status) refers to host_diag_fec_statistics.o(i.host_cw_rtmr_tmt_pfifo_get_lane_mask) for host_cw_rtmr_tmt_pfifo_get_lane_mask
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_tmt_pfifo_clsn_get_fault_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xdec_gbox_collision_clr_fault_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xdec_gbox_collision_get_fault_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xenc_gbox_collision_clr_fault_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_rtmr_xenc_gbox_collision_get_fault_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_suspend_fw_fsm) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_suspend_fw_fsm) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_diag_fec_statistics.o(i.host_diag_cw_suspend_fw_fsm) refers to hr_time.o(i.delay_ms) for delay_ms
    host_diag_fec_statistics.o(i.host_diag_cw_suspend_fw_fsm) refers to host_diag_fec_statistics.o(.constdata) for .constdata
    host_diag_fec_statistics.o(i.host_diag_set_rptr_fec_monitor) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_fec_statistics.o(i.host_diag_set_rptr_fec_monitor) refers to capi.o(i.capi_command_request) for capi_command_request
    host_diag_util.o(i.diag_lane_swap_info_sanity_checker) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.dsp_traffic_mode_switch_detect_cfg_sanity_checker) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_chip_wrapper.o(i.host_get_capi_feature_info) for host_get_capi_feature_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_polarity_info) for sanity_checker_set_polarity_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_polarity_info) for sanity_checker_get_polarity_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_lane_rx_info) for sanity_checker_lane_rx_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_lane_tx_info) for sanity_checker_lane_tx_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_lnktrn_info) for sanity_checker_lnktrn_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.diag_lane_swap_info_sanity_checker) for diag_lane_swap_info_sanity_checker
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_lane_ctrl_info) for sanity_checker_get_lane_ctrl_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_lane_ctrl_info) for sanity_checker_set_lane_ctrl_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_lane_info) for sanity_checker_get_lane_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_config_info) for sanity_checker_get_config_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_config_info) for sanity_checker_set_config_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_lw_rclk_info) for sanity_checker_set_lw_rclk_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_lw_rclk_info) for sanity_checker_get_lw_rclk_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_loopback) for sanity_checker_loopback
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_prbs_info) for sanity_checker_set_prbs_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_prbs_info) for sanity_checker_get_prbs_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_prbs_status_get) for sanity_checker_prbs_status_get
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_clear_prbs_status) for sanity_checker_clear_prbs_status
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_prbs_inject_error) for sanity_checker_prbs_inject_error
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_txpi_override) for sanity_checker_set_txpi_override
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_set_port_info) for sanity_checker_set_port_info
    host_diag_util.o(i.host_diag_cmd_sanity_checker) refers to host_diag_util.o(i.sanity_checker_get_port_info) for sanity_checker_get_port_info
    host_diag_util.o(i.host_get_memory_payload) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_diag_util.o(i.host_get_memory_payload) refers to hw_mutex_handler.o(i.acquire_hw_mutex) for acquire_hw_mutex
    host_diag_util.o(i.host_get_memory_payload) refers to host_to_chip_ipc.o(i.intf_read_memory) for intf_read_memory
    host_diag_util.o(i.host_get_memory_payload) refers to hw_mutex_handler.o(i.release_hw_mutex) for release_hw_mutex
    host_diag_util.o(i.host_get_memory_payload) refers to hr_time.o(i.delay_ms) for delay_ms
    host_diag_util.o(i.sanity_checker_get_config_info) refers to host_diag_util.o(i.validate_ports) for validate_ports
    host_diag_util.o(i.sanity_checker_get_config_info) refers to chip_config.o(.constdata) for ports
    host_diag_util.o(i.sanity_checker_get_config_info) refers to chip_config.o(.data) for port_total_entries
    host_diag_util.o(i.sanity_checker_get_lane_ctrl_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_get_lw_rclk_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_get_polarity_info) refers to host_diag_util.o(i.util_get_number_of_lanes) for util_get_number_of_lanes
    host_diag_util.o(i.sanity_checker_get_port_info) refers to host_diag_util.o(i.validate_ports) for validate_ports
    host_diag_util.o(i.sanity_checker_get_prbs_info) refers to host_diag_util.o(i.sanity_checker_set_prbs_info) for sanity_checker_set_prbs_info
    host_diag_util.o(i.sanity_checker_lane_rx_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_lane_tx_info) refers to host_diag_util.o(i.txfir_sanity_checker) for txfir_sanity_checker
    host_diag_util.o(i.sanity_checker_lane_tx_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_lnktrn_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_mpi_config_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_mpi_config_info) refers to cfcmple.o(.text) for __aeabi_cfcmpeq
    host_diag_util.o(i.sanity_checker_prbs_status_get) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_set_config_info) refers to host_diag_util.o(i.validate_ports) for validate_ports
    host_diag_util.o(i.sanity_checker_set_config_info) refers to chip_config.o(.constdata) for ports
    host_diag_util.o(i.sanity_checker_set_config_info) refers to chip_config.o(.data) for port_total_entries
    host_diag_util.o(i.sanity_checker_set_lw_rclk_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_set_port_info) refers to host_diag_util.o(i.validate_ports) for validate_ports
    host_diag_util.o(i.sanity_checker_set_port_info) refers to chip_config.o(.constdata) for ports
    host_diag_util.o(i.sanity_checker_set_port_info) refers to chip_config.o(.data) for port_total_entries
    host_diag_util.o(i.sanity_checker_set_prbs_info) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.sanity_checker_set_txpi_override) refers to host_diag_util.o(i.util_check_param_range) for util_check_param_range
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to fflti.o(.text) for __aeabi_i2f
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to fdiv.o(.text) for __aeabi_fdiv
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to fadd.o(.text) for __aeabi_fadd
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to fmul.o(.text) for __aeabi_fmul
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to f2d.o(.text) for __aeabi_f2d
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to dadd.o(.text) for __aeabi_dadd
    host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl) refers to dfixi.o(.text) for __aeabi_d2iz
    host_diag_util.o(i.validate_ports) refers to host_diag_util.o(i.intf_count_set_bits) for intf_count_set_bits
    host_diag_util.o(i.validate_ports) refers to chip_config.o(.constdata) for cw_cmode
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to memseta.o(.text) for __aeabi_memclr4
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to chal_cw_top.o(i.chal_cw_release_rtm_reset) for chal_cw_release_rtm_reset
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to chal_cw_top.o(i.chal_cw_release_egr_reset) for chal_cw_release_egr_reset
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to chal_cw_top.o(i.chal_cw_release_igr_reset) for chal_cw_release_igr_reset
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) for ml_cw_rtmr_cfg_datapath_init_handler
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) for ml_cw_rtmr_cfg_clock_reset_mux_handler
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) for ml_cw_rtmr_cfg_lane_clk_handler
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) for ml_cw_rtmr_cfg_lane_reset_handler
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) for ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pmon_config_handler) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pmon_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_bbaddr_kp4deca) for host_diag_cw_get_bbaddr_kp4deca
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pmon_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_cw_rtmr_get_kpr4fec_dec_stat) for host_diag_cw_rtmr_get_kpr4fec_dec_stat
    host_fec_prbs.o(i.host_diag_cw_rptr_fec_pmon_config_handler) refers to host_diag_fec_statistics.o(i.host_diag_set_rptr_fec_monitor) for host_diag_set_rptr_fec_monitor
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_clear_prbs_status) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) for chal_cw_rtmr_fec_pmon_clr_all
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) for chal_cw_rtmr_pgen_get_poly
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) for chal_cw_rtmr_pgen_get_inv
    host_fec_prbs.o(i.host_fec_get_prbs_gen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) for chal_cw_rtmr_fec_pgen_get_active_status
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) for chal_cw_rtmr_pmon_get_poly
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) for chal_cw_rtmr_pmon_get_inv
    host_fec_prbs.o(i.host_fec_get_prbs_mon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) for chal_cw_rtmr_fec_pmon_get_active_status
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to memseta.o(.text) for __aeabi_memclr4
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_get_prbs_status) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) for chal_cw_rtmr_pmon_get_status
    host_fec_prbs.o(i.host_fec_ignore_fault) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_fec_prbs.o(i.host_fec_ignore_fault) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_fec_prbs.o(i.host_fec_ignore_fault) refers to hr_time.o(i.delay_ms) for delay_ms
    host_fec_prbs.o(i.host_fec_reset_port) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_fec_prbs.o(i.host_fec_reset_port) refers to hr_time.o(i.delay_ms) for delay_ms
    host_fec_prbs.o(i.host_fec_reset_port) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.host_diag_cw_rptr_fec_pgen_config_handler) for host_diag_cw_rptr_fec_pgen_config_handler
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to capi.o(i.capi_set_lane_ctrl_info) for capi_set_lane_ctrl_info
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_line_pgen) for kp4_config_rtmr_prbs_egr_line_pgen
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_line_pgen) for kp4_enable_rtmr_prbs_egr_line_pgen
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_host_pgen) for kp4_config_rtmr_prbs_igr_host_pgen
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_host_pgen) for kp4_enable_rtmr_prbs_igr_host_pgen
    host_fec_prbs.o(i.host_fec_set_prbs_gen) refers to host_fec_prbs.o(i.host_fec_reset_port) for host_fec_reset_port
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to common_util.o(i.util_memset) for util_memset
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_get_chip_config) for host_diag_cw_get_chip_config
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_port_cfg) for host_diag_cw_kp4_gen_port_cfg
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_diag_fec_statistics.o(i.host_diag_cw_kp4_gen_cw_mode_cfg) for host_diag_cw_kp4_gen_cw_mode_cfg
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_fec_prbs.o(i.host_diag_cw_rptr_fec_pmon_config_handler) for host_diag_cw_rptr_fec_pmon_config_handler
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_host_pmon) for kp4_config_rtmr_prbs_egr_host_pmon
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_host_pmon) for kp4_enable_rtmr_prbs_egr_host_pmon
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_line_pmon) for kp4_config_rtmr_prbs_igr_line_pmon
    host_fec_prbs.o(i.host_fec_set_prbs_mon) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_line_pmon) for kp4_enable_rtmr_prbs_igr_line_pmon
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_host_pmon) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_pmon) for kp4_config_rtmr_prbs_pmon
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_line_pgen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_line_pgen) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_line_pgen) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_datamux_cfg) for chal_cw_rtmr_fec_enc_datamux_cfg
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_egr_line_pgen) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) for kp4_config_rtmr_prbs_pgen
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_host_pgen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_host_pgen) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_host_pgen) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_datamux_cfg) for chal_cw_rtmr_fec_enc_datamux_cfg
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_host_pgen) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) for kp4_config_rtmr_prbs_pgen
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_igr_line_pmon) refers to host_fec_prbs.o(i.kp4_config_rtmr_prbs_pmon) for kp4_config_rtmr_prbs_pmon
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) for chal_cw_rtmr_fec_enc_reset_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_clk_gate_control) for chal_cw_rtmr_fec_enc_clk_gate_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) for chal_cw_rtmr_fec_symb_dist_reset_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_clk_gate_control) for chal_cw_rtmr_fec_symb_dist_clk_gate_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) for chal_cw_rtmr_tmt_pfifo_reset_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_clk_gate_control) for chal_cw_rtmr_tmt_pfifo_clk_gate_control
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg) for chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg) for chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pgen_fec_prbs_clk_cfg) for chal_pam4_pgen_fec_prbs_clk_cfg
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pgen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) for chal_cw_rtmr_fec_prbs_pgen_config_wrapper
    host_fec_prbs.o(i.kp4_config_rtmr_prbs_pmon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) for chal_cw_rtmr_fec_prbs_pmon_config_wrapper
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_host_pmon) refers to hr_time.o(i.delay_ms) for delay_ms
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_host_pmon) refers to host_fec_prbs.o(i.kp4_rtmr_egr_get_pcsfec_lock) for kp4_rtmr_egr_get_pcsfec_lock
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_host_pmon) refers to printfa.o(i.__0printf) for __2printf
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_host_pmon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) for chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_egr_line_pgen) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) for kp4_enable_rtmr_prbs_pgen
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_host_pgen) refers to host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) for kp4_enable_rtmr_prbs_pgen
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_line_pmon) refers to hr_time.o(i.delay_ms) for delay_ms
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_line_pmon) refers to host_fec_prbs.o(i.kp4_rtmr_igr_get_pcsfec_lock) for kp4_rtmr_igr_get_pcsfec_lock
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_line_pmon) refers to printfa.o(i.__0printf) for __2printf
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_igr_line_pmon) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) for chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) for chal_cw_rtmr_tmt_pfifo_reset_control
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_clk_gate_control) for chal_cw_rtmr_tmt_pfifo_clk_gate_control
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) for chal_cw_rtmr_fec_symb_dist_reset_control
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) for chal_cw_rtmr_fec_enc_reset_control
    host_fec_prbs.o(i.kp4_enable_rtmr_prbs_pgen) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) for chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper
    host_fec_prbs.o(i.kp4_rtmr_egr_get_pcsfec_lock) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_rtmr_egr_get_pcsfec_lock) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_rtmr_egr_get_pcsfec_lock) refers to chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_pcs_sync_get_status) for chal_cw_rtmr_pcs_sync_get_status
    host_fec_prbs.o(i.kp4_rtmr_egr_get_pcsfec_lock) refers to chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_fec_sync_status) for chal_cw_rtmr_fec_sync_status
    host_fec_prbs.o(i.kp4_rtmr_igr_get_pcsfec_lock) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_fec_prbs.o(i.kp4_rtmr_igr_get_pcsfec_lock) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    host_fec_prbs.o(i.kp4_rtmr_igr_get_pcsfec_lock) refers to chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_fec_sync_status) for chal_cw_rtmr_fec_sync_status
    host_gpio_util.o(i.host_get_gpio_info) refers to chal_gpio.o(i.chal_gpio_get_dir) for chal_gpio_get_dir
    host_gpio_util.o(i.host_get_gpio_info) refers to chal_gpio.o(i.chal_gpio_get_input) for chal_gpio_get_input
    host_gpio_util.o(i.host_get_gpio_info) refers to chal_gpio.o(i.chal_gpio_get_pulldown) for chal_gpio_get_pulldown
    host_gpio_util.o(i.host_get_gpio_info) refers to chal_gpio.o(i.chal_gpio_get_pullup) for chal_gpio_get_pullup
    host_gpio_util.o(i.host_set_gpio_info) refers to chal_gpio.o(i.chal_gpio_set_dir) for chal_gpio_set_dir
    host_gpio_util.o(i.host_set_gpio_info) refers to chal_gpio.o(i.chal_gpio_set_pulldown) for chal_gpio_set_pulldown
    host_gpio_util.o(i.host_set_gpio_info) refers to chal_gpio.o(i.chal_gpio_set_pullup) for chal_gpio_set_pullup
    host_gpio_util.o(i.host_set_gpio_info) refers to chal_gpio.o(i.chal_gpio_set_output) for chal_gpio_set_output
    host_log_util.o(i.get_phy_command_id_str) refers to host_log_util.o(.data) for ret_str
    host_lw_wrapper.o(i.host_dsp_get_pll_fracn_dynamic_adjust) refers to memseta.o(.text) for __aeabi_memclr4
    host_lw_wrapper.o(i.host_dsp_get_pll_fracn_dynamic_adjust) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to memseta.o(.text) for __aeabi_memclr4
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to common_util.o(i.util_memset) for util_memset
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to hr_time.o(i.delay_ms) for delay_ms
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_lw_wrapper.o(i.host_dsp_set_pll_fracn_dynamic_adjust) refers to host_lw_wrapper.o(.constdata) for .constdata
    host_lw_wrapper.o(i.host_lw_get_lane_modulation) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_lw_wrapper.o(i.host_lw_get_lane_modulation) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_get_pll_configuration) refers to memseta.o(.text) for __aeabi_memclr4
    host_lw_wrapper.o(i.host_lw_get_pll_configuration) refers to dsp_utils.o(i.lw_util_init_lane_hw_base_addr) for lw_util_init_lane_hw_base_addr
    host_lw_wrapper.o(i.host_lw_get_pll_configuration) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_get_snr_threshold) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_lw_wrapper.o(i.host_lw_get_snr_threshold) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_set_snr_threshold) refers to dsp_utils.o(i.lw_util_init_lane_config_base_addr) for lw_util_init_lane_config_base_addr
    host_lw_wrapper.o(i.host_lw_set_snr_threshold) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_set_snr_threshold) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_4tap_prog_256_lut) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_4tap_prog_256_lut) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_4tap_read_256_lut) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_4tap_read_256_lut) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_4tap_read_256_lut) refers to common_util.o(i.signext) for signext
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to memcpya.o(.text) for __aeabi_memcpy4
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to common_util.o(i.signext) for signext
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to fflti.o(.text) for __aeabi_i2f
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to fdiv.o(.text) for __aeabi_fdiv
    host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut) refers to host_lw_wrapper.o(.constdata) for .constdata
    host_power_util.o(i.chal_ana_avdd_get_volt) refers to host_power_util.o(i.chal_ana_reg_access) for chal_ana_reg_access
    host_power_util.o(i.chal_ana_avdd_set_volt) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_avs_set_volt) refers to host_power_util.o(i.chal_ana_reg_access) for chal_ana_reg_access
    host_power_util.o(i.chal_ana_reg_access) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_power_util.o(i.chal_ana_reg_access) refers to hr_time.o(i.delay_us) for delay_us
    host_power_util.o(i.chal_ana_reg_access) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_power_util.o(i.chal_ana_reg_disable_avdd_slave) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_disable_avs_1_slaves) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_disable_avs_2_slaves) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_disable_avs_3_slaves) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_disable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_disable_vddm_slave) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avdd_slave) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avs_slave1) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avs_slave2) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avs_slave3) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avs_slave4) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_avs_slaves) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_enable_vddm_slave) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.chal_ana_reg_rdwr_access) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_power_util.o(i.chal_ana_reg_rdwr_access) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_power_util.o(i.chal_ana_vddm_get_volt) refers to host_power_util.o(i.chal_ana_reg_access) for chal_ana_reg_access
    host_power_util.o(i.chal_ana_vddm_set_volt) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.disable_avs) refers to host_avs.o(i.host_get_avs_status) for host_get_avs_status
    host_power_util.o(i.disable_avs) refers to common_util.o(i.util_memset) for util_memset
    host_power_util.o(i.disable_avs) refers to capi.o(i.capi_set_avs_config) for capi_set_avs_config
    host_power_util.o(i.disable_avs) refers to hr_time.o(i.delay_ms) for delay_ms
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to common_util.o(i.util_memset) for util_memset
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to common_util.o(i.util_memcpy) for util_memcpy
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to hr_time.o(i.delay_ms) for delay_ms
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to host_power_util.o(i.disable_avs) for disable_avs
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to host_power_util.o(i.chal_ana_vddm_set_volt) for chal_ana_vddm_set_volt
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to host_power_util.o(i.chal_ana_vddm_get_volt) for chal_ana_vddm_get_volt
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to host_power_util.o(i.chal_ana_avdd_set_volt) for chal_ana_avdd_set_volt
    host_power_util.o(i.host_set_internal_regulator_voltage) refers to host_power_util.o(i.chal_ana_avdd_get_volt) for chal_ana_avdd_get_volt
    host_power_util.o(i.host_util_ana_disable_all_regs) refers to host_power_util.o(i.chal_ana_reg_rdwr_access) for chal_ana_reg_rdwr_access
    host_power_util.o(i.top_supspend_resume) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_power_util.o(i.top_supspend_resume) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_power_util.o(i.top_supspend_resume) refers to hr_time.o(i.delay_ms) for delay_ms
    host_test.o(i.host_test_check_port_validation) refers to chip_config.o(.constdata) for cw_cmode
    host_test.o(i.host_test_get_port_lane_mask) refers to chip_config.o(.constdata) for cw_cmode
    host_test.o(i.host_test_get_valid_port_mask) refers to chip_config.o(.constdata) for cw_cmode
    host_to_chip_ipc.o(i.intf_capi2fw_command_Handler) refers to host_to_chip_ipc.o(i.intf_util_get_optimized_req_payload_size) for intf_util_get_optimized_req_payload_size
    host_to_chip_ipc.o(i.intf_capi2fw_command_Handler) refers to host_to_chip_ipc.o(i.intf_capi2fw_command_request) for intf_capi2fw_command_request
    host_to_chip_ipc.o(i.intf_capi2fw_command_Handler) refers to host_to_chip_ipc.o(i.intf_capi2fw_command_response) for intf_capi2fw_command_response
    host_to_chip_ipc.o(i.intf_capi2fw_command_request) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_to_chip_ipc.o(i.intf_capi2fw_command_request) refers to host_to_chip_ipc.o(i.intf_util_assign_tocken) for intf_util_assign_tocken
    host_to_chip_ipc.o(i.intf_capi2fw_command_request) refers to host_to_chip_ipc.o(i.intf_util_write_cmd_req_payload_sram) for intf_util_write_cmd_req_payload_sram
    host_to_chip_ipc.o(i.intf_capi2fw_command_request) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_to_chip_ipc.o(i.intf_capi2fw_command_response) refers to hr_time.o(i.delay_ms) for delay_ms
    host_to_chip_ipc.o(i.intf_capi2fw_command_response) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_to_chip_ipc.o(i.intf_capi2fw_command_response) refers to host_to_chip_ipc.o(i.intf_util_read_cmd_rsp_sram) for intf_util_read_cmd_rsp_sram
    host_to_chip_ipc.o(i.intf_capi2fw_command_response) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_to_chip_ipc.o(i.intf_read_memory) refers to host_to_chip_ipc.o(i.intf_util_read_sram) for intf_util_read_sram
    host_to_chip_ipc.o(i.intf_util_assign_tocken) refers to host_to_chip_ipc.o(.data) for cmd_tocken
    host_to_chip_ipc.o(i.intf_util_read_cmd_rsp_sram) refers to host_chip_mem_map.o(i.host_chip_memmap_get_data_ptr) for host_chip_memmap_get_data_ptr
    host_to_chip_ipc.o(i.intf_util_read_cmd_rsp_sram) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_to_chip_ipc.o(i.intf_util_read_cmd_rsp_sram) refers to host_to_chip_ipc.o(i.intf_util_read_sram) for intf_util_read_sram
    host_to_chip_ipc.o(i.intf_util_read_sram) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_to_chip_ipc.o(i.intf_util_write_cmd_req_payload_sram) refers to host_chip_mem_map.o(i.host_chip_memmap_get_data_ptr) for host_chip_memmap_get_data_ptr
    host_to_chip_ipc.o(i.intf_util_write_cmd_req_payload_sram) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    host_to_chip_ipc.o(i.intf_util_write_cmd_req_payload_sram) refers to host_to_chip_ipc.o(i.intf_util_write_sram) for intf_util_write_sram
    host_to_chip_ipc.o(i.intf_util_write_sram) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    hw_mutex_handler.o(i.acquire_hw_mutex) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    hw_mutex_handler.o(i.release_hw_mutex) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_config) refers to chal_cw_prbs.o(i.chal_cw_pgen_init) for chal_cw_pgen_init
    chal_cw_prbs.o(i.chal_cw_pgen_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_enable) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_enable) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_enable) refers to chal_cw_prbs.o(i.chal_cw_pgen_read_lock) for chal_cw_pgen_read_lock
    chal_cw_prbs.o(i.chal_cw_pgen_init) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pgen_read_lock) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_clr_accs) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_clr_accs) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_clr_all) refers to chal_cw_prbs.o(i.chal_cw_pmon_clr_accs) for chal_cw_pmon_clr_accs
    chal_cw_prbs.o(i.chal_cw_pmon_clr_all) refers to chal_cw_prbs.o(i.chal_cw_pmon_clr_lol) for chal_cw_pmon_clr_lol
    chal_cw_prbs.o(i.chal_cw_pmon_clr_lol) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_clr_lol) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_config) refers to chal_cw_prbs.o(i.chal_cw_pmon_init) for chal_cw_pmon_init
    chal_cw_prbs.o(i.chal_cw_pmon_config) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_config) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_enable) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_enable) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_get_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_get_status) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_prbs.o(i.chal_cw_pmon_init) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_4p) for chal_cw_rtmr_fec_clk_gate_control_4p
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_8p) for chal_cw_rtmr_fec_clk_gate_control_8p
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_4p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_4p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_8p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control_8p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_4p) for chal_cw_rtmr_fec_reset_control_4p
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_8p) for chal_cw_rtmr_fec_reset_control_8p
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_4p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_4p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_8p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control_8p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_clk_gate_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_rcv_gapclk_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_rcv_gapclk_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gapclk_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gapclk_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gbox_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gbox_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) refers to chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_tmt_pfifo_get_lane_mask) for chal_cw_rtmr_tmt_pfifo_get_lane_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_top_reset_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_top_reset_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_am_ins_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_am_ins_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_clk_gate_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_clk_gate_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_reset_control) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_reset_control) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_reset_control) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_cmu2cdr_lane_map_cfg) refers to chal_cw_rtmr_clockrst_mux.o(i.util_get_lowest_index_from_mask) for util_get_lowest_index_from_mask
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_cmu2cdr_lane_map_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_cmu2cdr_lane_map_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_4p) for chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_4p
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_8p) for chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_8p
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_4p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_4p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_8p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg_8p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_tmt_clk_rst_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_tmt_clk_rst_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_clk_rst_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_clk_rst_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_tmt_clk_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_tmt_clk_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cdr_clk_rst_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cdr_clk_rst_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cmu_clk_rst_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cmu_clk_rst_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_rcv_pfifo_clk_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_rcv_pfifo_clk_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_tmt_pfifo_clk_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_tmt_pfifo_clk_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cha_cw_rtmr_din_off) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cha_cw_rtmr_din_off) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_dp_mode_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_dp_mode_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_deskew_reorder_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_deskew_reorder_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_datamux_cfg) refers to chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_enc_datamux_config_slice) for cw_rtmr_fec_enc_datamux_config_slice
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_mux_cfg) refers to memseta.o(.text) for __aeabi_memclr4
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_mux_cfg) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg) refers to memseta.o(.text) for __aeabi_memclr4
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_mode_kr4_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_mode_kr4_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_rcv_datamux_cfg) refers to chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_rcv_datamux_config_slice) for cw_rtmr_fec_rcv_datamux_config_slice
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecdec_data_gating_cfg) refers to memseta.o(.text) for __aeabi_memclr4
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecdec_data_gating_cfg) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecdec_data_gating_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecdec_data_gating_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecenc_data_gating_cfg) refers to memseta.o(.text) for __aeabi_memclr4
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecenc_data_gating_cfg) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecenc_data_gating_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecenc_data_gating_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_fec_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_fec_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_speed_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_speed_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_ovr_am_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_ovr_am_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_predec_bypass_deint_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_predec_bypass_deint_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_bundle_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_bundle_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_corr_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_4p) for chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_4p
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_8p) for chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_8p
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_4p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_4p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_8p) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg_8p) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ib_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ib_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_mode_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_mode_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ob_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ob_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_wrmux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_wrmux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_bundle_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_bundle_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_corr_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_dinmux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_dinmux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_doutmux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_doutmux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ib_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ib_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_mode_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_mode_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ob_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ob_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_rdmux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_rdmux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_xenc_datamux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_xenc_datamux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_enc_datamux_config_slice) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_enc_datamux_config_slice) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_rcv_datamux_config_slice) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_datapath_cfg.o(i.cw_rtmr_fec_rcv_datamux_config_slice) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pgen_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) refers to chal_cw_prbs.o(i.chal_cw_pmon_clr_all) for chal_cw_pmon_clr_all
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_get_active_status) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to hr_time.o(i.delay_ms) for delay_ms
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to chal_cw_prbs.o(i.chal_cw_pgen_config) for chal_cw_pgen_config
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) refers to chal_cw_prbs.o(i.chal_cw_pgen_enable) for chal_cw_pgen_enable
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pgen_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) refers to chal_cw_prbs.o(i.chal_cw_pmon_config) for chal_cw_pmon_config
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_config_wrapper) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) refers to chal_cw_prbs.o(i.chal_cw_pmon_enable) for chal_cw_pmon_enable
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_pmon_enable_disable_wrapper) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_inv) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pgen_get_poly) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_inv) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_poly) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to common_util.o(i.util_memcpy) for util_memcpy
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to memseta.o(.text) for __aeabi_memclr4
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_prbs_get_base_addr) for chal_cw_rtmr_fec_prbs_get_base_addr
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to chal_cw_prbs.o(i.chal_cw_pmon_get_status) for chal_cw_pmon_get_status
    chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_pmon_get_status) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for fec_prbs
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pgen_fec_prbs_clk_cfg) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pgen_fec_prbs_clk_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pgen_fec_prbs_clk_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pgen_fec_prbs_clk_cfg) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for .constdata
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg) refers to chal_cw_rtmr_kp4prbs.o(i._get_cfg_list_array) for _get_cfg_list_array
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_kp4prbs.o(i.chal_pam4_pmon_fec_prbs_fec_ip_data_mux_cfg) refers to chal_cw_rtmr_kp4prbs.o(.constdata) for .constdata
    chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_fec_sync_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_status_check.o(i.chal_cw_rtmr_pcs_sync_get_status) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_dscr_ctrl) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_dscr_ctrl) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_gbox_gap) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_gbox_gap) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_width_ctrl) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_width_ctrl) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_gbox_gap) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_gbox_gap) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_scr_ctrl) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_scr_ctrl) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_width_ctrl) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_width_ctrl) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_top.o(i.chal_cw_release_egr_reset) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_top.o(i.chal_cw_release_egr_reset) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_top.o(i.chal_cw_release_igr_reset) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_top.o(i.chal_cw_release_igr_reset) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_top.o(i.chal_cw_release_rtm_reset) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_top.o(i.chal_cw_release_rtm_reset) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_cw_top.o(i.chal_cw_tx_data_select) refers to chal_cw_utils.o(i.duplicate_2) for duplicate_2
    chal_cw_top.o(i.chal_cw_tx_data_select) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_cw_top.o(i.chal_cw_tx_data_select) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_gpio.o(i.chal_gpio_get_dir) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_get_input) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_get_pulldown) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_get_pullup) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_set_dir) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_set_dir) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_gpio.o(i.chal_gpio_set_output) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_set_output) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_gpio.o(i.chal_gpio_set_pulldown) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_set_pulldown) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    chal_gpio.o(i.chal_gpio_set_pullup) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    chal_gpio.o(i.chal_gpio_set_pullup) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    dsp_utils.o(i.lw_util_init_lane_config_base_addr) refers to common_util.o(i.util_memcpy) for util_memcpy
    dsp_utils.o(i.lw_util_init_lane_config_base_addr) refers to dsp_config.o(.constdata) for dsp_lane_config_gpr_bbaddr
    dsp_utils.o(i.lw_util_init_lane_hw_base_addr) refers to common_util.o(i.util_memcpy) for util_memcpy
    dsp_utils.o(i.lw_util_init_lane_hw_base_addr) refers to dsp_config.o(.constdata) for dsp_lane_bbaddr
    host_chip_mem_map.o(i.host_chip_memmap_get_data_ptr) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    host_chip_mem_map.o(i.host_chip_memmap_get_data_ptr) refers to host_chip_mem_map.o(.data) for g_chip_mem_map_data_ptr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to common_util.o(i.util_memcpy) for util_memcpy
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_tmt_pfifo_clk_mux_cfg) for chal_cw_rtmr_tmt_pfifo_clk_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_rcv_pfifo_clk_mux_cfg) for chal_cw_rtmr_rcv_pfifo_clk_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg) for chal_cw_rtmr_fec_rcv_clk_rst_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_clk_rst_mux_cfg) for chal_cw_rtmr_pcs_rcv_clk_rst_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_tmt_clk_rst_mux_cfg) for chal_cw_rtmr_fec_tmt_clk_rst_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cdr_clk_rst_mux_cfg) for chal_cw_rtmr_pcs_tmt_cdr_clk_rst_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_tmt_cmu_clk_rst_mux_cfg) for chal_cw_rtmr_pcs_tmt_cmu_clk_rst_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg) for chal_cw_rtmr_fec_rcv_tmt_clk_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_pcs_rcv_tmt_clk_mux_cfg) for chal_cw_rtmr_pcs_rcv_tmt_clk_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_clock_reset_mux_handler) refers to chal_cw_rtmr_clockrst_mux.o(i.chal_cw_rtmr_cmu2cdr_lane_map_cfg) for chal_cw_rtmr_cmu2cdr_lane_map_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) refers to common_util.o(i.util_memcpy) for util_memcpy
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) refers to chal_cw_top.o(i.chal_cw_tx_data_select) for chal_cw_tx_data_select
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler) refers to ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) for ml_cw_rtmr_cfg_datapath_init_handler_core
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to common_util.o(i.util_memcpy) for util_memcpy
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.cha_cw_rtmr_din_off) for cha_cw_rtmr_din_off
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_mode_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_mode_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_doutmux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_corr_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_corr_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ob_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_ob_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_ib_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_ib_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_bundle_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_bundle_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_rcv_pfifo_gbox_wrmux_cfg) for chal_cw_rtmr_rcv_pfifo_gbox_wrmux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_mode_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_mode_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_dinmux_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_dinmux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_corr_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_corr_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ob_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_ob_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_ib_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_ib_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_bundle_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_bundle_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_rdmux_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_rdmux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_tmt_pfifo_gbox_doutmux_cfg) for chal_cw_rtmr_tmt_pfifo_gbox_doutmux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_rcv_datamux_cfg) for chal_cw_rtmr_fec_rcv_datamux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_datamux_cfg) for chal_cw_rtmr_fec_enc_datamux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_xenc_datamux_cfg) for chal_cw_rtmr_xenc_datamux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_mode_kr4_cfg) for chal_cw_rtmr_fec_mode_kr4_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_dp_mode_mux_cfg) for chal_cw_rtmr_dp_mode_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_deskew_reorder_cfg) for chal_cw_rtmr_fec_deskew_reorder_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_predec_bypass_deint_cfg) for chal_cw_rtmr_predec_bypass_deint_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_speed_cfg) for chal_cw_rtmr_kxky_gbox_speed_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_kxky_gbox_fec_cfg) for chal_cw_rtmr_kxky_gbox_fec_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_width_ctrl) for chal_cw_rtmr_xdec_width_ctrl
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_gbox_gap) for chal_cw_rtmr_xdec_gbox_gap
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xdec.o(i.chal_cw_rtmr_xdec_dscr_ctrl) for chal_cw_rtmr_xdec_dscr_ctrl
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_width_ctrl) for chal_cw_rtmr_xenc_width_ctrl
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_gbox_gap) for chal_cw_rtmr_xenc_gbox_gap
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_xenc.o(i.chal_cw_rtmr_xenc_scr_ctrl) for chal_cw_rtmr_xenc_scr_ctrl
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_mux_cfg) for chal_cw_rtmr_fec_enc_mux_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg) for chal_cw_rtmr_fec_enc_pass_thru_uncorr_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecdec_data_gating_cfg) for chal_cw_rtmr_fecdec_data_gating_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_fecenc_data_gating_cfg) for chal_cw_rtmr_fecenc_data_gating_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_datapath_init_handler_core) refers to chal_cw_rtmr_datapath_cfg.o(i.chal_cw_rtmr_ovr_am_cfg) for chal_cw_rtmr_ovr_am_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to common_util.o(i.util_memcpy) for util_memcpy
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_clk_gate_control) for chal_cw_rtmr_pcs_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_clk_gate_control) for chal_cw_rtmr_fec_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_rcv_gapclk_control) for chal_cw_rtmr_rcv_gapclk_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_clk_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gapclk_control) for chal_cw_rtmr_tmt_gapclk_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to common_util.o(i.util_memcpy) for util_memcpy
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) for ml_cw_rtmr_get_base_addr
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_reset_control) for chal_cw_rtmr_pcs_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_reset_control) for chal_cw_rtmr_fec_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_lane_reset_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_top_reset_cfg) for chal_cw_rtmr_top_reset_cfg
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_clk_gate_control) for chal_cw_rtmr_krkp_gbox_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_krkp_gbox_reset_control) for chal_cw_rtmr_krkp_gbox_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_clk_gate_control) for chal_cw_rtmr_xdec_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xdec_reset_control) for chal_cw_rtmr_xdec_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_clk_gate_control) for chal_cw_rtmr_xenc_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_reset_control) for chal_cw_rtmr_xenc_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_xenc_am_ins_clk_gate_control) for chal_cw_rtmr_xenc_am_ins_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_clk_gate_control) for chal_cw_rtmr_fec_enc_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_clk_gate_control) for chal_cw_rtmr_fec_symb_dist_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_clk_gate_control) for chal_cw_rtmr_pcs_tmt_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_reset_control) for chal_cw_rtmr_tmt_pfifo_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_pfifo_clk_gate_control) for chal_cw_rtmr_tmt_pfifo_clk_gate_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_enc_reset_control) for chal_cw_rtmr_fec_enc_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_fec_symb_dist_reset_control) for chal_cw_rtmr_fec_symb_dist_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_pcs_tmt_reset_control) for chal_cw_rtmr_pcs_tmt_reset_control
    ml_cw_rtmr_handler.o(i.ml_cw_rtmr_cfg_trnsmt_func_blk_clk_and_rst_handler) refers to chal_cw_rtmr_clkrst_control.o(i.chal_cw_rtmr_tmt_gbox_reset_control) for chal_cw_rtmr_tmt_gbox_reset_control
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to common_util.o(i.util_memset) for util_memset
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to ml_cw_xbar.o(i.ml_cw_get_xbar_logical_lane_mask) for ml_cw_get_xbar_logical_lane_mask
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) for ml_cw_get_quad_swap_enabled
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to ml_cw_rtmr_modes.o(i._intf_count_set_bits) for _intf_count_set_bits
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_gen_cw_mode_param) refers to ml_cw_rtmr_modes.o(.data) for rtmr_map
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_get_base_addr) refers to ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) for ml_cw_rtmr_is_A1_4p_port
    ml_cw_rtmr_modes.o(i.ml_cw_rtmr_is_A1_4p_port) refers to common_util.o(i.fw_read_rev_id) for fw_read_rev_id
    ml_cw_xbar.o(i.ml_cw_get_m1_modes_lane_swap_enabled) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_xbar.o(i.ml_cw_get_m1_modes_lane_swap_enabled) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) refers to memseta.o(.text) for __aeabi_memclr4
    ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    ml_cw_xbar.o(i.ml_cw_get_xbar_logical_lane_mask) refers to ml_cw_xbar.o(i.ml_cw_get_quad_swap_enabled) for ml_cw_get_quad_swap_enabled
    ml_cw_xbar.o(i.ml_cw_get_xbar_logical_lane_mask) refers to ml_cw_xbar.o(i.ml_cw_get_m1_modes_lane_swap_enabled) for ml_cw_get_m1_modes_lane_swap_enabled
    common_util.o(i.fw_read_chip_id) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    common_util.o(i.fw_read_package_id) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    common_util.o(i.fw_read_rev_id) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    common_util.o(i.wr_reg_field) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    common_util.o(i.wr_reg_field) refers to mdio.o(i.wr_reg_ex) for wr_reg_ex
    hr_time.o(i.HATS_DelayMs) refers to hr_time.o(i.delay_ms) for delay_ms
    hr_time.o(i.HATS_DelayUsec) refers to hr_time.o(i.delay_us) for delay_us
    hr_time.o(i._get_elapsed_timestamp) refers to hr_time.o(i._get_timestamp) for _get_timestamp
    hr_time.o(i._get_timestamp) refers to mdio.o(i.rd_reg_ex) for rd_reg_ex
    hr_time.o(i.delay_ms) refers to hr_time.o(i.delay_us) for delay_us
    hr_time.o(i.delay_s) refers to hr_time.o(i.delay_ms) for delay_ms
    hr_time.o(i.delay_us) refers to hr_time.o(i.phy_delay_us) for phy_delay_us
    hr_time.o(i.phy_delay_us) refers to hr_time.o(i._get_timestamp) for _get_timestamp
    hr_time.o(i.phy_delay_us) refers to hr_time.o(i._get_elapsed_timestamp) for _get_elapsed_timestamp
    log10.o(i.__softfp_log10) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10.o(i.__softfp_log10) refers to log10.o(i.log10) for log10
    log10.o(i.log10) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10.o(i.log10) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10.o(i.log10) refers to errno.o(i.__set_errno) for __set_errno
    log10.o(i.log10) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log10.o(i.log10) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log10.o(i.log10) refers to dscalb.o(.text) for __ARM_scalbn
    log10.o(i.log10) refers to dflti.o(.text) for __aeabi_i2d
    log10.o(i.log10) refers to log.o(i.log) for log
    log10.o(i.log10) refers to dmul.o(.text) for __aeabi_dmul
    log10.o(i.log10) refers to dadd.o(.text) for __aeabi_dadd
    log10_x.o(i.____softfp_log10$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10_x.o(i.____softfp_log10$lsc) refers to log10_x.o(i.__log10$lsc) for __log10$lsc
    log10_x.o(i.__log10$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10_x.o(i.__log10$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log10_x.o(i.__log10$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log10_x.o(i.__log10$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    log10_x.o(i.__log10$lsc) refers to dflti.o(.text) for __aeabi_i2d
    log10_x.o(i.__log10$lsc) refers to log.o(i.log) for log
    log10_x.o(i.__log10$lsc) refers to dmul.o(.text) for __aeabi_dmul
    log10_x.o(i.__log10$lsc) refers to dadd.o(.text) for __aeabi_dadd
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f205xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f205xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f205xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f205xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltul.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__softfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.log) for log
    log.o(i.log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.log) refers to errno.o(i.__set_errno) for __set_errno
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.log) refers to dscalb.o(.text) for __ARM_scalbn
    log.o(i.log) refers to dadd.o(.text) for __aeabi_dsub
    log.o(i.log) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log.o(i.log) refers to dflti.o(.text) for __aeabi_i2d
    log.o(i.log) refers to dmul.o(.text) for __aeabi_dmul
    log.o(i.log) refers to ddiv.o(.text) for __aeabi_ddiv
    log.o(i.log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.log) refers to log.o(.constdata) for .constdata
    log.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.__log$lsc) for __log$lsc
    log_x.o(i.__log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.__log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.__log$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log_x.o(i.__log$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    log_x.o(i.__log$lsc) refers to dadd.o(.text) for __aeabi_dsub
    log_x.o(i.__log$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log_x.o(i.__log$lsc) refers to dflti.o(.text) for __aeabi_i2d
    log_x.o(i.__log$lsc) refers to dmul.o(.text) for __aeabi_dmul
    log_x.o(i.__log$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    log_x.o(i.__log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.__log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.__log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f205xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.Update_ErrorCount), (156 bytes).
    Removing main.o(i.getcommand_value), (300 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing usb_device.o(.rev16_text), (4 bytes).
    Removing usb_device.o(.revsh_text), (4 bytes).
    Removing usb_device.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(.rev16_text), (4 bytes).
    Removing usbd_conf.o(.revsh_text), (4 bytes).
    Removing usbd_conf.o(.rrx_text), (6 bytes).
    Removing usbd_conf.o(i.HAL_PCD_MspDeInit), (52 bytes).
    Removing usbd_conf.o(i.USBD_LL_DeInit), (60 bytes).
    Removing usbd_conf.o(i.USBD_LL_Delay), (12 bytes).
    Removing usbd_conf.o(i.USBD_LL_FlushEP), (68 bytes).
    Removing usbd_conf.o(i.USBD_LL_Stop), (60 bytes).
    Removing usbd_desc.o(.rev16_text), (4 bytes).
    Removing usbd_desc.o(.revsh_text), (4 bytes).
    Removing usbd_desc.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing master_i2c.o(.rev16_text), (4 bytes).
    Removing master_i2c.o(.revsh_text), (4 bytes).
    Removing master_i2c.o(.rrx_text), (6 bytes).
    Removing master_i2c.o(i.I2C_ReadByte), (84 bytes).
    Removing mdio.o(.rev16_text), (4 bytes).
    Removing mdio.o(.revsh_text), (4 bytes).
    Removing mdio.o(.rrx_text), (6 bytes).
    Removing mdio.o(i.HAL_SPI_MspDeInit), (48 bytes).
    Removing usbd_cdc_if.o(.rev16_text), (4 bytes).
    Removing usbd_cdc_if.o(.revsh_text), (4 bytes).
    Removing usbd_cdc_if.o(.rrx_text), (6 bytes).
    Removing bcm87800.o(.rev16_text), (4 bytes).
    Removing bcm87800.o(.revsh_text), (4 bytes).
    Removing bcm87800.o(.rrx_text), (6 bytes).
    Removing bcm87800.o(i.bcm87800_clock_divider), (156 bytes).
    Removing bcm87800.o(i.bcm87800_set_fec_clear_all), (104 bytes).
    Removing bcm87800.o(i.bcm87800_set_fec_enabled), (164 bytes).
    Removing bcm87800.o(i.chip_internal_dvdd), (56 bytes).
    Removing rng.o(.rev16_text), (4 bytes).
    Removing rng.o(.revsh_text), (4 bytes).
    Removing rng.o(.rrx_text), (6 bytes).
    Removing rng.o(i.HAL_RNG_MspDeInit), (32 bytes).
    Removing rng.o(i.HAL_RNG_MspInit), (52 bytes).
    Removing rng.o(i.MX_RNG_Init), (32 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (48 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (52 bytes).
    Removing master_i2c_pm.o(.rev16_text), (4 bytes).
    Removing master_i2c_pm.o(.revsh_text), (4 bytes).
    Removing master_i2c_pm.o(.rrx_text), (6 bytes).
    Removing master_i2c_pm.o(i.I2C_ReadBytes_PM), (110 bytes).
    Removing master_i2c_pm.o(i.I2C_WriteBytes_PM), (82 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (60 bytes).
    Removing stm32f2xx_hal_pcd_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_pcd_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_pcd_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_pcd.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_pcd.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_pcd.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ActivateRemoteWakeup), (36 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ConnectCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DataInStageCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DataOutStageCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DeActivateRemoteWakeup), (24 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DeInit), (38 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DevConnect), (46 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DevDisconnect), (46 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_DisconnectCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_EP_Flush), (68 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_GetState), (8 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ISOINIncompleteCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ISOOUTIncompleteCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_MspInit), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ResetCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_ResumeCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_SOFCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_SetupStageCallback), (2 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_Stop), (58 bytes).
    Removing stm32f2xx_hal_pcd.o(i.HAL_PCD_SuspendCallback), (2 bytes).
    Removing stm32f2xx_ll_usb.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_ll_usb.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_ll_usb.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_ActivateDedicatedEndpoint), (284 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_ClearInterrupts), (8 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_DeactivateDedicatedEndpoint), (116 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_DoPing), (64 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_DriveVbus), (68 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_GetCurrentFrame), (12 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_GetHostSpeed), (18 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_HC_Halt), (378 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_HC_Init), (392 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_HC_ReadInterrupt), (12 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_HC_StartXfer), (392 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_HostInit), (236 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_InitFSLSPClkSel), (62 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_ResetPort), (48 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_StopDevice), (82 bytes).
    Removing stm32f2xx_ll_usb.o(i.USB_StopHost), (174 bytes).
    Removing stm32f2xx_ll_usb.o(.data), (4 bytes).
    Removing stm32f2xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (136 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_DeInit), (68 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_IRQHandler), (372 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_PollForConversion), (186 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_Start), (256 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_Start_IT), (272 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_Stop), (72 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f2xx_hal_adc.o(i.HAL_ADC_Stop_IT), (88 bytes).
    Removing stm32f2xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (110 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (26 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (14 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (560 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (70 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (160 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (268 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (280 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (104 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (116 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (136 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (308 bytes).
    Removing stm32f2xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (112 bytes).
    Removing stm32f2xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (228 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (376 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Init), (296 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (436 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (110 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (808 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (372 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (240 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT), (304 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT), (272 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (404 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (372 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (228 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (808 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (528 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (252 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (392 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (388 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (236 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (408 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (308 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (232 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT), (154 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT), (154 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (412 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f2xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (232 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_DMAAbort), (104 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_DMAError), (60 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_DMAXferCplt), (166 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_ITError), (368 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (52 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (272 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (160 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterRequestRead), (304 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterRequestWrite), (192 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (160 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (330 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Master_ADD10), (14 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Master_ADDR), (558 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Master_SB), (130 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryRead), (344 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (224 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (30 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (72 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (28 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (70 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Slave_ADDR), (54 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Slave_AF), (164 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_Slave_STOPF), (272 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (86 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (124 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (172 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (110 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (82 bytes).
    Removing stm32f2xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (86 bytes).
    Removing stm32f2xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_DMAStop), (46 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_DeInit), (54 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_GetError), (6 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_GetState), (8 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_IRQHandler), (312 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Receive), (364 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (252 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Receive_IT), (204 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit), (422 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (550 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (324 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (200 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (220 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (184 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (52 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (50 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (52 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (50 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_CheckFlag_BSY), (40 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (188 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_CloseRx_ISR), (104 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_CloseTx_ISR), (160 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAAbortOnError), (20 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAError), (40 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (14 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (14 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (14 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMAReceiveCplt), (96 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMATransmitCplt), (114 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_RxISR_16BIT), (36 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_RxISR_8BIT), (34 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_TxISR_16BIT), (36 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_TxISR_8BIT), (34 bytes).
    Removing stm32f2xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (170 bytes).
    Removing stm32f2xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DeInit), (68 bytes).
    Removing stm32f2xx_hal.o(i.HAL_DisableCompensationCell), (28 bytes).
    Removing stm32f2xx_hal.o(i.HAL_EnableCompensationCell), (28 bytes).
    Removing stm32f2xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f2xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f2xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f2xx_hal.o(i.HAL_GetUID), (28 bytes).
    Removing stm32f2xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f2xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f2xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f2xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_DeInit), (320 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (292 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f2xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (80 bytes).
    Removing stm32f2xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (436 bytes).
    Removing stm32f2xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_MPU_Disable), (44 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_MPU_Enable), (64 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (28 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f2xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f2xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (272 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f2xx_hal_flash.o(i.HAL_FLASH_Program_IT), (136 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (40 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (40 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (60 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (120 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f2xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f2xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_gpio.o(i.HAL_GPIO_DeInit), (372 bytes).
    Removing stm32f2xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f2xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f2xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f2xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (94 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f2xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f2xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f2xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f2xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f2xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (18 bytes).
    Removing stm32f2xx_hal_dac.o(i.DAC_DMAErrorCh1), (26 bytes).
    Removing stm32f2xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (14 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_DeInit), (36 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_GetError), (6 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_GetState), (6 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_GetValue), (16 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_IRQHandler), (138 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_Start_DMA), (324 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_Stop), (30 bytes).
    Removing stm32f2xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (92 bytes).
    Removing stm32f2xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (18 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (26 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (14 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (20 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (32 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (110 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (34 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (74 bytes).
    Removing stm32f2xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (74 bytes).
    Removing stm32f2xx_hal_rng.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_rng.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_rng.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_DeInit), (56 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_ErrorCallback), (2 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_GenerateRandomNumber_IT), (64 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_GetError), (6 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_GetRandomNumber_IT), (46 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_GetState), (6 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_IRQHandler), (122 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_Init), (52 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_MspInit), (2 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_ReadLastRandomNumber), (6 bytes).
    Removing stm32f2xx_hal_rng.o(i.HAL_RNG_ReadyDataCallback), (2 bytes).
    Removing stm32f2xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f2xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f2xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_LIN_Init), (142 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_MultiProcessor_Init), (154 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Abort), (166 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive), (100 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (120 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit), (88 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (108 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Abort_IT), (216 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_DMAResume), (126 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_DeInit), (58 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Receive), (216 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Receive_IT), (112 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_Transmit_IT), (88 bytes).
    Removing stm32f2xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMARxAbortCallback), (52 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (24 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMATransmitCplt), (54 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMATxAbortCallback), (52 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f2xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing system_stm32f2xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f2xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f2xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f2xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing usbd_core.o(.rev16_text), (4 bytes).
    Removing usbd_core.o(.revsh_text), (4 bytes).
    Removing usbd_core.o(.rrx_text), (6 bytes).
    Removing usbd_core.o(i.USBD_DeInit), (38 bytes).
    Removing usbd_core.o(i.USBD_Stop), (26 bytes).
    Removing usbd_ctlreq.o(.rev16_text), (4 bytes).
    Removing usbd_ctlreq.o(.revsh_text), (4 bytes).
    Removing usbd_ctlreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(.rev16_text), (4 bytes).
    Removing usbd_ioreq.o(.revsh_text), (4 bytes).
    Removing usbd_ioreq.o(.rrx_text), (6 bytes).
    Removing usbd_ioreq.o(i.USBD_GetRxCount), (18 bytes).
    Removing usbd_cdc.o(.rev16_text), (4 bytes).
    Removing usbd_cdc.o(.revsh_text), (4 bytes).
    Removing usbd_cdc.o(.rrx_text), (6 bytes).
    Removing capi.o(i._capi_is_mixed_mode_xbar_required), (50 bytes).
    Removing capi.o(i.capi_disable_all_regs), (50 bytes).
    Removing capi.o(i.capi_disable_avdd_slave), (228 bytes).
    Removing capi.o(i.capi_disable_avs_slaves), (264 bytes).
    Removing capi.o(i.capi_disable_vddm_slave), (228 bytes).
    Removing capi.o(i.capi_enable_avs_slaves), (272 bytes).
    Removing capi.o(i.capi_get_avs_config), (120 bytes).
    Removing capi.o(i.capi_get_chip_command), (88 bytes).
    Removing capi.o(i.capi_get_chip_status), (220 bytes).
    Removing capi.o(i.capi_get_config), (104 bytes).
    Removing capi.o(i.capi_get_download_crc_status), (74 bytes).
    Removing capi.o(i.capi_get_download_status), (190 bytes).
    Removing capi.o(i.capi_get_lane_ctrl_info), (48 bytes).
    Removing capi.o(i.capi_get_lane_info), (52 bytes).
    Removing capi.o(i.capi_get_port_info), (84 bytes).
    Removing capi.o(i.capi_get_status), (16 bytes).
    Removing capi.o(i.capi_get_status_info), (54 bytes).
    Removing capi.o(i.capi_get_temperture_status), (200 bytes).
    Removing capi.o(i.capi_get_voltage_status), (200 bytes).
    Removing capi.o(i.capi_override_upgrade_status), (26 bytes).
    Removing capi.o(i.capi_set_chip_command), (100 bytes).
    Removing capi.o(i.capi_set_fw_download_info), (6 bytes).
    Removing capi.o(i.capi_set_port), (70 bytes).
    Removing capi.o(i.capi_set_regulator_info), (60 bytes).
    Removing capi.o(i.capi_set_voltage_config), (296 bytes).
    Removing capi.o(i.capi_write_register), (28 bytes).
    Removing capi_custom.o(i.capi_custom_command_request), (268 bytes).
    Removing capi_diag.o(i.capi_diag_command_request_info), (524 bytes).
    Removing capi_diag.o(i.capi_fec_ber_cmis_latch_release), (116 bytes).
    Removing capi_diag.o(i.capi_fec_ber_cmis_latch_request), (332 bytes).
    Removing capi_diag.o(i.capi_get_usr_diagnostics), (58 bytes).
    Removing capi_test.o(i.capi_clear_prbs_status), (54 bytes).
    Removing capi_test.o(i.capi_get_loopback), (44 bytes).
    Removing capi_test.o(i.capi_prbs_inject_error), (40 bytes).
    Removing capi_test.o(i.capi_set_loopback), (34 bytes).
    Removing host_chip_wrapper.o(i.host_get_gpr_lane_status), (1424 bytes).
    Removing host_chip_wrapper.o(i.host_get_lpm_st), (136 bytes).
    Removing host_chip_wrapper.o(i.host_get_spi_info), (68 bytes).
    Removing host_chip_wrapper.o(i.host_set_capi_feature_info), (104 bytes).
    Removing host_chip_wrapper.o(i.host_set_spi_info), (72 bytes).
    Removing host_chip_wrapper.o(i.util_wait_for_uc_ready), (216 bytes).
    Removing host_diag.o(i._lw_decode_fw_cu_rx_event), (144 bytes).
    Removing host_diag.o(i.host_dump_fw_events), (940 bytes).
    Removing host_diag.o(i.host_get_dynamic_mpi_state), (230 bytes).
    Removing host_diag.o(i.host_get_event_type_string), (572 bytes).
    Removing host_diag.o(i.host_get_hw_gain2_a_config), (110 bytes).
    Removing host_diag.o(i.host_get_mpi_canceller_config), (108 bytes).
    Removing host_diag.o(i.host_get_mpi_config), (200 bytes).
    Removing host_diag.o(i.host_get_mpi_dynamic_config), (182 bytes).
    Removing host_diag.o(i.host_get_mpi_state), (728 bytes).
    Removing host_diag.o(i.host_get_tmsd_config), (110 bytes).
    Removing host_diag.o(i.host_get_tmsd_state), (204 bytes).
    Removing host_diag.o(i.host_lw_calculate_pam_snr), (128 bytes).
    Removing host_diag.o(i.host_lw_get_asnr_from_gp), (198 bytes).
    Removing host_diag.o(i.host_lw_get_snr_from_gp), (250 bytes).
    Removing host_diag.o(i.host_lw_get_usr_cmis_diagnostics), (664 bytes).
    Removing host_diag.o(i.host_lw_get_usr_diagnostics), (58 bytes).
    Removing host_diag.o(i.host_set_hw_gain2_a_config), (120 bytes).
    Removing host_diag.o(i.host_set_mpi_canceller_config), (110 bytes).
    Removing host_diag.o(i.host_set_mpi_config), (244 bytes).
    Removing host_diag.o(i.host_set_mpi_dynamic_config), (192 bytes).
    Removing host_diag.o(i.host_set_tmsd_config), (120 bytes).
    Removing host_diag.o(.data), (800 bytes).
    Removing host_diag_fec_statistics.o(i.host_diag_cw_calculate_ber_between_latch), (1822 bytes).
    Removing host_diag_util.o(i.dsp_hw_gain2_adapt_cfg_sanity_checker), (32 bytes).
    Removing host_diag_util.o(i.dsp_mission_mpi_cfg_sanity_checker), (64 bytes).
    Removing host_diag_util.o(i.dsp_mpi_canceller_cfg_sanity_checker), (32 bytes).
    Removing host_diag_util.o(i.dsp_traffic_mode_switch_detect_cfg_sanity_checker), (46 bytes).
    Removing host_diag_util.o(i.host_get_memory_payload), (100 bytes).
    Removing host_diag_util.o(i.sanity_checker_mpi_config_info), (72 bytes).
    Removing host_diag_util.o(i.util_lw_tx_fir_lut_validate_coef_and_lvl), (520 bytes).
    Removing host_fec_prbs.o(i.host_fec_clear_prbs_status), (176 bytes).
    Removing host_fec_prbs.o(i.host_fec_ignore_fault), (256 bytes).
    Removing host_fec_prbs.o(.constdata), (32 bytes).
    Removing host_gpio_util.o(i.host_get_gpio_info), (98 bytes).
    Removing host_gpio_util.o(i.host_set_gpio_info), (82 bytes).
    Removing host_log_util.o(i.capi_printf), (8 bytes).
    Removing host_log_util.o(i.get_phy_command_id_str), (340 bytes).
    Removing host_log_util.o(.data), (925 bytes).
    Removing host_lw_wrapper.o(i.host_lw_get_lane_modulation), (56 bytes).
    Removing host_lw_wrapper.o(i.host_lw_get_pll_configuration), (296 bytes).
    Removing host_lw_wrapper.o(i.host_lw_tx_fir_4tap_prog_256_lut), (210 bytes).
    Removing host_lw_wrapper.o(i.host_lw_tx_fir_4tap_read_256_lut), (144 bytes).
    Removing host_lw_wrapper.o(i.host_lw_tx_fir_7tap_prog_28_lut), (6 bytes).
    Removing host_lw_wrapper.o(i.host_lw_tx_fir_7tap_read_28_lut), (144 bytes).
    Removing host_power_util.o(i.chal_ana_avs_set_volt), (180 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_avdd_slave), (156 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_avs_1_slaves), (156 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_avs_2_slaves), (304 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_avs_3_slaves), (452 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_avs_slaves), (600 bytes).
    Removing host_power_util.o(i.chal_ana_reg_disable_vddm_slave), (156 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avdd_slave), (160 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avs_slave1), (166 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avs_slave2), (166 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avs_slave3), (166 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avs_slave4), (166 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_avs_slaves), (616 bytes).
    Removing host_power_util.o(i.chal_ana_reg_enable_vddm_slave), (160 bytes).
    Removing host_power_util.o(i.host_util_ana_disable_all_regs), (568 bytes).
    Removing host_power_util.o(i.top_supspend_resume), (262 bytes).
    Removing host_test.o(i.host_test_check_port_validation), (40 bytes).
    Removing host_test.o(i.host_test_get_valid_port_mask), (24 bytes).
    Removing host_to_chip_ipc.o(i.intf_read_memory), (28 bytes).
    Removing hw_mutex_handler.o(i.acquire_hw_mutex), (66 bytes).
    Removing hw_mutex_handler.o(i.release_hw_mutex), (30 bytes).
    Removing chal_cw_prbs.o(i.chal_cw_pmon_clr_accs), (144 bytes).
    Removing chal_cw_prbs.o(i.chal_cw_pmon_clr_all), (18 bytes).
    Removing chal_cw_prbs.o(i.chal_cw_pmon_clr_lol), (66 bytes).
    Removing chal_cw_rtmr_kp4prbs.o(i.chal_cw_rtmr_fec_pmon_clr_all), (92 bytes).
    Removing chal_cw_utils.o(i.dup2_even), (36 bytes).
    Removing chal_cw_utils.o(i.dup4), (74 bytes).
    Removing chal_cw_utils.o(i.dup4_even), (68 bytes).
    Removing chal_cw_utils.o(i.get_num_bits), (30 bytes).
    Removing chal_gpio.o(i.chal_gpio_get_dir), (78 bytes).
    Removing chal_gpio.o(i.chal_gpio_get_input), (78 bytes).
    Removing chal_gpio.o(i.chal_gpio_get_pulldown), (80 bytes).
    Removing chal_gpio.o(i.chal_gpio_get_pullup), (80 bytes).
    Removing chal_gpio.o(i.chal_gpio_set_dir), (104 bytes).
    Removing chal_gpio.o(i.chal_gpio_set_output), (98 bytes).
    Removing chal_gpio.o(i.chal_gpio_set_pulldown), (84 bytes).
    Removing chal_gpio.o(i.chal_gpio_set_pullup), (84 bytes).
    Removing dsp_utils.o(i.lw_util_init_lane_hw_base_addr), (64 bytes).
    Removing common_util.o(i.fw_read_chip_id), (68 bytes).
    Removing common_util.o(i.fw_read_package_id), (68 bytes).
    Removing common_util.o(i.signext), (12 bytes).
    Removing common_util.o(i.wr_reg_field), (108 bytes).
    Removing hr_time.o(i.HATS_DelayMs), (12 bytes).
    Removing hr_time.o(i.HATS_DelayUsec), (12 bytes).
    Removing hr_time.o(i.delay_s), (18 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing cfcmple.o(.text), (20 bytes).
    Removing cfrcmple.o(.text), (20 bytes).

627 unused section(s) (total 66119 bytes) removed from the image.
