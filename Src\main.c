
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * This notice applies to any and all portions of this file
  * that are not between comment pairs USER CODE BEGIN and
  * USER CODE END. Other portions of this file, whether 
  * inserted by the user or by software development tools
  * are owned by their respective copyright owners.
  *
  * Copyright (c) 2019 STMicroelectronics International N.V. 
  * All rights reserved.
  *
  * Redistribution and use in source and binary forms, with or without 
  * modification, are permitted, provided that the following conditions are met:
  *
  * 1. Redistribution of source code must retain the above copyright notice, 
  *    this list of conditions and the following disclaimer.
  * 2. Redistributions in binary form must reproduce the above copyright notice,
  *    this list of conditions and the following disclaimer in the documentation
  *    and/or other materials provided with the distribution.
  * 3. Neither the name of STMicroelectronics nor the names of other 
  *    contributors to this software may be used to endorse or promote products 
  *    derived from this software without specific written permission.
  * 4. This software, including modifications and/or derivative works of this 
  *    software, must execute solely and exclusively on microcontroller or
  *    microprocessor devices manufactured by or for STMicroelectronics.
  * 5. Redistribution and use of this software other than as permitted under 
  *    this license is void and will automatically terminate your rights under 
  *    this license. 
  *
  * THIS SOFTWARE IS PROVIDED BY STMICROELECTRONICS AND CONTRIBUTORS "AS IS" 
  * AND ANY EXPRESS, IMPLIED OR STATUTORY WARRANTIES, INCLUDING, BUT NOT 
  * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
  * PARTICULAR PURPOSE AND NON-INFRINGEMENT OF THIRD PARTY INTELLECTUAL PROPERTY
  * RIGHTS ARE DISCLAIMED TO THE FULLEST EXTENT PERMITTED BY LAW. IN NO EVENT 
  * SHALL STMICROELECTRONICS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, 
  * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF 
  * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING 
  * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32f2xx_hal.h"
#include "usb_device.h"
#include "gpio.h"
#include "mdio.h"
#include "dac.h"
#include "adc.h"
#include "dma.h"
#include "rng.h"
#include "capi_def.h"
#include "bcm87800.h"
#include "master_i2c.h"
#include "master_i2c_mod.h"
#include "master_i2c_pm.h"
#include "usbd_cdc.h"
#include "usbd_cdc_if.h"
#include "clock-Freq-Registers.h"
#include "usart.h"
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private variables ---------------------------------------------------------*/
#define FLASH_USER_START_ADDR ADDR_FLASH_SECTOR_11
#define Channel_Num  2
#define Sample_Num  16
uint32_t ADC_ConvertedValue[Sample_Num][Channel_Num];

uint32_t accum_time = 0;
uint8_t  TimerFlag =0;
uint32_t bert_TimeCountStart[2][8];

uint8_t C4_0_255_Table[256];
C4_Table_TypeDef* C4 = (C4_Table_TypeDef*)C4_0_255_Table;
bert_TypeDef BER[2][8];
uint32_t st=0,et=0;
uint8_t interface = 0;  //
uint16_t DAC_ValueCompare;
uint16_t DAC_TxI_Offset;
uint8_t LPMode_Compare;
uint8_t RST_Compare;
uint8_t PM_Compare;
extern RNG_HandleTypeDef hrng;
/* USER CODE BEGIN PV */
/* Private variables ---------------------------------------------------------*/

uint32_t MCUID[3];

_USB_State hid_State;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Process_ReceiveData(void);
void Update_ErrorCount(void);
void Flash_Read(uint32_t addr, uint8_t *buffer, uint32_t length);
HAL_StatusTypeDef Flash_Write(uint32_t addr, uint8_t *buffer, uint32_t length);
void SPI_DAC_Output(void);
void Monitor(void);
void Monitor_Temp(void);


/* USER CODE BEGIN PFP */
/* Private function prototypes -----------------------------------------------*/
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/* USER CODE BEGIN PFP */
/* Private function prototypes -----------------------------------------------*/

/* USER CODE END PFP */

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  *
  * @retval None
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
	uint32_t LoopCount=0;
	uint16_t i=0,n=0;
	char *VendorName = "OVLINK";
	char EVB_SN[16];
	uint16_t SampleCount = 0;
	bool dsp_init;
    /* USER CODE END 1 */

    /* MCU Configuration----------------------------------------------------------*/

    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();

    /* USER CODE BEGIN Init */
	MCUID[0]=*(uint32_t*)(STM32F2_ID);
    MCUID[1]=*(uint32_t*)(STM32F2_ID+4);
    MCUID[2]=*(uint32_t*)(STM32F2_ID+8);
	uint8_t arrMCUID[4];
	memcpy((uint8_t*)arrMCUID,&MCUID[2],4);
	sprintf(EVB_SN,"BX%.8X%.4X",MCUID[0],MCUID[1]&0xffff);
	
    /* USER CODE END Init */

    /* Configure the system clock */
    SystemClock_Config();
	MX_GPIO_Init();
	MX_DMA_Init();
	MX_ADC1_Init();
	MX_DAC_Init();
	MX_SPI1_Init();
	
    /* USER CODE BEGIN SysInit */
	Flash_Read(FLASH_USER_START_ADDR,C4_0_255_Table,256);
	for(i=0;i<128;i++)
	{
		if(C4_0_255_Table[i]==0xFF) n++;
	}
	if(n>127)   //默认参数
	{
		memset(&C4_0_255_Table[0],0x00,32);
		
		memcpy(C4->VendorName,VendorName,strlen(VendorName));   //16bytes
		memcpy(C4->EVB_SN,EVB_SN,strlen(EVB_SN));			          //16bytes

		C4->Password[0] = 0x47;
		C4->Password[1] = 0x45;
		C4->Password[2] = 0x52;
		C4->Password[3] = 0x44;
		
		C4->Interface = 0;
		C4->SignalMode = CAPI_MODULATION_PAM4;
		C4->DataRate = 0x0e;
		C4->ClkDivide = 0x02;
		C4->RxAutoInvert = 0;
		C4->rng_value = HAL_RNG_GetRandomNumber(&hrng)&0x00fffff;
		
		C4->VCC_DAC = 3300;
		C4->VCC_DAC_slope = 256;
		C4->VCC_DAC_offset = 0;
		C4->I_slope = 256;
		C4->I_offset = 0;
		C4->Vcc_slope = 256;
		C4->Vcc_offset = 0;
		C4->Temp_offset = 0;
		C4->IALARM = 8000;
		
		C4->hostline_Taps[0] = 0;
		C4->hostline_Taps[1] = -30;
		C4->hostline_Taps[2] = 130;
		C4->hostline_Taps[3] = 0;
		C4->hostline_Taps[4] = 0;
		C4->hostline_Taps[5] = 0;
		C4->hostline_Taps[6] = 0;
		
		Flash_Write(FLASH_USER_START_ADDR,C4_0_255_Table,256);
		HAL_Delay(100);
	}
	
	
	
	//C4->DataRate 
	for(i=0;i<8;i++)
	{
		
//	C4->HostTxPattern[i] = 8;
//	C4->HostRxPattern[i] = 8;

//	C4->TxSquelch[i]    = 0;
//	C4->TxInvert[i] = 0;
//	C4->RxInvert[i] = 0;

		C4->LaneTxPattern[i] = 8;
		C4->LaneRxPattern[i] = 8;


		BER[0][i].FECEn_Flag = 0;
		BER[0][i].FECEn_HistoryFlag = 0;
		BER[0][i].EDEn_Flag = 0;
		BER[0][i].EDEn_HistoryFlag = 0;
		BER[0][i].lane_num_taps = 0;
		BER[1][i].lane_num_taps = 0;
	}

	
	C4->gain_boost = 0;
	C4->peaking_filter = 0;
	C4->host_rx_bw_value = 0;
	C4->lane_rx_bw_value = 0;
	C4->vga = 0;
	C4->Interface = 0;
	C4->RxAutoInvert = 0;
	
	
//	C4->ClkDivide = 0x02;
//	C4->DataRate = 0x0e;
//	C4->SignalMode = CAPI_MODULATION_PAM4;

//	C4->host_Taps[0] = 0;
//	C4->host_Taps[1] = -30;
//	C4->host_Taps[2] = 130;
//	C4->host_Taps[3] = 0;
//	C4->host_Taps[4] = 0;
//	C4->host_Taps[5] = 0;
//	C4->host_Taps[6] = 0;
	
	
  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
	hid_State = None;
	MX_USB_DEVICE_Init();
	MX_USART3_UART_Init();
	SPI_DAC_Output();
	HAL_ADC_Start_DMA(&hadc1,(uint32_t*)&ADC_ConvertedValue,Sample_Num*Channel_Num);
	//HAL_ADC_Start_DMA(&hadc1,(uint32_t*)&ADC_ConvertedValue,Sample_Num*Channel_Num);
	
	uint16_t SHUNT_CAL ;//= 0.00512/Current_LSB = 200uA * RSHUNT = 0.01R
	SHUNT_CAL = 2560;
	I2C_WriteWord_PM(0x80,0x00,0x4527);
	I2C_WriteWord_PM(0x80,0x05,SHUNT_CAL);

  /* USER CODE BEGIN 2 */
	if(MCUID[0]==((~C4->MCU_ID[0])-0x112233) && MCUID[1]==((~C4->MCU_ID[1])-C4->rng_value) && MCUID[2]==((~C4->MCU_ID[2])-0x223311))
	{
		C4->Licence = 0x80;
	}
    else
    {
        C4->Licence =0;
    }
	C4->Licence = 0x80;
	if(C4->Licence == 0x80)
	{
		bcm87800_download_firmware();
		dsp_init = bcm87800_init_prbs(1);
	}

    /* USER CODE END 2 */

    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {

        /* USER CODE END WHILE */
        LoopCount++;
        SampleCount++;
        if(LoopCount>0x3ff && dsp_init)
        {
            LoopCount = 0;
            HAL_GPIO_TogglePin(LINK_GPIO_Port,LINK_Pin);
        }
        else if(LoopCount>0x3ff && !dsp_init)
        {
            LoopCount = 0;
            HAL_GPIO_WritePin(LINK_GPIO_Port,LINK_Pin,GPIO_PIN_RESET);
        }
        Process_ReceiveData();
        SPI_DAC_Output();
        Monitor();
        Monitor_Temp();
        /* USER CODE BEGIN 3 */
    }
  /* USER CODE END 3 */

}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{

  RCC_OscInitTypeDef RCC_OscInitStruct;
  RCC_ClkInitTypeDef RCC_ClkInitStruct;

    /**Initializes the CPU, AHB and APB busses clocks 
    */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 20;
  RCC_OscInitStruct.PLL.PLLN = 192;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 5;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

    /**Initializes the CPU, AHB and APB busses clocks 
    */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
  {
    Error_Handler();
  }

    /**Configure the Systick interrupt time 
    */
  HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq()/1000);

    /**Configure the Systick 
    */
  HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);

  /* SysTick_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(SysTick_IRQn, 0, 0);
}

/* USER CODE BEGIN 4 */
uint16_t ReadADCAverageValue(uint16_t Channel)
{
  uint8_t i,j;
  uint32_t ADC_Sum = 0,adc_swap;
	uint16_t adc_value[16];
	
	for(i=0; i<Sample_Num; i++)
	{
		 ADC_Sum +=ADC_ConvertedValue[i][Channel];
	}
	ADC_Sum=ADC_Sum>>4;
	if(ADC_Sum>4095)
	{
		ADC_Sum=4095;
	}
	return(ADC_Sum);
}

void Monitor_Temp(void)
{
	unsigned int ADvalue=0;
	ADvalue = ReadADCAverageValue(1);
	float f_temp = (ADvalue*3388/4096.0-760)/2.5+25;
	
	f_temp += C4->Temp_offset;
	C4->DDM_Temp = f_temp*256;
}

void Monitor(void)
{
	unsigned int ADvalue=0;
	signed int offset=0;
	unsigned int slope=0;
	float f_temp =0.0;
	unsigned int TxIOffsetTemp = 0;
	float f_Offset = 0.0;
	uint16_t buffer[4]={0};

	I2C_ReadWord_PM(0x80,1,&buffer[0]);
	I2C_ReadWord_PM(0x80,2,&buffer[1]);
	I2C_ReadWord_PM(0x80,3,&buffer[2]);
	I2C_ReadWord_PM(0x80,4,&buffer[3]);

	C4->DDM_POWER = (float)buffer[2]*200e-3*32;
	
	ADvalue = buffer[3];
	f_temp = (float)ADvalue*200e-6; 
	f_temp = f_temp *1000;           	//mA
	f_Offset = f_temp;

	slope = C4->I_slope;
	offset = C4->I_offset;
	f_temp = f_temp*slope;
	f_temp = f_temp/256;
	f_temp += offset;
	C4->DDM_I = (uint16_t)f_temp;
	
	TxIOffsetTemp = f_Offset*0.035;  //校准没插模块偏差 0.175是电感0.07和0.025R电阻之和，1.221是AD5318的精度   
	DAC_TxI_Offset = TxIOffsetTemp;

	ADvalue =ReadADCAverageValue(0);	   //P2.2 VCCTD
	f_temp = (float)ADvalue;
	f_temp = (f_temp*VREF/ADC_MAX);         	//V
	f_temp *=2000;							              //MV

	slope = C4->Vcc_slope;
	offset = C4->Vcc_offset;
	f_temp = f_temp*slope;
	f_temp = f_temp/256;
	f_temp += offset;
	C4->DDM_Vcc  = (uint16_t) f_temp;
}

void SPI_DAC_Output(void)
{
	signed int offset=0;
	float f_temp;
	unsigned int slope = 0,diffVal = 0;
	uint16_t DAC_UN_Value,OutDAC = 420;
	
	//校准
	DAC_UN_Value = C4->VCC_DAC;
	slope  = C4->VCC_DAC_slope;
	offset = C4->VCC_DAC_offset;

	f_temp = DAC_UN_Value;
	f_temp += offset;
	DAC_UN_Value = f_temp;
	
	DAC_UN_Value +=DAC_TxI_Offset;
	if(DAC_UN_Value >ADC_MAX) DAC_UN_Value = ADC_MAX;
	
	diffVal=abs(DAC_UN_Value-DAC_ValueCompare);
	if(DAC_UN_Value != DAC_ValueCompare && diffVal>1)
	{
        DAC_ValueCompare = DAC_UN_Value; 
    
        OutDAC = (3.930222 - DAC_UN_Value/1000.0)/0.000949;
        WriteOutputDAC(1,OutDAC);
	}

}

void SetOutputRefClock(uint8_t Freq)
{
	uint16_t n = 0;
	clock_register_t reg; 
	
	for(n=0;n<20;n++)
	{
		reg=Clock_FreqConfig[Freq][n];
		I2C_WriteByte(0xb2,reg.address,reg.value);
		HAL_Delay(2);
	}
	//HAL_Delay(50);
}

uint32_t Get_Time_Diff(uint32_t u32_TimeStart)
{
    uint32_t u32_TimeAct;

    TimerFlag = 0;
    u32_TimeAct = accum_time; //Get actual time
    if(TimerFlag)
    {
        u32_TimeAct = accum_time;     
    }
    if(u32_TimeStart<=u32_TimeAct)
    {
        return (u32_TimeAct - u32_TimeStart);       //account time /2
    }
    else
    {
        return ((0xFFFFFFFF - u32_TimeStart)+ u32_TimeAct);    //overflow
    }
}

void Update_ErrorCount(void)
{
	for(uint8_t ch = 0; ch<8;ch++)
	{
		if (BER[0][ch].EDEn_Flag||BER[0][ch].EDEn_HistoryFlag)
		{
			get_line_Checker_Status(ch);
			BER[0][ch].EDEn_HistoryFlag = BER[0][ch].EDEn_Flag;	
		}
		if (BER[1][ch].EDEn_Flag||BER[1][ch].EDEn_HistoryFlag)
		{
			get_host_Checker_Status(ch);
			BER[1][ch].EDEn_HistoryFlag = BER[1][ch].EDEn_Flag;	
		}
	}
}

int splitcmd(char* str, const char* spl,char* maincmd,char* subcmd,uint8_t *val)
{
    char dst[10][100];
    int n = 0;
    char *result = NULL;
    result = strtok(str, spl);
    while( result != NULL )
    {
        strcpy(dst[n++], result);
        result = strtok(NULL, spl);
    }

    sprintf(maincmd,"%s\r\n",dst[0]);
    *val=0;
    for(int j=15;j>=0;j--)
    {
        char temp[10];
        sprintf(temp,"%d",j);
        char *p = strstr(maincmd,temp);
        if(p!=NULL) 
        {
            *val=j; 
            break;
        }
    }
    if(n>1)
    {
        sprintf(subcmd,"%s","");
        for(int i=1;i<n;i++)
        {
            strcat(subcmd, ":");
            strcat(subcmd, dst[i]);
        }
    }
    return n;
}

int split(char dst[][100], char* str, const char* spl)
{
    int n = 0;
    char *result = NULL;
    result = strtok(str, spl);
    while( result != NULL )
    {
        strcpy(dst[n++], result);
        result = strtok(NULL, spl);
    }
    return n;
}

void SendData(char* strval)
{
    //USBD_CDC_SetTxBuffer(&hUsbDeviceFS, (uint8_t*)strval, strlen(strval));
    //USBD_CDC_TransmitPacket(&hUsbDeviceFS);
	if(interface == 1) UsartSendCmd((uint8_t*)strval);
  else	CDC_Transmit_FS((uint8_t*)strval, strlen(strval));
}

uint8_t getcommand(char* strcmd,char dst[][100])
{
	if(strncmp((char*)UserRxBufferFS,strcmd,strlen(strcmd)) == 0) 
	{
		split(dst,(char*)UserRxBufferFS, " ");
		return 1;
	}
	else return 0;
}

int getcommand_value(char* strcmd,int *val,uint8_t *itype)
{
	char dst[10][100],temp[10];
	char *value;
	int val_cnt = 0;
	
	*itype = 1;
	if(strncmp((char*)UserRxBufferFS,strcmd,strlen(strcmd)) == 0) 
	{
		int n = split(dst,(char*)UserRxBufferFS, ":");
		value = dst[n-1];
		if(strstr(value,"?")!=NULL||strstr(value," ")!=NULL||strstr(value,"CHAN")!=NULL)
		{
            int m = split(&dst[n-1],value, " ");
            for(int j=16;j>=1;j--)
            {
                sprintf(temp,"%d",j);
                char *p = strstr(dst[n-1],temp);
                if(p!=NULL) 
                {
                    val_cnt++;
                    val[0]=j; 
                    break;
                }
            }
            for(int i = 0;i<m-1;i++)
            {
                sscanf(dst[n+i], "%x",&val[val_cnt]);
                val_cnt++;
            }
            *itype = strstr(value,"?")!=NULL? 2:1;
		}
		return 1;
	}
	else return 0;
}

int getcommand_value_float(char* strallcmd,char* strsubcmd,double *val,uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if(strncmp(strallcmd,strsubcmd,strlen(strsubcmd)) == 0) 
	{
		int n = split(dst,strallcmd, ":");
		value = dst[n-1];
		if(strstr(value,"?")!=NULL ||strstr(value," ")!=NULL)
		{
			*itype = strstr(value,"?")!=NULL? 2:1;
			//if(*itype==1)
			//{
				int nn = split(&dst[0],value, " ");
				int ll = split(&dst[0],dst[1], ",");
				for(int i = 0;i<ll;i++)
				{
                    sscanf(dst[i], "%lf",&val[val_cnt]);
                    val_cnt++;
				}
			//}
		}
		return 1;
	}
	else return 0;
}

int getcommand_value_int(char* strallcmd,char* strsubcmd,int *val,uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if(strncmp(strallcmd,strsubcmd,strlen(strsubcmd)) == 0) 
	{
		int n = split(dst,strallcmd, ":");
		value = dst[n-1];
		if(strstr(value,"?")!=NULL ||strstr(value," ")!=NULL)
		{
			*itype = strstr(value,"?")!=NULL? 2:1;

            int nn = split(&dst[0],value, " ");
            int ll = split(&dst[0],dst[1], ",");
            for(int i = 0;i<ll;i++)
            {
                sscanf(dst[i], "%d",&val[val_cnt]);
                val_cnt++;
            }
		}
		return 1;
	}
	else return 0;
}

int getcommand_value_hexint(char* strallcmd,char* strsubcmd,int *val,uint8_t *itype)
{
	char dst[10][100];
	char *value;
	int val_cnt = 0;

	*itype = 1;
	if(strncmp(strallcmd,strsubcmd,strlen(strsubcmd)) == 0) 
	{
		int n = split(dst,strallcmd, ":");
		value = dst[n-1];
		if(strstr(value,"?")!=NULL ||strstr(value," ")!=NULL)
		{
			*itype = strstr(value,"?")!=NULL? 2:1;

            int nn = split(&dst[0],value, " ");
            int ll = split(&dst[0],dst[1], ",");
            for(int i = 0;i<ll;i++)
            {
                sscanf(dst[i], "%x",&val[val_cnt]);
                val_cnt++;
            }
		}
		return 1;
	}
	else return 0;
}


//===============================================
void CheckInput_Statue(uint8_t rw)
{
    if(rw>0)
    {
        //TXDIS
        if(LPMode_Compare != C4->LPMode)
        {
            LPMode_Compare = C4->LPMode;
            if(C4->LPMode) HAL_GPIO_WritePin(LPMode_GPIO_Port, LPMode_Pin, GPIO_PIN_SET );	
            else HAL_GPIO_WritePin(LPMode_GPIO_Port, LPMode_Pin, GPIO_PIN_RESET );

        } 	
        if(RST_Compare != C4->ResetL)
        {
            RST_Compare = C4->ResetL;
            if(C4->ResetL) HAL_GPIO_WritePin(ResetL_GPIO_Port, ResetL_Pin, GPIO_PIN_SET );	
            else HAL_GPIO_WritePin(ResetL_GPIO_Port, ResetL_Pin, GPIO_PIN_RESET );
            HAL_Delay(2);
        }
        if(PM_Compare != C4->Power)
        {
            PM_Compare = C4->Power;

            if(C4->Power) HAL_GPIO_WritePin(PM_GPIO_Port, PM_Pin, GPIO_PIN_SET );	
            else HAL_GPIO_WritePin(PM_GPIO_Port, PM_Pin, GPIO_PIN_RESET );
            HAL_Delay(2);
        }
    }
    else
    {
      C4->LPMode  = HAL_GPIO_ReadPin(LPMode_GPIO_Port, LPMode_Pin);
      C4->ResetL  = HAL_GPIO_ReadPin(ResetL_GPIO_Port, ResetL_Pin);
      C4->ModPrsL = HAL_GPIO_ReadPin(ModPrsL_GPIO_Port, ModPrsL_Pin);
      C4->ModSEL = HAL_GPIO_ReadPin(ModSEL_GPIO_Port, ModSEL_Pin);
      C4->IntL   =  HAL_GPIO_ReadPin(IntL_GPIO_Port, IntL_Pin);
      C4->Power   =  HAL_GPIO_ReadPin(PM_GPIO_Port, PM_Pin);
    }
}


/**
  * @brief  This function is receive usb to vcp data.
  * @param  none
  * @retval None
  */
void Process_ReceiveData(void)
{
	char dst[10][100], strdata[512];
	uint8_t ch=0,itype;
	double fvalue[10]={0};
	int ivalue[264];
	char maincmd[20], subcmd[250];
	bool ret = false;
	
	if(RxFlag == 1)
	{
		RxFlag = 0x00;
		HAL_GPIO_TogglePin(LINK_GPIO_Port,LINK_Pin);
		if(getcommand("*IDN?",dst))
		{
			char sn[16]={32};
			for(int n=0;n<16;n++)
			{
				if(C4->EVB_SN[n]==0x20 || C4->EVB_SN[n]==0x00) break;
				sn[n] = C4->EVB_SN[n];
			}
			//固件版本：V1.1 2019-06-23
			sprintf(strdata,"OVLINK,DEBX-08112A-VA,%s,V1.84,%s\r\n","2025022002",__DATE__);	//__DATE__" "__TIME__;
			SendData(strdata);
		}
		else if(getcommand("*RST",dst))
		{
			SendData("1\r\n");
			HAL_Delay(20);
			__set_FAULTMASK(1);		//关闭所有中断
			NVIC_SystemReset();		//复位
			
		}
		memset(UserRxBufferFS,0,APP_TX_DATA_SIZE);
	}
	else if(RxFlag == 2)	
	{	
		RxFlag=0x00;
		HAL_GPIO_TogglePin(LINK_GPIO_Port,LINK_Pin);
		splitcmd((char*)UserRxBufferFS,":",maincmd,subcmd,&ch);
		
		if (strncmp(maincmd, "SYS", 3) == 0)
		{
			//1. 设置通道速率
			if(getcommand_value_int(subcmd,":DATARATE",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->DataRate = ivalue[0];
					if(bcm87800_init_prbs(0))
						SendData("1\r\n");
					else
					{
						SendData("0\r\n");
					
						HAL_GPIO_WritePin(RESET_DSP_GPIO_Port,RESET_DSP_Pin,(GPIO_PinState)0);
						HAL_Delay(20);
////						__set_FAULTMASK(1);		//关闭所有中断
//						NVIC_SystemReset();		//复位
						
						bcm87800_download_firmware();
						bool init = bcm87800_init_prbs(1);
					}
						//SendData("0\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",C4->DataRate);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":EYEMODE",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->SignalMode = ivalue[0];
					if(bcm87800_init_prbs(0))
						SendData("1\r\n");
					else
					{
						SendData("0\r\n");
					
						HAL_GPIO_WritePin(RESET_DSP_GPIO_Port,RESET_DSP_Pin,(GPIO_PinState)0);
						HAL_Delay(20);
////						__set_FAULTMASK(1);		//关闭所有中断
//						NVIC_SystemReset();		//复位
						
						bcm87800_download_firmware();
						bool init = bcm87800_init_prbs(1);
					}
						//SendData("0\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",C4->SignalMode);
					SendData(strdata);
				}
			}
			else if(getcommand_value_float(subcmd,":REFCLK",fvalue,&itype))
			{
				if(itype==1)
				{
					SetOutputRefClock(fvalue[0]);
					SendData("1\r\n");
				}
			}
			else if(getcommand_value_int(subcmd,":RST",ivalue,&itype))
			{
				if(itype==1)
				{
					SendData("1\r\n");
					HAL_Delay(10);
					__set_FAULTMASK(1);		//关闭所有中断
					NVIC_SystemReset();		//复位
				}
			}
			else if(getcommand_value_int(subcmd,":DSPRST",ivalue,&itype))
			{
				uint8_t State = ivalue[0];
				HAL_GPIO_WritePin(RESET_DSP_GPIO_Port,RESET_DSP_Pin,(GPIO_PinState)State);
				SendData("1\r\n");
			}			
			else if(getcommand_value_int(subcmd,":FECPGEN",ivalue,&itype))
			{
				if(itype==1 && C4->SignalMode == 2)
				{
					if(bcm87800_set_fec_pgen(ivalue[0]))
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",1);
					SendData(strdata);
				}
			}
			//切换触发分频
			else if(getcommand_value_int(subcmd,":TRIG",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->ClkDivide = ivalue[0];
					if(bcm87800_clock_divider2())
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else
				{
					sprintf(strdata,"%d\r\n",C4->ClkDivide);
					SendData(strdata);
				}
			}
			else if(getcommand_value_hexint(subcmd,":IIC",ivalue,&itype))
			{
				uint8_t devadd, regaddr = 0;
				uint16_t size = 0;
				uint8_t regval[128];
				
				devadd = ivalue[0];
				regaddr = ivalue[1];
				size = ivalue[2];
				
				if(size==0) size = 1;
				if(size>16) size = 16;
				if(itype==1)
				{
					for(uint16_t i=0;i<size;i++)
					{
						regval[i] = ivalue[i+3];
					}
					if(I2C_WriteBytes(devadd,regaddr,regval,size))
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					uint8_t ret = 0;

					ret = I2C_ReadBytes(devadd,regaddr,regval,size);
					if(ret>0)
					{
						char temp[10];
						for(int n=0;n<size;n++)
						{
							sprintf(temp,"%.2X",regval[n]);
							if(n==0)
							{
								sprintf(strdata,"%s,",strcpy(strdata,temp));
							}
							else
							{
								sprintf(strdata,"%s,",strcat(strdata,temp));
							}
						}
						//strdata[size*3-1]='\0';
						char str[50];
						sprintf(str,"\r\n");
				    strcat(strdata,str);
						
						SendData(strdata);
					}
					else
					{
						SendData(":SYS:I2C NG\r\n");
					}
				}
			}
			else if(getcommand_value_int(subcmd,":DUT:STATE",ivalue,&itype))
			{
				if(itype==1)
				{
					C4_0_255_Table[128] = ivalue[0]&0xff;
					CheckInput_Statue(1);
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					CheckInput_Statue(0);
					sprintf(strdata,"%d\r\n",C4_0_255_Table[128]);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":PWONOFF",ivalue,&itype))
			{
				if(itype==1)
				{

					HAL_GPIO_WritePin(PM_GPIO_Port,PM_Pin,(GPIO_PinState)(ivalue[0])); 
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",HAL_GPIO_ReadPin(PM_GPIO_Port,PM_Pin)); 
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":LPMODE",ivalue,&itype))
			{
				if(itype==1)
				{

					HAL_GPIO_WritePin(LPMode_GPIO_Port,LPMode_Pin,(GPIO_PinState)(ivalue[0])); 
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",HAL_GPIO_ReadPin(LPMode_GPIO_Port,LPMode_Pin)); 
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":RESETL",ivalue,&itype))
			{
				if(itype==1)
				{

					HAL_GPIO_WritePin(ResetL_GPIO_Port,ResetL_Pin,(GPIO_PinState)(ivalue[0])); 
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",HAL_GPIO_ReadPin(ResetL_GPIO_Port,ResetL_Pin)); 
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":MODSEL",ivalue,&itype))
			{
				if(itype==1)
				{

					HAL_GPIO_WritePin(ModSEL_GPIO_Port,ModSEL_Pin,(GPIO_PinState)(ivalue[0])); 
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",HAL_GPIO_ReadPin(ModSEL_GPIO_Port,ModSEL_Pin)); 
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":VCCDAC",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->VCC_DAC =  ivalue[0];
					SPI_DAC_Output();
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d\r\n",C4->VCC_DAC);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":DDM",ivalue,&itype))
			{
				if(itype==2)
				{
					sprintf(strdata,"%d,%d,%d,%d\r\n",C4->DDM_Temp,C4->DDM_Vcc,C4->DDM_I,C4->DDM_POWER);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":IALARM",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->IALARM = ivalue[0];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d\r\n",C4->IALARM );
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":CAL:BIAS",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->I_slope =  ivalue[0];
					C4->I_offset = ivalue[1];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d,%d\r\n",C4->I_slope,C4->I_offset);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":CAL:TEMP",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->Temp_offset =  ivalue[1];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d,%d\r\n",0x100,C4->Temp_offset);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":CAL:VCC",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->Vcc_slope =  ivalue[0];
					C4->Vcc_offset = ivalue[1];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d,%d\r\n",C4->Vcc_slope,C4->Vcc_offset);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":CAL:SETVCC",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->VCC_DAC_slope =  ivalue[0];
					C4->VCC_DAC_offset = ivalue[1];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d,%d\r\n",C4->VCC_DAC_slope,C4->VCC_DAC_offset);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":INTERDIR",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->Interface =  ivalue[0];
					SendData("1\r\n");
				}
				else
				{
					sprintf(strdata,"%d\r\n",C4->Interface);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":CAL:SAVEALL",ivalue,&itype))
			{
				if(itype==1)
				{
					Flash_Write(FLASH_USER_START_ADDR,C4_0_255_Table,256);
					HAL_Delay(100);
					SendData("1\r\n");
				}
			}
			else
			{
				SendData("0\r\n");
			}
			memset(UserRxBufferFS,0,APP_TX_DATA_SIZE);
		}
		else if (strncmp(maincmd, "PPG", 3) == 0)
		{
			//1.使能 PPG 通道输入输出
			if(getcommand_value_int(subcmd,":SQUELCH",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->TxSquelch[ch] = ivalue[0];
					if(bcm87800_tx_squelch(ch,C4->TxSquelch[ch]))
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",C4->TxSquelch[ch]);
					SendData(strdata);
				}
			}
			//2.使能 PPG 所有通道输入输出
			else if(getcommand_value_int(subcmd,":ALL:SQUELCH",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					for(ch=0;ch<8;ch++)
					{
						C4->TxSquelch[ch] = ivalue[0];
						ret &= bcm87800_tx_squelch(ch,C4->TxSquelch[ch]);
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else
				{
					SendData("0\r\n");
				}
			}
			//5. 配置通道的码型
			//	PRBS7,PRBS9_4,PRBS9_5,PRBS11,PRBS13,PRBS15,PRBS16,PRBS23,PRBS31,PRBS58,FIXED,JP083B,LIN,CJT,SSPRQ			
			else if(getcommand_value_int(subcmd,":PATTERN",ivalue,&itype))
			{
				if(itype==1)
				{
					if(C4->Interface == 0) {
						C4->LaneTxPattern[ch] = ivalue[0]; 
						if(bcm87800_tx_prbs(ch,C4->LaneTxPattern[ch]))
						{
							SendData("1\r\n");
						}
						else
							SendData("0\r\n");
					}
					else
					{
						C4->HostTxPattern[ch] = ivalue[0]; 
						if(bcm87800_tx_prbs(ch,C4->HostTxPattern[ch]))
							SendData("1\r\n");
						else
							SendData("0\r\n");
					}
				}
				//6.查询通道的码型
				else if(itype==2)
				{
					if(C4->Interface == 0)
						sprintf(strdata,"%d\r\n",C4->LaneTxPattern[ch]);
					else 
						sprintf(strdata,"%d\r\n",C4->HostTxPattern[ch]);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":ALL:PATTERN",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					for(ch=0;ch<8;ch++)
					{
						if(C4->Interface == 0) {
							C4->LaneTxPattern[ch] = ivalue[0]; 
							ret &= bcm87800_tx_prbs(ch,C4->LaneTxPattern[ch]);
						}
						else
						{
							C4->HostTxPattern[ch] = ivalue[0]; 
							ret &= bcm87800_tx_prbs(ch,C4->HostTxPattern[ch]);
						}
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
			}
			else if(getcommand_value_int(subcmd,":USERPATTERN",ivalue,&itype))
			{
				if(itype==1)
				{
					if(C4->Interface == 0) 
                    {
						C4->LaneTxPattern[ch] = ivalue[0]; 
						if(bcm87800_tx_prbs(ch,C4->LaneTxPattern[ch]))
						{
							SendData("1\r\n");
						}
						else
							SendData("0\r\n");
					}
					else
					{
						C4->HostTxPattern[ch] = ivalue[0]; 
						if(bcm87800_tx_prbs(ch,C4->HostTxPattern[ch]))
							SendData("1\r\n");
						else
							SendData("0\r\n");
					}
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					//sprintf(strdata,"%d,%d,%d,%d\r\n",BER[ch].fixed_pat0,BER[ch].fixed_pat1,BER[ch].fixed_pat2,BER[ch].fixed_pat3);
					SendData(strdata);
				}
			}
			
			//9. 配置通道的输出幅值
			else if(getcommand_value_int(subcmd,":TAPS",ivalue,&itype))
			{
				if(itype==1)
				{
					BER[C4->Interface][ch].line_taps[0] = ivalue[0];
					BER[C4->Interface][ch].line_taps[1] = ivalue[1];
					BER[C4->Interface][ch].line_taps[2] = ivalue[2];
					BER[C4->Interface][ch].line_taps[3] = ivalue[3];
					BER[C4->Interface][ch].line_taps[4] = ivalue[4];
					BER[C4->Interface][ch].line_taps[5] = ivalue[5];
					BER[C4->Interface][ch].line_taps[6] = ivalue[6];
					BER[C4->Interface][ch].lane_num_taps = ivalue[7];
					if(bcm87800_tx_emphasis(ch))
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2)	//10. 查询通道的输出幅值
				{
                    sprintf(strdata,"%d,%d,%d,%d,%d,%d,%d,%d\r\n",
                    BER[C4->Interface][ch].line_taps[0],
                    BER[C4->Interface][ch].line_taps[1],
                    BER[C4->Interface][ch].line_taps[2],
                    BER[C4->Interface][ch].line_taps[3],
                    BER[C4->Interface][ch].line_taps[4],
                    BER[C4->Interface][ch].line_taps[5],
                    BER[C4->Interface][ch].line_taps[6],
                    BER[C4->Interface][ch].lane_num_taps);
					SendData(strdata);
				}
			}
			//11. 配置所有通道的输出幅值
			else if(getcommand_value_int(subcmd,":ALL:TAPS",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					
					C4->hostline_Taps[0] = ivalue[0];
					C4->hostline_Taps[1] = ivalue[1];
					C4->hostline_Taps[2] = ivalue[2];
					C4->hostline_Taps[3] = ivalue[3];
					C4->hostline_Taps[4] = ivalue[4];
					C4->hostline_Taps[5] = ivalue[5];
					C4->hostline_Taps[6] = ivalue[6];
					for(ch=0;ch<8;ch++)
					{
						BER[C4->Interface][ch].line_taps[0]=ivalue[0];
						BER[C4->Interface][ch].line_taps[1]=ivalue[1];
						BER[C4->Interface][ch].line_taps[2]=ivalue[2];
						BER[C4->Interface][ch].line_taps[3]=ivalue[3];
						BER[C4->Interface][ch].line_taps[4]=ivalue[4];
						BER[C4->Interface][ch].line_taps[5]=ivalue[5];
						BER[C4->Interface][ch].line_taps[6]=ivalue[6];
						BER[C4->Interface][ch].lane_num_taps=ivalue[7];
						ret &= bcm87800_tx_emphasis(ch);
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
			}
			//12. 配置输出通道极性
			else if(getcommand_value_int(subcmd,":POLA",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->TxInvert[ch] = ivalue[0];
					if(bcm87800_tx_polarity(ch,(C4->TxInvert[ch]==0)?true:false))
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2)	
				{
					sprintf(strdata,"%d\r\n",C4->TxInvert[ch]);
					SendData(strdata);
				}
			}
			//21. 配置所有通道输出极性
			else if(getcommand_value_int(subcmd,":ALL:POLA",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					for(ch=0;ch<8;ch++)
					{
						C4->TxInvert[ch] = ivalue[0];
						ret &= bcm87800_tx_polarity(ch,(C4->TxInvert[ch]==0)?true:false);
					}
					if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else
				{
					SendData("0\r\n");
				}
			}
			else
			{
				SendData("0\r\n");
			}
		}
		else if (strncmp(maincmd, "ED", 2) == 0)
		{
			//1. 配置通道的码型
			//	PRBS7,PRBS9_4,PRBS9_5,PRBS11,PRBS13,PRBS15,PRBS16,PRBS23,PRBS31,PRBS58,FIXED,JP083B,LIN,CJT,SSPRQ			
			if(getcommand_value_int(subcmd,":PATTERN",ivalue,&itype))
			{
				if(itype==1)
				{
					if(C4->Interface == 0) {
						C4->LaneRxPattern[ch] = ivalue[0]; 
						ret = bcm87800_rx_prbs(ch,C4->LaneRxPattern[ch]);
					}
					else
					{
						C4->HostRxPattern[ch] = ivalue[0]; 
						ret = bcm87800_rx_prbs(ch,C4->HostRxPattern[ch]);
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					if(C4->Interface == 0)
						sprintf(strdata,"%d\r\n",C4->LaneRxPattern[ch]);
					else
						sprintf(strdata,"%d\r\n",C4->HostRxPattern[ch]);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":ALL:PATTERN",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					for(ch=0;ch<8;ch++)
					{
						if(C4->Interface == 0) {
							C4->LaneRxPattern[ch] = ivalue[0]; 
							ret &= bcm87800_rx_prbs(ch,C4->LaneRxPattern[ch]);
						}
						else
						{
							C4->HostRxPattern[ch] = ivalue[0]; 
							ret &= bcm87800_rx_prbs(ch,C4->HostRxPattern[ch]);
						}
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
			}
			//3. 配置输入信号极性
			else if(getcommand_value_int(subcmd,":POLA",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->RxInvert[ch] = ivalue[0];
					ret = bcm87800_rx_polarity(ch,C4->RxInvert[ch]==0?true:false);
					if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",C4->RxInvert[ch]);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":RXINFO",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->gain_boost = ivalue[0];
					C4->peaking_filter = ivalue[1];
					if(C4->Interface == 1)
						C4->host_rx_bw_value = ivalue[2];
					else
						C4->lane_rx_bw_value = ivalue[2];
					
					C4->vga = ivalue[3];
					
					ret = bcm87800_set_rx_info(ch);
					if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
				else if(itype==2)
				{
					if(C4->Interface == 0)
						sprintf(strdata,"%d,%d,%d,%d\r\n",C4->gain_boost,C4->peaking_filter,C4->lane_rx_bw_value,C4->vga);
					else
						sprintf(strdata,"%d,%d,%d,%d\r\n",C4->gain_boost,C4->peaking_filter,C4->host_rx_bw_value,C4->vga);
					SendData(strdata);
				}
			}
			else if(getcommand_value_int(subcmd,":ALL:RXINFO",ivalue,&itype))
			{
				ret=true;
				for(ch=0;ch<8;ch++)
				{ 
					C4->gain_boost = ivalue[0];
					C4->peaking_filter = ivalue[1];
					if(C4->Interface == 1)
						C4->host_rx_bw_value = ivalue[2];
					else
						C4->lane_rx_bw_value = ivalue[2];
					C4->vga = ivalue[3];
					ret &= bcm87800_set_rx_info(ch);
				}
				if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
			}
			//3. 配置输入信号极性
			else if(getcommand_value_int(subcmd,":ALL:POLA",ivalue,&itype))
			{
				if(itype==1)
				{
					ret=true;
					for(ch=0;ch<8;ch++)
					{
						C4->RxInvert[ch] = ivalue[0];
						ret &= bcm87800_rx_polarity(ch,C4->RxInvert[ch]==0?true:false);
					}
					if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
				}
			}
			//7.开始误码测试
			else if(getcommand_value_int(subcmd,":MEAS:ENABLE",ivalue,&itype))
			{
				if(itype==1)
				{
					bert_TimeCountStart[C4->Interface][ch] = accum_time;
					if(C4->SignalMode!=2)
					{
						C4->RxEnable = ivalue[0];
						BER[C4->Interface][ch].RealErrCount_msb = 0;
						BER[C4->Interface][ch].RealErrCount_lsb = 0;
						BER[C4->Interface][ch].RealBitCount = 0;
						
						BER[C4->Interface][ch].AccumErrCount_msb = 0;
						BER[C4->Interface][ch].AccumErrCount_lsb = 0;
						BER[C4->Interface][ch].AccumBitCount = 0;
						BER[C4->Interface][ch].EDLockFlag = 0; 
						ret = bcm87800_enable_checker(ch);
						BER[C4->Interface][ch].EDEn_Flag = C4->RxEnable;
						BER[C4->Interface][ch].FECEn_Flag = 0;
					}
					else
					{
						C4->FEC_Enable = ivalue[0];

						ret = bcm87800_set_fec_init(ch,C4->FEC_Enable);
						ret &= bcm87800_set_fec_clear(ch);
						BER[C4->Interface][ch].EDEn_Flag = 0;
						BER[C4->Interface][ch].FECEn_Flag = C4->FEC_Enable;
						BER[C4->Interface][ch].pre_fec_ber = 0;
						BER[C4->Interface][ch].post_fec_ber = 0;
						BER[C4->Interface][ch].tot_frame_rev_cnt = 0;
						BER[C4->Interface][ch].tot_frame_corr_cnt = 0;
						BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;
						BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;
						BER[C4->Interface][ch].fec_lock_flag = 0;
						for(int c = 0; c < 16; c++)
							BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;
						
						//on fec
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2) //9. 查询误码测试
				{
					sprintf(strdata,"%d\r\n",BER[C4->Interface][ch].EDEn_Flag);
					SendData(strdata);
				}
			}
			//8. 开始所有通道误码测试
			else if(getcommand_value_int(subcmd,":ALL:MEAS:ENABLE",ivalue,&itype))
			{
				if(itype==1)
				{
					ret = true;
					for(ch=0;ch<8;ch++)
					{
						bert_TimeCountStart[C4->Interface][ch] = accum_time;
						if(C4->SignalMode!=2)
						{
							C4->RxEnable = ivalue[0];
							BER[C4->Interface][ch].RealErrCount_msb = 0;
							BER[C4->Interface][ch].RealErrCount_lsb = 0;
							BER[C4->Interface][ch].RealBitCount = 0;
							
							BER[C4->Interface][ch].AccumErrCount_msb = 0;
							BER[C4->Interface][ch].AccumErrCount_lsb = 0;
							BER[C4->Interface][ch].AccumBitCount = 0;
							BER[C4->Interface][ch].EDLockFlag = 0; 
							BER[C4->Interface][ch].EDEn_Flag = C4->RxEnable;
							BER[C4->Interface][ch].FECEn_Flag = 0;
							
							ret &=  bcm87800_enable_checker(ch);
						}
						else
						{
							C4->FEC_Enable = ivalue[0];
							BER[C4->Interface][ch].EDEn_Flag = 0;
							BER[C4->Interface][ch].FECEn_Flag = C4->FEC_Enable;
							ret &= bcm87800_set_fec_init(ch,C4->FEC_Enable);
							ret &= bcm87800_set_fec_clear(ch);
							BER[C4->Interface][ch].fec_lock_flag = 0;
							BER[C4->Interface][ch].pre_fec_ber = 0;
							BER[C4->Interface][ch].post_fec_ber = 0;
							BER[C4->Interface][ch].tot_frame_rev_cnt = 0;
							BER[C4->Interface][ch].tot_frame_corr_cnt = 0;
							BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;
							BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;
							for(int c = 0; c < 16; c++)
								BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;
							
							//on fec
						}
					}
					if(ret)
					{
						SendData("1\r\n");
					}
					else
						SendData("0\r\n");
				}
				else if(itype==2)	//10. 查询所有通道误码测试
				{
					SendData("0\r\n");
				}
				
			}
			//13. 清除通道误码数
			else if(getcommand_value_int(subcmd,":ERR:CLEAR",ivalue,&itype))
			{
				bert_TimeCountStart[C4->Interface][ch] = accum_time;
				if(C4->SignalMode!=2)
				{
					BER[C4->Interface][ch].RealErrCount_msb = 0;
					BER[C4->Interface][ch].RealErrCount_lsb = 0;
					BER[C4->Interface][ch].RealBitCount = 0;
					
					BER[C4->Interface][ch].AccumErrCount_msb = 0;
					BER[C4->Interface][ch].AccumErrCount_lsb = 0;
					BER[C4->Interface][ch].AccumBitCount = 0;
					BER[C4->Interface][ch].EDLockFlag = 0;
					ret = true;
				}
				else
				{
					BER[C4->Interface][ch].fec_lock_flag = 0;
					BER[C4->Interface][ch].pre_fec_ber = 0;
					BER[C4->Interface][ch].post_fec_ber = 0;
					BER[C4->Interface][ch].tot_frame_rev_cnt = 0;
					BER[C4->Interface][ch].tot_frame_corr_cnt = 0;
					BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;
					BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;
					ret = bcm87800_set_fec_clear(ch);
					for(int c = 0; c < 16; c++)
						BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;
				}
				if(ret)
						SendData("1\r\n");
					else
						SendData("0\r\n");
			}
			//14. 清除所有通道误码数
			else if(getcommand_value_int(subcmd,":ALL:ERR:CLEAR",ivalue,&itype))
			{
				ret=true;
				for(ch=0;ch<8;ch++)
				{
					bert_TimeCountStart[C4->Interface][ch] = accum_time;
					
					if(C4->SignalMode!=2)
					{
						BER[C4->Interface][ch].RealErrCount_msb = 0;
						BER[C4->Interface][ch].RealErrCount_lsb = 0;
						BER[C4->Interface][ch].RealBitCount = 0;
						
						BER[C4->Interface][ch].AccumErrCount_msb = 0;
						BER[C4->Interface][ch].AccumErrCount_lsb = 0;
						BER[C4->Interface][ch].AccumBitCount = 0;
						BER[C4->Interface][ch].EDLockFlag = 0;
						ret = true;
					}
					else
					{
						ret &= bcm87800_set_fec_clear(ch);
						BER[C4->Interface][ch].fec_lock_flag = 0;
						BER[C4->Interface][ch].pre_fec_ber = 0;
						BER[C4->Interface][ch].post_fec_ber = 0;
						BER[C4->Interface][ch].tot_frame_rev_cnt = 0;
						BER[C4->Interface][ch].tot_frame_corr_cnt = 0;
						BER[C4->Interface][ch].tot_frame_uncorr_cnt = 0;
						BER[C4->Interface][ch].tot_symbols_corr_cnt = 0;
						for(int c = 0; c < 16; c++)
							BER[C4->Interface][ch].tot_frames_err_cnt[c] = 0;
					}
				}
				if(ret)
					SendData("1\r\n");
				else
					SendData("0\r\n");
			}
			//15. 读取通道误码数
			else if(getcommand_value_int(subcmd,":MEAS:DATA?",ivalue,&itype))
			{
				st = accum_time;
				memset(strdata,0,256);
				if (BER[C4->Interface][ch].EDEn_Flag||BER[C4->Interface][ch].EDEn_HistoryFlag)
				{
					bcm87800_Checker_Status(ch);
					BER[C4->Interface][ch].EDEn_HistoryFlag = BER[C4->Interface][ch].EDEn_Flag;	
				}
				sprintf(strdata,"%.0f,%.0f,%.0f,%.0f,%.0f,%.0f,%d\r\n",(double)BER[C4->Interface][ch].RealErrCount_msb,(double)BER[C4->Interface][ch].RealErrCount_lsb,(double)BER[C4->Interface][ch].RealBitCount,
				(double)BER[C4->Interface][ch].AccumErrCount_msb,(double)BER[C4->Interface][ch].AccumErrCount_lsb,(double)BER[C4->Interface][ch].AccumBitCount,BER[C4->Interface][ch].EDLockFlag);
				SendData(strdata);
				BER[C4->Interface][ch].RealErrCount_msb = 0;
				BER[C4->Interface][ch].RealErrCount_lsb = 0;
				BER[C4->Interface][ch].RealBitCount = 0;
				et = accum_time-st;
				bert_TimeCountStart[C4->Interface][ch] = accum_time;
			}
			else if(getcommand_value_int(subcmd,":MEAS:FECDATA?",ivalue,&itype))
			{
				st = accum_time;
				memset(strdata,0,256);
				if (BER[C4->Interface][ch].FECEn_Flag||BER[C4->Interface][ch].FECEn_HistoryFlag)
				{
					bcm87800_set_fec_status(ch);
					BER[C4->Interface][ch].FECEn_HistoryFlag = BER[C4->Interface][ch].FECEn_Flag;	
				}
				char str[50];
				memset(strdata,0,512);
				sprintf(str,"%0.3e,",BER[C4->Interface][ch].pre_fec_ber);
				strcat(strdata,str);
				sprintf(str,"%0.3e,",BER[C4->Interface][ch].post_fec_ber);
				strcat(strdata,str);
				sprintf(str,"%012llX,",BER[C4->Interface][ch].tot_frame_rev_cnt);
				strcat(strdata,str);
				sprintf(str,"%012llX,",BER[C4->Interface][ch].tot_frame_corr_cnt);
				strcat(strdata,str);
				sprintf(str,"%012llX,",BER[C4->Interface][ch].tot_frame_uncorr_cnt);
				strcat(strdata,str);
				sprintf(str,"%012llX,",BER[C4->Interface][ch].tot_symbols_corr_cnt);
				strcat(strdata,str);
				
				for(int c = 0; c < 16; c++)
				{
					sprintf(str,"%012llX,",BER[C4->Interface][ch].tot_frames_err_cnt[c]);
					strcat(strdata,str);
				}
				sprintf(str,"%d\r\n",BER[C4->Interface][ch].fec_lock_flag);
				strcat(strdata,str);
				
				//strdata[511]='\0';
				SendData(strdata);
				et = accum_time-st;
				bert_TimeCountStart[C4->Interface][ch] = accum_time;
			}
			else if(getcommand_value_int(subcmd,":ALL:AUTOPOLA",ivalue,&itype))
			{
				if(itype==1)
				{
					C4->RxAutoInvert = ivalue[0];
					for(ch=0;ch<8;ch++)
					{
						//in115025_enable_checker(ch);
					}
					SendData("1\r\n");
				}
				else if(itype==2)
				{
					sprintf(strdata,"%d\r\n",C4->RxAutoInvert);
					SendData(strdata);
				}
			}
			else
			{
				SendData("0\r\n");
			}
		}
		else
		{
			SendData("0\r\n");
		}
		memset(UserRxBufferFS,0,APP_RX_DATA_SIZE);
	}
}

/**
  * @brief  Gets the sector of a given address
  * @param  Address: Flash address
  * @retval The sector of a given address
  */
uint32_t GetSector(uint32_t Address)
{
  uint32_t sector = 0;
  
  if((Address < ADDR_FLASH_SECTOR_1) && (Address >= ADDR_FLASH_SECTOR_0))
  {
    sector = FLASH_SECTOR_0;  
  }
  else if((Address < ADDR_FLASH_SECTOR_2) && (Address >= ADDR_FLASH_SECTOR_1))
  {
    sector = FLASH_SECTOR_1;  
  }
  else if((Address < ADDR_FLASH_SECTOR_3) && (Address >= ADDR_FLASH_SECTOR_2))
  {
    sector = FLASH_SECTOR_2;  
  }
  else if((Address < ADDR_FLASH_SECTOR_4) && (Address >= ADDR_FLASH_SECTOR_3))
  {
    sector = FLASH_SECTOR_3;  
  }
  else if((Address < ADDR_FLASH_SECTOR_5) && (Address >= ADDR_FLASH_SECTOR_4))
  {
    sector = FLASH_SECTOR_4;  
  }
  else if((Address < ADDR_FLASH_SECTOR_6) && (Address >= ADDR_FLASH_SECTOR_5))
  {
    sector = FLASH_SECTOR_5;  
  }
  else if((Address < ADDR_FLASH_SECTOR_7) && (Address >= ADDR_FLASH_SECTOR_6))
  {
    sector = FLASH_SECTOR_6;  
  }
  else if((Address < ADDR_FLASH_SECTOR_8) && (Address >= ADDR_FLASH_SECTOR_7))
  {
    sector = FLASH_SECTOR_7;  
  }
  else if((Address < ADDR_FLASH_SECTOR_9) && (Address >= ADDR_FLASH_SECTOR_8))
  {
    sector = FLASH_SECTOR_8;  
  }
  else if((Address < ADDR_FLASH_SECTOR_10) && (Address >= ADDR_FLASH_SECTOR_9))
  {
    sector = FLASH_SECTOR_9;  
  }
  else if((Address < ADDR_FLASH_SECTOR_11) && (Address >= ADDR_FLASH_SECTOR_10))
  {
    sector = FLASH_SECTOR_10;  
  }
  else /*(Address < FLASH_END_ADDR) && (Address >= ADDR_FLASH_SECTOR_11))*/
  {
    sector = FLASH_SECTOR_11;  
  }
  return sector;
}


HAL_StatusTypeDef Flash_Erase(uint32_t addr, uint8_t count)
{
	uint32_t SECTORError = 0;
	FLASH_EraseInitTypeDef EraseInitStruct;
	
	EraseInitStruct.TypeErase     = TYPEERASE_SECTORS;
  EraseInitStruct.VoltageRange  = VOLTAGE_RANGE_3;
  EraseInitStruct.Sector        = GetSector(addr);
  EraseInitStruct.NbSectors     = 1;
	if (HAL_FLASHEx_Erase(&EraseInitStruct, &SECTORError) != HAL_OK) return HAL_ERROR;
	return HAL_OK;
}

HAL_StatusTypeDef Flash_Write(uint32_t addr, uint8_t *buffer, uint32_t length)
{
	uint32_t DATA_32,i;
	
	HAL_FLASH_Unlock();
	Flash_Erase(addr,1);	
	for(i = 0; i < length; i += 4)
	{
		DATA_32 = *((unsigned long*)&buffer[i]);
	  if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, addr+i, DATA_32) != HAL_OK) return HAL_ERROR;
	}
	HAL_FLASH_Lock();
	return HAL_OK;
}

void Flash_Read(uint32_t addr, uint8_t *buffer, uint32_t length)
{
	uint16_t i=0;
	
	for(i=0;i<length;i++)
	{
		buffer[i] = *(__IO uint8_t*)addr;
		addr+=1;
	}
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @param  file: The file name as string.
  * @param  line: The line in file as a number.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  while(1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{ 
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     tex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
