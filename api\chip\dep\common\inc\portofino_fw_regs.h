/**
 *
 * @file     portofino_fw_regs.h
 * <AUTHOR> Team
 * @date     08-18-2020
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2020 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    This file contains definitions for public registers.
 *
 *           NOTE: DO NOT EDIT THIS FILE MANUALLY. This is a generated file.
 *           Refer to details given in portofino_fw_regs.h
 *
 */

#ifndef PORTOFINO_FW_REGS_H
#define PORTOFINO_FW_REGS_H

/****************************************************************************
 * CHIP_TOP_CHIP_REFCLK_CONFIG_REG 
 ***************************************************************************/
#define CHIP_TOP_CHIP_REFCLK_CONFIG_REG_REF_CLK_FREQ_SELECT_MASK 0x0000001f /*!< REF_CLK_FREQ_SELECT */
#define CHIP_TOP_CHIP_REFCLK_CONFIG_REG_REF_CLK_FREQ_SELECT_SHIFT 0
typedef union chip_top_chip_temp_reading_reg_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t temp_reading_valid : 1; /*!<temp_reading_valid */
        uint16_t temp_reading : 15; /*!<temp_reading */
#else /* LITTLE ENDIAN */
        uint16_t temp_reading : 15; /*!<temp_reading */
        uint16_t temp_reading_valid : 1; /*!<temp_reading_valid */
#endif
    } fields;
} chip_top_chip_temp_reading_reg_t;

typedef union chip_top_chip_volt_reading_reg_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t voltage_reading_valid : 1; /*!<voltage_reading_valid */
        uint16_t volatge_reading : 15; /*!<volatge_reading */
#else /* LITTLE ENDIAN */
        uint16_t volatge_reading : 15; /*!<volatge_reading */
        uint16_t voltage_reading_valid : 1; /*!<voltage_reading_valid */
#endif
    } fields;
} chip_top_chip_volt_reading_reg_t;


typedef union chip_top_chip_rvtmon_avs_reg_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t reserved : 4; /*!<reserved*/
        uint16_t toggle_reset_counter: 4;
        uint16_t try_counter: 4;
        uint16_t wait_for_avs_config : 1; /*!<wait_for_avs_config*/
        uint16_t avs_done : 1; /*!<avs_done*/
        uint16_t rvtmon_init_done : 1; /*!<rvtmond_init_done*/
        uint16_t rescal_done : 1; /*!<rescal_done */
#else /* LITTLE ENDIAN */
        uint16_t rescal_done : 1; /*!<rescal_done */
        uint16_t rvtmon_init_done : 1; /*!<rvtmond_init_done*/
        uint16_t avs_done : 1; /*!<avs_done*/
        uint16_t wait_for_avs_config : 1; /*!<wait_for_avs_config*/
        uint16_t try_counter: 4;
        uint16_t toggle_reset_counter: 4;
        uint16_t reserved : 4; /*!<reserved*/
#endif
    } fields;
} chip_top_chip_rvtmon_avs_reg_t;

typedef union chip_top_avs_mode_config_reg_t_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t avs_status : 3; /*< AVS status */
        uint16_t avs_dc_margin : 5; /*!< AVS DC margin */
        uint16_t type_of_regulator : 3; /*!< different type of regulator */
        uint16_t num_of_phy : 2; /*!<AVS number of pky share AVS */
        uint16_t disable_type: 1; /*!< either firmwware disable or external disable */
        uint16_t avs_regulator_control_mode : 1; /*!<avs regulator control mode */
        uint16_t avs_enable : 1; /*!<enabel avs or not */
#else /* LITTLE ENDIAN */
        uint16_t avs_enable : 1; /*!<enabel avs or not */
        uint16_t avs_regulator_control_mode : 1; /*!<avs regulator control mode */
        uint16_t disable_type: 1; /*!< either firmwware disable or external disable */
        uint16_t num_of_phy : 2; /*!<AVS number of pky share AVS */
        uint16_t type_of_regulator : 3; /*!< different type of regulator */
        uint16_t avs_dc_margin : 5; /*!< AVS DC margin */
        uint16_t avs_status : 3; /*< AVS status */
#endif
    } fields;
} chip_top_avs_mode_config_reg_t;

typedef union chip_top_fixed_voltage_config_reg_t_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t reserved : 6; /*< reserved*/
        uint16_t digital_analog : 1; /*< digital or analogy */
        uint16_t reason : 2; /*< Reason */
        uint16_t set_status : 3; /*< AVS status */      
        uint16_t type_of_regulator : 3; /*!< different type of regulator */      
        uint16_t fixed_enable : 1; /*!<enabel avs or not */
#else /* LITTLE ENDIAN */
        uint16_t fixed_enable : 1; /*!<enabel avs or not */
        uint16_t type_of_regulator : 3; /*!< different type of regulator */        
        uint16_t set_status : 3; /*< AVS status */
        uint16_t reason : 2; /*<Reason */
        uint16_t digital_analog : 1; /*< digital or analogy */
        uint16_t reserved : 6; /*< reserved*/
#endif
    } fields;
} chip_top_fixed_voltage_config_reg_t;
typedef union chip_top_command_avsmgt_reg_t_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t reserved : 13; /*< reserved*/       
        uint16_t suspend_resume : 2; /*< suspend =1, resume =0 */
        uint16_t command_request : 1;      
#else /* LITTLE ENDIAN */
        uint16_t command_request : 1;       
        uint16_t suspend_resume : 2; /*< suspend =1, resume =0 */
        uint16_t reserved : 13; /*< reserved*/
#endif
    } fields;
} chip_top_command_avsmgt_reg_t;

typedef union chip_top_status_avsmgt_reg_t_u {
    uint16_t words;
    struct {
#ifdef CAPI_IS_BIG_ENDIAN
        uint16_t reserved : 13; /*< reserved*/       
        uint16_t suspend_resume : 2; /*< suspend =1, resume =0 */
        uint16_t status_ack : 1;       
#else /* LITTLE ENDIAN */
        uint16_t status_ack : 1;       
        uint16_t suspend_resume : 2; /*< suspend =1, resume =0 */
        uint16_t reserved : 13; /*< reserved*/
#endif
    } fields;
} chip_top_status_avsmgt_reg_t;


#endif /* PORTOFINO_FW_REGS_H */
