/**
 *
 * @file     fw_gp_reg_map.h
 * <AUTHOR> Team
 * @date     08-18-2020
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2020 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    This file contains definitions for public registers.
 *
 *           NOTE: DO NOT EDIT THIS FILE MANUALLY. This is a generated file.
 *           Refer to details given in fw_gp_reg_map.h
 *
 */

#ifndef FW_GP_REG_MAP_H
#define FW_GP_REG_MAP_H

#define USE_SRAM_FOR_GP
#define OCTAL_TOP_REGS                                 APB2_SLV3_REGS
#define CHIP_TOP_CHIP_FIRMWARE_VERSION_REG            QUAD_CORE_GPCTRL_GPCTRL_1_RDB
#define CHIP_TOP_CHIP_FIRMWARE_VERSION_MINOR_REG_ADDR QUAD_CORE_GPCTRL_GPCTRL_2_RDB
#define CHIP_CAPI_MAJOR_VERSION_REG                   QUAD_CORE_GPCTRL_GPCTRL_510_RDB
#define CHIP_CAPI_MINOR_VERSION_REG                   QUAD_CORE_GPCTRL_GPCTRL_511_RDB
#define CHIP_STANDBY_FW_MAJOR_VERSION_REG             QUAD_CORE_GPCTRL_GPCTRL_508_RDB
#define CHIP_STANDBY_FW_MINOR_VERSION_REG             QUAD_CORE_GPCTRL_GPCTRL_509_RDB
#define CHIP_TOP_CHIP_INTERNAL_CONTROL                QUAD_CORE_GPCTRL_GPCTRL_6_RDB          //Not used
#define CHIP_TOP_CHIP_FIRMWARE_CRC_RET_REG            QUAD_CORE_GPCTRL_GPCTRL_7_RDB

#define FW_EVENTS_ADDR_LO_REG                         QUAD_CORE_GPCTRL_GPCTRL_8_RDB  // LOG
#define FW_EVENTS_ADDR_HI_REG                         QUAD_CORE_GPCTRL_GPCTRL_9_RDB  // LOG
#define CHIP_TOP_CHIP_TEMP_READING_REG                QUAD_CORE_GPCTRL_GPCTRL_10_RDB
#define CHIP_TOP_CHIP_VOLT_READING_REG                QUAD_CORE_GPCTRL_GPCTRL_11_RDB
#define CHIP_TOP_CHIP_FIXED_VOLTAGE_CONFIG_REG        QUAD_CORE_GPCTRL_GPCTRL_31_RDB
#define CHIP_TOP_CHIP_FIXED_VOLTAGE_INPUT_REG         QUAD_CORE_GPCTRL_GPCTRL_32_RDB
#define CHIP_TOP_CHIP_COMMAND_AVSMGT_REG              QUAD_CORE_GPCTRL_GPCTRL_34_RDB
#define CHIP_TOP_CHIP_STATUS_AVSMGT_REG               QUAD_CORE_GPCTRL_GPCTRL_35_RDB
#define CHIP_TOP_CHIP_FIRMWARE_STATUS_P0_REG          QUAD_CORE_GPCTRL_GPCTRL_36_RDB
#define CHIP_TOP_CHIP_FIRMWARE_STATUS_P1_REG          QUAD_CORE_GPCTRL_GPCTRL_37_RDB
#define INTF_CAPI2FW_CW_CMD_REQUEST_GPREG             QUAD_CORE_GPCTRL_GPCTRL_40_RDB
#define INTF_CAPI2FW_CW_CMD_LANE_MASK_GPREG           QUAD_CORE_GPCTRL_GPCTRL_41_RDB
#define INTF_CAPI2FW_CW_CMD_RESPONSE_GPREG            QUAD_CORE_GPCTRL_GPCTRL_42_RDB
#define INTF_CAPI2FW_CW_RSP_LANE_MASK_GPREG           QUAD_CORE_GPCTRL_GPCTRL_43_RDB

#define INTF_CAPI2FW_HOST_CMD_REQUEST_GPREG           QUAD_CORE_GPCTRL_GPCTRL_44_RDB
#define INTF_CAPI2FW_HOST_CMD_LANE_MASK_GPREG         QUAD_CORE_GPCTRL_GPCTRL_45_RDB
#define INTF_CAPI2FW_HOST_CMD_RESPONSE_GPREG          QUAD_CORE_GPCTRL_GPCTRL_46_RDB
#define INTF_CAPI2FW_HOST_RSP_LANE_MASK_GPREG         QUAD_CORE_GPCTRL_GPCTRL_47_RDB

#define INTF_CAPI2FW_MEDIA_CMD_REQUEST_GPREG          QUAD_CORE_GPCTRL_GPCTRL_48_RDB
#define INTF_CAPI2FW_MEDIA_CMD_LANE_MASK_GPREG        QUAD_CORE_GPCTRL_GPCTRL_49_RDB
#define INTF_CAPI2FW_MEDIA_CMD_RESPONSE_GPREG         QUAD_CORE_GPCTRL_GPCTRL_50_RDB
#define INTF_CAPI2FW_MEDIA_RSP_LANE_MASK_GPREG        QUAD_CORE_GPCTRL_GPCTRL_51_RDB
#define DSP_BRCM_SNR_LOG_LO_REG                       QUAD_CORE_GPCTRL_GPCTRL_58_RDB
#define DSP_BRCM_SNR_LOG_HI_REG                       QUAD_CORE_GPCTRL_GPCTRL_59_RDB
#define INTF_LWTOCW_COMMANDSTATUS_LANE0               QUAD_CORE_GPCTRL_GPCTRL_324_RDB
#define INTF_BHTOCW_COMMANDSTATUS_LANE0               QUAD_CORE_GPCTRL_GPCTRL_372_RDB
#define MEDIA_LANE_CDR_LOCK_STATUS_GPREG              QUAD_CORE_GPCTRL_GPCTRL_92_RDB
#define HOST_LANE_CDR_LOCK_STATUS_GPREG               QUAD_CORE_GPCTRL_GPCTRL_93_RDB
#define MEDIA_LANE_LOL_LOS_STATUS_GPREG               QUAD_CORE_GPCTRL_GPCTRL_94_RDB
#define HOST_LANE_LOL_LOS_STATUS_GPREG                QUAD_CORE_GPCTRL_GPCTRL_95_RDB
#define MEDIA_LANE_LOL_STATUS_ACK_GPREG               QUAD_CORE_GPCTRL_GPCTRL_98_RDB
#define HOST_LANE_LOL_STATUS_ACK_GPREG                QUAD_CORE_GPCTRL_GPCTRL_99_RDB

#define MEDIA_LANE_TX_SQUELCH_STATUS_GPREG            QUAD_CORE_GPCTRL_GPCTRL_100_RDB
#define HOST_LANE_TX_SQUELCH_STATUS_GPREG             QUAD_CORE_GPCTRL_GPCTRL_101_RDB

#define HOST_LANE_RX_OUTPUT_STATUS_GPREG              QUAD_CORE_GPCTRL_GPCTRL_102_RDB
#define HOST_LANE_RX_OUTPUT_LATCH_STATUS_GPREG        QUAD_CORE_GPCTRL_GPCTRL_103_RDB
#define FW_INTERNAL_CONFIG_REG_1                      QUAD_CORE_GPCTRL_GPCTRL_164_RDB
#define FW_INTERNAL_CONFIG_REG_2                      QUAD_CORE_GPCTRL_GPCTRL_165_RDB
#define OCW_CONFIG_FLT_PROC_CFG_REG                   QUAD_CORE_GPCTRL_GPCTRL_179_RDB
#define OCW_CONFIG_FSM_RESET_CFG_REG                  QUAD_CORE_GPCTRL_GPCTRL_180_RDB
#define OCW_CHIP_POWER_DOWN_STATUS_REG                QUAD_CORE_GPCTRL_GPCTRL_182_RDB
#define OCW_CHIP_POWER_DOWN_FW_CMD_REG                QUAD_CORE_GPCTRL_GPCTRL_184_RDB
#define OCW_CHIP_LPM_FW_REG                           QUAD_CORE_GPCTRL_GPCTRL_185_RDB
#define OCW_EGRESS_FSM_SUSPEND_CFG                    QUAD_CORE_GPCTRL_GPCTRL_202_RDB
#define OCW_INGRESS_FSM_SUSPEND_CFG                   QUAD_CORE_GPCTRL_GPCTRL_203_RDB
#define OCW_EGRESS47_FSM_SUSPEND_CFG                   QUAD_CORE_GPCTRL_GPCTRL_213_RDB
#define OCW_INGRESS47_FSM_SUSPEND_CFG                  QUAD_CORE_GPCTRL_GPCTRL_214_RDB

#define OCW_FEC_MON_STATUS                             QUAD_CORE_GPCTRL_GPCTRL_216_RDB
#define CAPI_FEATURE_INFORMATION                       QUAD_CORE_GPCTRL_GPCTRL_219_RDB
#define OCW_CONFIG_FEC_PGEN_EN_REG                     QUAD_CORE_GPCTRL_GPCTRL_222_RDB
#define OCW_CONFIG_FEC_PMON_EN_REG                     QUAD_CORE_GPCTRL_GPCTRL_223_RDB
#define DSP_NEXT_LANE_CONFIG_REG_OFFSET               32 //(4 * MEDIA_LANE_NMU_MAX)  /**< DSP Next Lane Config Reg offset)    */
#define SRAM_GP_REGION_START                          0x52000 /* start of sram gp space, reserved 3KB (768 equivalent GPs)
                                                                 first half for host, second half for media
                                                              */
#define SRAM_GP_REGION_END                            0x52FFF /* end of sram gp space, star and end are absolute address */

#define COMMON_LW_LANE_CONFIG1_REG                    0

#define COMMON_LW_LANE_CONFIG2_REG     (COMMON_LW_LANE_CONFIG1_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP1 OFFSET: 0x20*/
#define COMMON_LW_LANE_CONFIG3_REG     (COMMON_LW_LANE_CONFIG2_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP2 OFFSET: 0x40*/
#define COMMON_LW_LANE_CONFIG4_REG     (COMMON_LW_LANE_CONFIG3_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP3 OFFSET: 0x60*/
#define COMMON_LW_LANE_CONFIG5_REG     (COMMON_LW_LANE_CONFIG4_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP4 OFFSET: 0x80*/
#define COMMON_LW_LANE_CONFIG6_REG     (COMMON_LW_LANE_CONFIG5_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP5 OFFSET: 0xA0*/
#define COMMON_LW_LANE_CONFIG7_REG     (COMMON_LW_LANE_CONFIG6_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP6 OFFSET: 0xC0*/
#define COMMON_LW_LANE_CONFIG8_REG     (COMMON_LW_LANE_CONFIG7_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP7 OFFSET: 0xE0*/
#define COMMON_LW_LANE_CONFIG9_REG     (COMMON_LW_LANE_CONFIG8_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP8 OFFSET: 0x100*/
#define COMMON_LW_LANE_CONFIGA_REG     (COMMON_LW_LANE_CONFIG9_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP9  OFFSET: 0x120*/
#define COMMON_LW_LANE_CONFIGB_REG     (COMMON_LW_LANE_CONFIGA_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP10 OFFSET: 0x140*/
#define COMMON_LW_LANE_CONFIGC_REG     (COMMON_LW_LANE_CONFIGB_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP11 OFFSET: 0x160*/
#define COMMON_LW_LANE_CONFIGD_REG     (COMMON_LW_LANE_CONFIGC_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP12 OFFSET: 0x180*/
#define COMMON_LW_LANE_CONFIGE_REG     (COMMON_LW_LANE_CONFIGD_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP13 OFFSET: 0x1A0*/
#define COMMON_LW_LANE_CONFIGF_REG     (COMMON_LW_LANE_CONFIGE_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP14 OFFSET: 0x1C0*/
                                                                                                            
#define COMMON_LW_LANE_CONFIG10_REG    (COMMON_LW_LANE_CONFIGF_REG  + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP15 OFFSET: 0x1E0*/
#define COMMON_LW_LANE_CONFIG11_REG    (COMMON_LW_LANE_CONFIG10_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP16 OFFSET: 0x200*/
#define COMMON_LW_LANE_CONFIG12_REG    (COMMON_LW_LANE_CONFIG11_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP17 OFFSET: 0x220*/
#define COMMON_LW_LANE_CONFIG13_REG    (COMMON_LW_LANE_CONFIG12_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP18 OFFSET: 0x240*/
#define COMMON_LW_LANE_CONFIG14_REG    (COMMON_LW_LANE_CONFIG13_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP19 OFFSET: 0x260*/
#define COMMON_LW_LANE_CONFIG15_REG    (COMMON_LW_LANE_CONFIG14_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP20 OFFSET: 0x280*/
#define COMMON_LW_LANE_CONFIG16_REG    (COMMON_LW_LANE_CONFIG15_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP21 OFFSET: 0x2A0*/
#define COMMON_LW_LANE_CONFIG17_REG    (COMMON_LW_LANE_CONFIG16_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP22 OFFSET: 0x2C0*/
#define COMMON_LW_LANE_CONFIG18_REG    (COMMON_LW_LANE_CONFIG17_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP23 OFFSET: 0x2E0*/
#define COMMON_LW_LANE_CONFIG19_REG    (COMMON_LW_LANE_CONFIG18_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP24 OFFSET: 0x300*/
#define COMMON_LW_LANE_CONFIG1A_REG    (COMMON_LW_LANE_CONFIG19_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP25 OFFSET: 0x320*/
#define COMMON_LW_LANE_CONFIG1B_REG    (COMMON_LW_LANE_CONFIG1A_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP26 OFFSET: 0x340*/
#define COMMON_LW_LANE_CONFIG1C_REG    (COMMON_LW_LANE_CONFIG1B_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP27 OFFSET: 0x360*/
#define COMMON_LW_LANE_CONFIG1D_REG    (COMMON_LW_LANE_CONFIG1C_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP28 OFFSET: 0x380*/
#define COMMON_LW_LANE_CONFIG1E_REG    (COMMON_LW_LANE_CONFIG1D_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP29 OFFSET: 0x3A0*/
#define COMMON_LW_LANE_CONFIG1F_REG    (COMMON_LW_LANE_CONFIG1E_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP30 OFFSET: 0x3C0*/
#define COMMON_LW_LANE_CONFIG20_REG    (COMMON_LW_LANE_CONFIG1F_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP31 OFFSET: 0x3E0*/
#define COMMON_LW_LANE_CONFIG21_REG    (COMMON_LW_LANE_CONFIG20_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP32 OFFSET: 0x400*/
#define COMMON_LW_LANE_CONFIG22_REG    (COMMON_LW_LANE_CONFIG21_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP33 OFFSET: 0x420*/
#define COMMON_LW_LANE_CONFIG23_REG    (COMMON_LW_LANE_CONFIG22_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP34 OFFSET: 0x440*/
#define COMMON_LW_LANE_CONFIG24_REG    (COMMON_LW_LANE_CONFIG23_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP35 OFFSET: 0x460*/
#define COMMON_LW_LANE_CONFIG25_REG    (COMMON_LW_LANE_CONFIG24_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP36 OFFSET: 0x480*/
#define COMMON_LW_LANE_CONFIG26_REG    (COMMON_LW_LANE_CONFIG25_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP37 OFFSET: 0x4A0*/
#define COMMON_LW_LANE_CONFIG27_REG    (COMMON_LW_LANE_CONFIG26_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP38 OFFSET: 0x4C0*/
#define COMMON_LW_LANE_CONFIG28_REG    (COMMON_LW_LANE_CONFIG27_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP39 OFFSET: 0x4E0*/
#define COMMON_LW_LANE_CONFIG29_REG    (COMMON_LW_LANE_CONFIG28_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP40 OFFSET: 0x500*/
#define COMMON_LW_LANE_CONFIG2A_REG    (COMMON_LW_LANE_CONFIG29_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP41 OFFSET: 0x520*/
#define COMMON_LW_LANE_CONFIG2B_REG    (COMMON_LW_LANE_CONFIG2A_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP42 OFFSET: 0x540*/
#define COMMON_LW_LANE_CONFIG2C_REG    (COMMON_LW_LANE_CONFIG2B_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP43 OFFSET: 0x560*/
#define COMMON_LW_LANE_CONFIG2D_REG    (COMMON_LW_LANE_CONFIG2C_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP44 OFFSET: 0x580*/
#define COMMON_LW_LANE_CONFIG2E_REG    (COMMON_LW_LANE_CONFIG2D_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP45 OFFSET: 0x5A0*/
#define COMMON_LW_LANE_CONFIG2F_REG    (COMMON_LW_LANE_CONFIG2E_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP46 OFFSET: 0x5C0*/
#define COMMON_LW_LANE_CONFIG30_REG    (COMMON_LW_LANE_CONFIG2F_REG + DSP_NEXT_LANE_CONFIG_REG_OFFSET) /**< GP47 OFFSET: 0x5E0*/

#define CHIP_TOP_CHIP_REFCLK_CONFIG_REG    QUAD_CORE_CONFIG_CFGVAL_0_RDB
#define CHIP_TOP_CHIP_AVS_MODE_CONFIG_REG  QUAD_CORE_CONFIG_CFGVAL_2_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_0                 QUAD_CORE_CONFIG_CFGVAL_8_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_1                 QUAD_CORE_CONFIG_CFGVAL_10_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_2                 QUAD_CORE_CONFIG_CFGVAL_12_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_3                 QUAD_CORE_CONFIG_CFGVAL_14_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_4                 QUAD_CORE_CONFIG_CFGVAL_16_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_5                 QUAD_CORE_CONFIG_CFGVAL_18_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_6                 QUAD_CORE_CONFIG_CFGVAL_20_RDB
#define OCTAL_PORT_MODE_CONFIG_1_REG_7                 QUAD_CORE_CONFIG_CFGVAL_22_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_0                 QUAD_CORE_CONFIG_CFGVAL_9_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_1                 QUAD_CORE_CONFIG_CFGVAL_11_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_2                 QUAD_CORE_CONFIG_CFGVAL_13_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_3                 QUAD_CORE_CONFIG_CFGVAL_15_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_4                 QUAD_CORE_CONFIG_CFGVAL_17_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_5                 QUAD_CORE_CONFIG_CFGVAL_19_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_6                 QUAD_CORE_CONFIG_CFGVAL_21_RDB
#define OCTAL_PORT_MODE_CONFIG_2_REG_7                 QUAD_CORE_CONFIG_CFGVAL_23_RDB
#define OCTAL_PORT_MODE_CFG_LOG                        QUAD_CORE_CONFIG_CFGVAL_32_RDB 
#define OCW_BH1_LANE_CONFIG_ST                         QUAD_CORE_CONFIG_CFGVAL_36_RDB
#define OCW_LW_LANE_CONFIG_ST                          QUAD_CORE_CONFIG_CFGVAL_37_RDB
#define UC_READY_REG                                   QUAD_CORE_CONFIG_CFGVAL_40_RDB
#define CHIP_MODE_FAST_CONFIG_COMMAND_REG              QUAD_CORE_CONFIG_CFGVAL_43_RDB
#define OCTAL_PORT_MISSION_INFO_REG                    QUAD_CORE_CONFIG_CFGVAL_63_RDB
#define HOST_DSP_CONFIG1_REG                                  QUAD_CORE_CONFIG_CFGVAL_80_RDB
#define MEDIA_DSP_CONFIG1_REG                                 QUAD_CORE_CONFIG_CFGVAL_81_RDB
#define HOST_DSP_CONFIG2_REG                                  QUAD_CORE_CONFIG_CFGVAL_100_RDB
#define MEDIA_DSP_CONFIG2_REG                                 QUAD_CORE_CONFIG_CFGVAL_101_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_0                 QUAD_CORE_CONFIG_CFGVAL_84_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_1                 QUAD_CORE_CONFIG_CFGVAL_85_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_2                 QUAD_CORE_CONFIG_CFGVAL_86_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_3                 QUAD_CORE_CONFIG_CFGVAL_87_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_4                 QUAD_CORE_CONFIG_CFGVAL_88_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_5                 QUAD_CORE_CONFIG_CFGVAL_89_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_6                 QUAD_CORE_CONFIG_CFGVAL_90_RDB
#define OCTAL_PORT_MODE_CONFIG_2_SHADOW_REG_7                 QUAD_CORE_CONFIG_CFGVAL_91_RDB

#define FW_MEM_MAP_ADDR_LO_REG                                QUAD_CORE_CONFIG_CFGVAL_92_RDB
#define FW_MEM_MAP_ADDR_HI_REG                                QUAD_CORE_CONFIG_CFGVAL_93_RDB
#define COMMON_FW_EVENT_LOGGING_FILTER_CFG_REG                QUAD_CORE_CONFIG_CFGVAL_123_RDB
#define COMMON_FW_EVENT_LOGGING_FILTER_REG                    QUAD_CORE_CONFIG_CFGVAL_127_RDB
#define COMMON_FW_DSP_PLL_FRC_FRAC_VAL                        QUAD_CORE_CONFIG_CFGVAL_94_RDB
#define COMMON_FW_DSP_PLL_MEDIA_FRACN_CFG                     QUAD_CORE_CONFIG_CFGVAL_97_RDB
#define COMMON_FW_DSP_PLL_MEDIA_FRACN_REQ                     QUAD_CORE_CONFIG_CFGVAL_98_RDB

#endif /* FW_GP_REG_MAP_H */
