#ifndef HW_CHIP_COMMON_DEF_H
#define HW_CHIP_COMMON_DEF_H

/* number of clock cycle in 1 millisecond */
#define CLK_CYCLE_1MS 156250
#define CLK_CYCLE_1US 156

/**
 * Portofino Chip Identification
 */
#define PORTOFINO_87800_CHIP_ID        0x87800                     /**< CHIP ID 0x87800       */
#define PORTOFINO_87802_CHIP_ID        0x87802                     /**< CHIP ID 0x87802       */
#define PORTOFINO_87803_CHIP_ID        0x87803                     /**< CHIP ID 0x87803       */
#define PORTOFINO_87804_CHIP_ID        0x87804                     /**< CHIP ID 0x87804       */
#define PORTOFINO_87840_CHIP_ID        0x87840                     /**< CHIP ID 0x87840       */
#define PORTOFINO_87842_CHIP_ID        0x87842                     /**< CHIP ID 0x87842       */
#define PORTOFINO_87843_CHIP_ID        0x87843                     /**< CHIP ID 0x87843       */
#define PORTOFINO_87809_CHIP_ID        0x87809                     /**< CHIP ID 0x87809       */
#define PORTOFINO_87888_CHIP_ID        0x87888                     /**< CHIP ID 0x87888       */ /*bottom mount high swing chip, temporary assign 87888 to keep this code*/
#define PORTOFINO_87809_CHIP_ID        0x87809                     /**< CHIP ID 0x87809       */ /*Interstellar*/

#define PORTOFINO_REV_ID_A0            0xA0                        /**< REV ID A0             */
#define PORTOFINO_REV_ID_A1            0xA1                        /**< REV ID A0             */

#define CHIP_HOST_LANE_MAX         8            /**< Host Maximum Number of Lanes  */
#define CHIP_MEDIA_LANE_MAX        8            /**< Media Maximum Number of Lanes */

#endif
