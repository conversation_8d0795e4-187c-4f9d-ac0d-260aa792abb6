/**
 * @file     capi_custome_def.h
 * <AUTHOR> @date     02-26-2021
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2021 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    brief description of the block source file, sequences/flow/etc.
 *
 * @section  description
 *
 */
#ifndef CAPI_CUSTOME_DEF_H
#define CAPI_CUSTOME_DEF_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * The structure of TxFIR 4TAP Table Info
 */
typedef struct capi_txfir_4tap_table_info_s {
    int8_t      four_taps[256];                       /**< TxFIR 4 tap[0,....,255]                                         */
} capi_txfir_4tap_table_info_t;                                  /**< TxFIR Table Info type*/

/**
 * The structure of TxFIR 7TAP Table Info
 */
typedef struct capi_txfir_7tap_table_info_s {
    float      tap_level[7][4];          /**< TxFIR 7 tap[coefficient(0~6)][level(0~3)]                                         */
                                         /*for each entry, tap_level[tap_idx][lvl_idx] = tap_coef_7tap[tap_idx][lvl_idx] * (lvl_symbol[lvl_idx] + lvl_shift_7tap[tap_idx][lvl_idx]);*/
} capi_txfir_7tap_table_info_t;                                  /**< TxFIR Table Info type*/

/**
 * The structure of custome command information 
 */
typedef struct capi_custom_command_info_s {
    phy_command_id_t command_id;                                   /**< Command Identifier                    */

    union {
        capi_txfir_4tap_table_info_t     txfir_4tap_table_info;              /**< Costome: TxFIR 4TAP Information Table      */
        capi_txfir_7tap_table_info_t     txfir_7tap_table_info;              /**< Costome: TxFIR 7TAP Information Table      */
    } type;                                                        /**< Payload/data type of the command Id   */

} capi_custom_command_info_t;                                     /**< Custome Command Information type      */

#ifdef __cplusplus
}
#endif

#endif /* CAPI_CUSTOME_DEF_H */

