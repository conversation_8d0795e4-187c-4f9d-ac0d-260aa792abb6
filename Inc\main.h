/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * This notice applies to any and all portions of this file
  * that are not between comment pairs USER CODE BEGIN and
  * USER CODE END. Other portions of this file, whether 
  * inserted by the user or by software development tools
  * are owned by their respective copyright owners.
  *
  * Copyright (c) 2019 STMicroelectronics International N.V. 
  * All rights reserved.
  *
  * Redistribution and use in source and binary forms, with or without 
  * modification, are permitted, provided that the following conditions are met:
  *
  * 1. Redistribution of source code must retain the above copyright notice, 
  *    this list of conditions and the following disclaimer.
  * 2. Redistributions in binary form must reproduce the above copyright notice,
  *    this list of conditions and the following disclaimer in the documentation
  *    and/or other materials provided with the distribution.
  * 3. Neither the name of STMicroelectronics nor the names of other 
  *    contributors to this software may be used to endorse or promote products 
  *    derived from this software without specific written permission.
  * 4. This software, including modifications and/or derivative works of this 
  *    software, must execute solely and exclusively on microcontroller or
  *    microprocessor devices manufactured by or for STMicroelectronics.
  * 5. Redistribution and use of this software other than as permitted under 
  *    this license is void and will automatically terminate your rights under 
  *    this license. 
  *
  * THIS SOFTWARE IS PROVIDED BY STMICROELECTRONICS AND CONTRIBUTORS "AS IS" 
  * AND ANY EXPRESS, IMPLIED OR STATUTORY WARRANTIES, INCLUDING, BUT NOT 
  * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
  * PARTICULAR PURPOSE AND NON-INFRINGEMENT OF THIRD PARTY INTELLECTUAL PROPERTY
  * RIGHTS ARE DISCLAIMED TO THE FULLEST EXTENT PERMITTED BY LAW. IN NO EVENT 
  * SHALL STMICROELECTRONICS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
  * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, 
  * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF 
  * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING 
  * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H__
#define __MAIN_H__

/* Includes ------------------------------------------------------------------*/

/* USER CODE BEGIN Includes */
#include "stm32f2xx.h"
#include "stm32f2xx_hal.h"
/* USER CODE END Includes */

/* Private define ------------------------------------------------------------*/
#define MDC_Pin GPIO_PIN_3
#define MDC_GPIO_Port GPIOB
#define MDIO_Pin GPIO_PIN_4
#define MDIO_GPIO_Port GPIOB
#define MDIOIN_Pin GPIO_PIN_5
#define MDIOIN_GPIO_Port GPIOB

#define SDASI_Pin GPIO_PIN_7 // SDA
#define SDASI_GPIO_Port GPIOB
#define SCLSI_Pin GPIO_PIN_6  // SCL
#define SCLSI_GPIO_Port GPIOB

#define SDA_Pin GPIO_PIN_1
#define SDA_GPIO_Port GPIOB
#define SCL_Pin GPIO_PIN_2
#define SCL_GPIO_Port GPIOB

#define RESET_DSP_Pin GPIO_PIN_5
#define RESET_DSP_GPIO_Port GPIOC
#define LINK_Pin GPIO_PIN_1
#define LINK_GPIO_Port GPIOC

#define LPMode_Pin GPIO_PIN_15  // Low Power Mode
#define LPMode_GPIO_Port GPIOB
#define ResetL_Pin GPIO_PIN_12 // Reset
#define ResetL_GPIO_Port GPIOB
#define IntL_Pin GPIO_PIN_13 // Int
#define IntL_GPIO_Port GPIOB
#define ModPrsL_Pin GPIO_PIN_14 // ModPrs
#define ModPrsL_GPIO_Port GPIOB
#define ModSEL_Pin GPIO_PIN_11
#define ModSEL_GPIO_Port GPIOB
#define PM_Pin GPIO_PIN_3
#define PM_GPIO_Port GPIOC

#define VSET_Pin GPIO_PIN_4
#define VSET_GPIO_Port GPIOA
#define VCCADC_Pin GPIO_PIN_1
#define VCCADC_GPIO_Port GPIOA

//SPI
#define SPICS_Pin GPIO_PIN_4
#define SPICS_GPIO_Port GPIOC
#define SPICLK_Pin GPIO_PIN_5
#define SPICLK_GPIO_Port GPIOA
#define SPIMISO_Pin GPIO_PIN_6
#define SPIMISO_GPIO_Port GPIOA
#define SPIMOSI_Pin GPIO_PIN_7
#define SPIMOSI_GPIO_Port GPIOA
#define SPIRST_Pin GPIO_PIN_0
#define SPIRST_GPIO_Port GPIOB
#define SPIINT_Pin GPIO_PIN_15
#define SPIINT_GPIO_Port GPIOA

#define RX_Pin GPIO_PIN_11
#define RX_GPIO_Port GPIOC
#define TX_Pin GPIO_PIN_10
#define TX_GPIO_Port GPIOC

/* Base address of the Flash sectors Bank 1 */
#define ADDR_FLASH_SECTOR_0     ((uint32_t)0x08000000) /* Base @ of Sector 0, 16 Kbytes */
#define ADDR_FLASH_SECTOR_1     ((uint32_t)0x08004000) /* Base @ of Sector 1, 16 Kbytes */
#define ADDR_FLASH_SECTOR_2     ((uint32_t)0x08008000) /* Base @ of Sector 2, 16 Kbytes */
#define ADDR_FLASH_SECTOR_3     ((uint32_t)0x0800C000) /* Base @ of Sector 3, 16 Kbytes */
#define ADDR_FLASH_SECTOR_4     ((uint32_t)0x08010000) /* Base @ of Sector 4, 64 Kbytes */
#define ADDR_FLASH_SECTOR_5     ((uint32_t)0x08020000) /* Base @ of Sector 5, 128 Kbytes */
#define ADDR_FLASH_SECTOR_6     ((uint32_t)0x08040000) /* Base @ of Sector 6, 128 Kbytes */
#define ADDR_FLASH_SECTOR_7     ((uint32_t)0x08060000) /* Base @ of Sector 7, 128 Kbytes */
#define ADDR_FLASH_SECTOR_8     ((uint32_t)0x08080000) /* Base @ of Sector 8, 128 Kbytes */
#define ADDR_FLASH_SECTOR_9     ((uint32_t)0x080A0000) /* Base @ of Sector 9, 128 Kbytes */
#define ADDR_FLASH_SECTOR_10    ((uint32_t)0x080C0000) /* Base @ of Sector 10, 128 Kbytes */
#define ADDR_FLASH_SECTOR_11    ((uint32_t)0x080E0000) /* Base @ of Sector 11, 128 Kbytes */

/* ########################## Assert Selection ############################## */
/**
  * @brief Uncomment the line below to expanse the "assert_param" macro in the 
  *        HAL drivers code
  */
/* #define USE_FULL_ASSERT    1U */

/* USER CODE BEGIN Private defines */
typedef enum
{
	None = 0,
	Connection = 1,
	Disconnection = 2,
	Resume = 3,
	Suspend = 4,
}_USB_State;
extern _USB_State hid_State;

typedef struct
{
    const char* name;
    const char* cmd;
    uint8_t (*func_ptr)();
}struct_cmd_t;

typedef struct
{
    uint8_t EDLockFlag;
    uint64_t AccumBitCount;
    uint32_t AccumErrCount_msb;
    uint32_t AccumErrCount_lsb;

    uint32_t RealErrCount_msb;
    uint32_t RealErrCount_lsb;
    uint64_t RealBitCount;

    uint8_t EDEn_Flag;
    uint8_t EDEn_HistoryFlag;

    int16_t line_taps[7];	 		//118
    uint8_t	lane_num_taps;

    /******fixed pattern */
    uint16_t	 fixed_pat0;	//222
    uint16_t	 fixed_pat1;	//224
    uint16_t	 fixed_pat2;	//226
    uint16_t	 fixed_pat3;	//228

    float pre_fec_ber;              /*!< pre fec BER */
    float post_fec_ber;             /*!< post fec BER */
    uint64_t tot_frame_rev_cnt;			//fec total frame count pre corrected
    uint64_t tot_frame_corr_cnt;					//fec total frame count corrected
    uint64_t tot_frame_uncorr_cnt;
    uint64_t tot_symbols_corr_cnt;
    uint64_t tot_frames_err_cnt[16];         /**<  fec corrected frame count*/
    uint8_t fec_lock_flag;

    uint8_t FECEn_Flag;
    uint8_t FECEn_HistoryFlag;
}bert_TypeDef;

typedef struct
{
    uint8_t			 Password[4];    			//0~3
    uint8_t			 VendorName[12]; 			//4~15
    uint8_t      EVB_SN[16];     			//16-31
    uint32_t     MCU_ID[3];           //32-41
    uint16_t     Licence;             //44-45

    uint16_t     VCC_DAC;				 			//46
    uint16_t     VCC_DAC_slope;	 			//48
    int16_t      VCC_DAC_offset;			//50
    uint16_t		 I_slope;							//52
    int16_t      I_offset;						//54
    uint16_t     Vcc_slope;						//56
    int16_t      Vcc_offset;					//58
    int16_t			 Temp_offset;					//60
    uint16_t     IALARM;							//62

    uint8_t			 Interface;				//64 Line & Host
    uint8_t 	   DataRate;				//65 速率  70
    uint8_t      SignalMode;			//66 NRZ & PAM4
    uint8_t      FEC_Enable;			//67 FEC Eanbel
    uint8_t      FEC_DataRate;		//68 FEC DataRate
    uint8_t      ClkDivide;   		//69 Clock
    uint8_t 		 RxEnable;				//70开启误码测试 80
    uint8_t      RxAutoInvert; 		//71
    uint8_t 	   LaneTxPattern[8];		//Tx码型  72
    uint8_t 	   LaneRxPattern[8];   	//Rx码型  80
    uint8_t			 TxInvert[8]; 		    //88
    uint8_t      RxInvert[8]; 		    //96
    uint8_t      TxSquelch[8]; 		    //104

    uint8_t 	   HostTxPattern[8];		//Tx码型 112
    uint8_t 	   HostRxPattern[8];   	//Rx码型  120
    //======= State and Control ===========
    uint8_t			 ResetL     :1;	  //128
    uint8_t      LPMode     :1;					
    uint8_t			 ModPrsL    :1;					
    uint8_t			 IntL       :1;
    uint8_t			 Power      :1;	
    uint8_t      ModSEL     :1;
    uint8_t			 AuxState   :3;	
    //====================================

    uint8_t gain_boost;       //129
    uint8_t peaking_filter;   //130
    uint8_t host_rx_bw_value; //131
    uint8_t lane_rx_bw_value; //132
    uint8_t vga;							//133

    uint32_t rng_value;       //134-137
    uint16_t DDM_Vcc;         //138-139  
    int16_t DDM_Temp;         //142
    uint16_t DDM_I;           //144
    uint16_t DDM_POWER;       //146
    
    int  hostline_Taps[7];     //148  152  156 160 164 168  172

    // 光模块插拔检测相关参数
    uint32_t module_insert_count;   //176-179 模块插入次数计数器
    uint32_t module_remove_count;   //180-183 模块拔出次数计数器
    uint32_t total_plugin_cycles;   //184-187 总插拔周期数
    uint32_t module_present_time;   //188-191 模块在位时间(秒)
    uint32_t module_absent_time;    //192-195 模块离位时间(秒)
    uint8_t  module_present_state;  //196     当前模块在位状态
    uint8_t  module_detect_enable;  //197     插拔检测使能
    uint16_t debounce_time_ms;      //198-199 防抖时间(毫秒)
    uint32_t last_state_change_time;//200-203 上次状态变化时间

    uint8_t     Reserve2[26];		 //204 - 255
		
}C4_Table_TypeDef;
extern C4_Table_TypeDef *C4;
extern bert_TypeDef BER[2][8];		

#define STM32F0_ID 0x1FFFF7AC  /*STM32F0唯一ID起始地址*/
#define STM32F1_ID 0x1FFFF7E8  /*STM32F1唯一ID起始地址*/
#define STM32F2_ID 0x1FFF7A10  /*STM32F2唯一ID起始地址*/
#define STM32F3_ID 0x1FFFF7AC  /*STM32F3唯一ID起始地址*/
#define STM32F4_ID 0x1FFF7A10  /*STM32F4唯一ID起始地址*/
#define STM32F7_ID 0x1FF0F420  /*STM32F7唯一ID起始地址*/
#define STM32L0_ID 0x1FF80050  /*STM32L0唯一ID起始地址*/
#define STM32L1_ID 0x1FF80050  /*STM32L1唯一ID起始地址*/
#define STM32L4_ID 0x1FFF7590  /*STM32L4唯一ID起始地址*/
#define STM32H7_ID 0x1FF0F420  /*STM32H7唯一ID起始地址*/

#define SIZE_MAX_READ				0x40
#define SIZE_MAX_WRITE			0x40
#define VREF		 3.3
#define ADC_MAX  4095
extern uint32_t MCUID[3];
extern uint32_t bert_TimeCountStart[2][8];
extern uint32_t accum_time;
extern uint8_t TimerFlag;
extern uint8_t interface;
extern unsigned char C4_0_255_Table[256];

// 光模块插拔检测相关变量
extern uint8_t module_present_last_state;
extern uint32_t module_state_change_time;
extern uint32_t module_debounce_counter;

/* USER CODE END Private defines */

#ifdef __cplusplus
 extern "C" {
#endif
void Error_Handler(void);
void SetOutputRefClock(uint8_t Freq);
uint32_t Get_Time_Diff(uint32_t u32_TimeStart);

// 光模块插拔检测函数声明
void Module_PlugDetect_Init(void);
void Module_PlugDetect_Process(void);
void Module_PlugDetect_Reset_Counters(void);
uint8_t Module_Get_Present_State(void);
void Module_Update_Statistics(void);

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
