/**
 *
 * @file     portofino_pp_regs.h
 * <AUTHOR> Team
 * @date     08-18-2020
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2020 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    This file contains definitions for public registers.
 *
 *           NOTE: DO NOT EDIT THIS FILE MANUALLY. This is a generated file.
 *           Refer to details given in portofino_pp_regs.h
 *
 */

#ifndef PORTOFINO_PP_REGS_H
#define PORTOFINO_PP_REGS_H

#define RCV_IFC_CTRL_4_RDB              0x00005c70  
/****************************************************************************
* apb2_slv0_amba :: RCV_IFC_CTRL_4_RDB 
***************************************************************************/
#define RCV_IFC_CTRL_4_RDB_MD_RTM_RCV_DIN_OFF_MASK                            0x0000ff00   /*!< MD_RTM_RCV_DIN_OFF */ 
#define RCV_IFC_CTRL_4_RDB_MD_RTM_RCV_DIN_OFF_SHIFT                           8  

#endif /* PORTOFINO_PP_REGS_H */
