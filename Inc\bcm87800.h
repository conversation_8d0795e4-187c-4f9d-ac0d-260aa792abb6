/**
  ******************************************************************************
  * File Name          : in010c50.h
  * Description        : This file provides code for the configuration
  *                      of the in010c50.
  ******************************************************************************
  */
 
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __in010c50_H
#define __in010c50_H
#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
//#include "stm32f4xx_hal.h"
#include "type_defns.h"
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */
typedef struct {
    float prbs_ber;
    uint64_t prbs_err_LSB;
    uint64_t prbs_err_MSB;
    uint32_t pmon_lock;
    uint32_t pmon_loss_lock;
} prbs_err_ber_wlock_s;

bool bcm87800_download_firmware(void);
bool bcm87800_init_prbs(uint16_t setvcc);
bool bcm87800_clock_divider(void);
bool bcm87800_clock_divider_vco(void);
bool bcm87800_clock_divider2(void);
bool bcm87800_tx_prbs(uint16_t ch,uint16_t pattern);
bool bcm87800_rx_prbs(uint16_t ch,uint16_t pattern);
bool bcm87800_tx_emphasis(uint16_t ch);
bool bcm87800_tx_squelch(uint16_t ch,bool squelch);
bool bcm87800_tx_polarity(uint8_t ch,bool inverted);
bool bcm87800_rx_polarity(uint8_t ch,bool inverted);
bool bcm87800_Checker_Status(uint8_t ch);
bool bcm87800_enable_checker(uint8_t ch);
bool bcm87800_set_rx_info(uint8_t ch);
bool bcm87800_disable_prbs(void);

bool bcm87800_set_fec_status(uint8_t lane);
bool bcm87800_set_fec_pgen(uint8_t option);
bool bcm87800_set_fec_init(uint16_t ch,boolean enable);
bool bcm87800_set_fec_clear(uint16_t ch);
bool bcm87800_set_fec_clear_all(void);

return_result_t get_line_Checker_Status(uint8_t ch);
return_result_t get_host_Checker_Status(uint8_t ch);

bool bcm87800_set_fec_enabled(bool enabled);

#ifdef __cplusplus
}
#endif
#endif /*__ in010c50_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
