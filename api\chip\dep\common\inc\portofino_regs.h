/**
 *
 * @file     portofino_regs.h
 * <AUTHOR> Team
 * @date     08-18-2020
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2020 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    This file contains definitions for public registers.
 *
 *           NOTE: DO NOT EDIT THIS FILE MANUALLY. This is a generated file.
 *           Refer to details given in portofino_regs.h
 *
 */

#ifndef PORTOFINO_REGS_H
#define PORTOFINO_REGS_H

#define MGT                                   0x40000000
#define SPIF                                  0x21000000
#define APB2_SLV0_REGS                        0x52000000
#define APB2_SLV3_REGS                        0x52000000
#define MEDIA_BASE                            0x58000000
#define HOST_BASE                             0x50000000
#define TOP_LW_BLOCK_CH0                      0x00000000
#define TOP_LW_BLOCK_CH1                      0x00010000
#define TOP_LW_BLOCK_CH2                      0x00020000
#define TOP_LW_BLOCK_CH3                      0x00030000
#define TOP_LW_BLOCK_CH4                      0x00040000
#define TOP_LW_BLOCK_CH5                      0x00050000
#define TOP_LW_BLOCK_CH6                      0x00060000
#define TOP_LW_BLOCK_CH7                      0x00070000
#define COM_COM_CTRL                                                0x00001090  
#define COM_REMAP                                                   0x00001080  
#define GPIO_GPIO_EXT_PORTA                                         0x0000c050  
#define GPIO_GPIO_SWPORTA_DDR                                       0x0000c004  
#define GPIO_GPIO_SWPORTA_DR                                        0x0000c000  
#define MDIOM_CMD                                                   0x0000b004  
#define MDIOM_MDIOM_CTRL                                            0x0000b000  
#define PCR_RST_CTR                                                 0x00000000  
#define SPIF_CTRL                                                   0x0000008c  
#define SPIF_RLEN                                                   0x00000088  
#define SPIF_RFIFO_DEPTH                                            0x00000084  
#define SPIF_STAT                                                   0x00000090  
#define SPIF_WRFIFO                                                 0x00000000  
#define QUAD_CORE_CONFIG_CFG_CHIPID_0_RDB                           0x0001d000  
#define QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB                           0x0001d004  
#define QUAD_CORE_CONFIG_CFG_PKGID_0_RDB                            0x0001d018  
#define QUAD_CORE_CONFIG_CFG_PKGID_1_RDB                            0x0001d01c  
#define QUAD_CORE_CONFIG_CFGVAL_0_RDB                               0x0001d400  
#define QUAD_CORE_CONFIG_CFGVAL_10_RDB                              0x0001d428  
#define QUAD_CORE_CONFIG_CFGVAL_11_RDB                              0x0001d42c  
#define QUAD_CORE_CONFIG_CFGVAL_123_RDB                             0x0001d5ec  
#define QUAD_CORE_CONFIG_CFGVAL_127_RDB                             0x0001d5fc  
#define QUAD_CORE_CONFIG_CFGVAL_12_RDB                              0x0001d430  
#define QUAD_CORE_CONFIG_CFGVAL_13_RDB                              0x0001d434  
#define QUAD_CORE_CONFIG_CFGVAL_14_RDB                              0x0001d438  
#define QUAD_CORE_CONFIG_CFGVAL_15_RDB                              0x0001d43c  
#define QUAD_CORE_CONFIG_CFGVAL_16_RDB                              0x0001d440  
#define QUAD_CORE_CONFIG_CFGVAL_17_RDB                              0x0001d444  
#define QUAD_CORE_CONFIG_CFGVAL_18_RDB                              0x0001d448  
#define QUAD_CORE_CONFIG_CFGVAL_19_RDB                              0x0001d44c  
#define QUAD_CORE_CONFIG_CFGVAL_20_RDB                              0x0001d450  
#define QUAD_CORE_CONFIG_CFGVAL_21_RDB                              0x0001d454  
#define QUAD_CORE_CONFIG_CFGVAL_22_RDB                              0x0001d458  
#define QUAD_CORE_CONFIG_CFGVAL_23_RDB                              0x0001d45c  
#define QUAD_CORE_CONFIG_CFGVAL_2_RDB                               0x0001d408  
#define QUAD_CORE_CONFIG_CFGVAL_32_RDB                              0x0001d480  
#define QUAD_CORE_CONFIG_CFGVAL_36_RDB                              0x0001d490  
#define QUAD_CORE_CONFIG_CFGVAL_37_RDB                              0x0001d494  
#define QUAD_CORE_CONFIG_CFGVAL_40_RDB                              0x0001d4a0  
#define QUAD_CORE_CONFIG_CFGVAL_43_RDB                              0x0001d4ac  
#define QUAD_CORE_CONFIG_CFGVAL_63_RDB                              0x0001d4fc  
#define QUAD_CORE_CONFIG_CFGVAL_80_RDB                              0x0001d540  
#define QUAD_CORE_CONFIG_CFGVAL_81_RDB                              0x0001d544  
#define QUAD_CORE_CONFIG_CFGVAL_84_RDB                              0x0001d550  
#define QUAD_CORE_CONFIG_CFGVAL_85_RDB                              0x0001d554  
#define QUAD_CORE_CONFIG_CFGVAL_86_RDB                              0x0001d558  
#define QUAD_CORE_CONFIG_CFGVAL_87_RDB                              0x0001d55c  
#define QUAD_CORE_CONFIG_CFGVAL_88_RDB                              0x0001d560  
#define QUAD_CORE_CONFIG_CFGVAL_89_RDB                              0x0001d564  
#define QUAD_CORE_CONFIG_CFGVAL_8_RDB                               0x0001d420  
#define QUAD_CORE_CONFIG_CFGVAL_90_RDB                              0x0001d568  
#define QUAD_CORE_CONFIG_CFGVAL_91_RDB                              0x0001d56c  
#define QUAD_CORE_CONFIG_CFGVAL_92_RDB                              0x0001d570  
#define QUAD_CORE_CONFIG_CFGVAL_93_RDB                              0x0001d574  
#define QUAD_CORE_CONFIG_CFGVAL_94_RDB                              0x0001d578  
#define QUAD_CORE_CONFIG_CFGVAL_97_RDB                              0x0001d584  
#define QUAD_CORE_CONFIG_CFGVAL_98_RDB                              0x0001d588  
#define QUAD_CORE_CONFIG_CFGVAL_9_RDB                               0x0001d424  
#define QUAD_CORE_EGRMGT_PAD_DRIVE_CTRL_11_RDB                      0x0001c3b0  
#define QUAD_CORE_EGRMGT_PAD_DRIVE_CTRL_2_RDB                       0x0001c38c  
#define QUAD_CORE_EGRMGT_QC_CHIP_SW_RST_RDB                         0x0001c000  
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB                              0x0001c008  
#define QUAD_CORE_EGRMGT_QC_SW_RST_RDB                              0x0001c004  
#define QUAD_CORE_GPCTRL_GPCTRL_100_RDB                             0x0001c990  
#define QUAD_CORE_GPCTRL_GPCTRL_101_RDB                             0x0001c994  
#define QUAD_CORE_GPCTRL_GPCTRL_102_RDB                             0x0001c998  
#define QUAD_CORE_GPCTRL_GPCTRL_103_RDB                             0x0001c99c  
#define QUAD_CORE_GPCTRL_GPCTRL_10_RDB                              0x0001c828  
#define QUAD_CORE_GPCTRL_GPCTRL_11_RDB                              0x0001c82c  
#define QUAD_CORE_GPCTRL_GPCTRL_164_RDB                             0x0001ca90  
#define QUAD_CORE_GPCTRL_GPCTRL_165_RDB                             0x0001ca94  
#define QUAD_CORE_GPCTRL_GPCTRL_179_RDB                             0x0001cacc  
#define QUAD_CORE_GPCTRL_GPCTRL_180_RDB                             0x0001cad0  
#define QUAD_CORE_GPCTRL_GPCTRL_182_RDB                             0x0001cad8  
#define QUAD_CORE_GPCTRL_GPCTRL_184_RDB                             0x0001cae0  
#define QUAD_CORE_GPCTRL_GPCTRL_185_RDB                             0x0001cae4  
#define QUAD_CORE_GPCTRL_GPCTRL_1_RDB                               0x0001c804  
#define QUAD_CORE_GPCTRL_GPCTRL_202_RDB                             0x0001cb28  
#define QUAD_CORE_GPCTRL_GPCTRL_203_RDB                             0x0001cb2c  
#define QUAD_CORE_GPCTRL_GPCTRL_213_RDB                             0x0001cb54  
#define QUAD_CORE_GPCTRL_GPCTRL_214_RDB                             0x0001cb58  
#define QUAD_CORE_GPCTRL_GPCTRL_219_RDB                             0x0001cb6c  
#define QUAD_CORE_GPCTRL_GPCTRL_222_RDB                             0x0001cb78  
#define QUAD_CORE_GPCTRL_GPCTRL_223_RDB                             0x0001cb7c  
#define QUAD_CORE_GPCTRL_GPCTRL_2_RDB                               0x0001c808  
#define QUAD_CORE_GPCTRL_GPCTRL_31_RDB                              0x0001c87c  
#define QUAD_CORE_GPCTRL_GPCTRL_324_RDB                             0x0001cd10  
#define QUAD_CORE_GPCTRL_GPCTRL_32_RDB                              0x0001c880  
#define QUAD_CORE_GPCTRL_GPCTRL_34_RDB                              0x0001c888  
#define QUAD_CORE_GPCTRL_GPCTRL_35_RDB                              0x0001c88c  
#define QUAD_CORE_GPCTRL_GPCTRL_36_RDB                              0x0001c890  
#define QUAD_CORE_GPCTRL_GPCTRL_372_RDB                             0x0001cdd0  
#define QUAD_CORE_GPCTRL_GPCTRL_37_RDB                              0x0001c894  
#define QUAD_CORE_GPCTRL_GPCTRL_40_RDB                              0x0001c8a0  
#define QUAD_CORE_GPCTRL_GPCTRL_41_RDB                              0x0001c8a4  
#define QUAD_CORE_GPCTRL_GPCTRL_42_RDB                              0x0001c8a8  
#define QUAD_CORE_GPCTRL_GPCTRL_43_RDB                              0x0001c8ac  
#define QUAD_CORE_GPCTRL_GPCTRL_44_RDB                              0x0001c8b0  
#define QUAD_CORE_GPCTRL_GPCTRL_45_RDB                              0x0001c8b4  
#define QUAD_CORE_GPCTRL_GPCTRL_46_RDB                              0x0001c8b8  
#define QUAD_CORE_GPCTRL_GPCTRL_47_RDB                              0x0001c8bc  
#define QUAD_CORE_GPCTRL_GPCTRL_48_RDB                              0x0001c8c0  
#define QUAD_CORE_GPCTRL_GPCTRL_49_RDB                              0x0001c8c4  
#define QUAD_CORE_GPCTRL_GPCTRL_508_RDB                             0x0001cff0  
#define QUAD_CORE_GPCTRL_GPCTRL_509_RDB                             0x0001cff4  
#define QUAD_CORE_GPCTRL_GPCTRL_50_RDB                              0x0001c8c8  
#define QUAD_CORE_GPCTRL_GPCTRL_510_RDB                             0x0001cff8  
#define QUAD_CORE_GPCTRL_GPCTRL_511_RDB                             0x0001cffc  
#define QUAD_CORE_GPCTRL_GPCTRL_51_RDB                              0x0001c8cc  
#define QUAD_CORE_GPCTRL_GPCTRL_58_RDB                              0x0001c8e8  
#define QUAD_CORE_GPCTRL_GPCTRL_59_RDB                              0x0001c8ec  
#define QUAD_CORE_GPCTRL_GPCTRL_6_RDB                               0x0001c818  
#define QUAD_CORE_GPCTRL_GPCTRL_7_RDB                               0x0001c81c  
#define QUAD_CORE_GPCTRL_GPCTRL_8_RDB                               0x0001c820  
#define QUAD_CORE_GPCTRL_GPCTRL_92_RDB                              0x0001c970  
#define QUAD_CORE_GPCTRL_GPCTRL_93_RDB                              0x0001c974  
#define QUAD_CORE_GPCTRL_GPCTRL_94_RDB                              0x0001c978  
#define QUAD_CORE_GPCTRL_GPCTRL_95_RDB                              0x0001c97c  
#define QUAD_CORE_GPCTRL_GPCTRL_98_RDB                              0x0001c988  
#define QUAD_CORE_GPCTRL_GPCTRL_99_RDB                              0x0001c98c  
#define QUAD_CORE_GPCTRL_GPCTRL_9_RDB                               0x0001c824  
#define REG_BOOT_EN_CTRL                                            0x0001db04  
#define CAPMEM_CM_CFG                                               0x00004c44  
#define FFE_EQ_CTRL_1                                               0x000055c4  
#define FFE_FFE_MSE_CTRL_0                                          0x000055d8  
#define FFE_FFE_MSE_STAT_0                                          0x000055f0  
#define FFE_FFE_MSE_STAT_1                                          0x000055f4  
#define LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG                            0x00005380  
#define LWRX_DFE_LWRX_DFE_MSE_STAT_0_REG                            0x00005398  
#define LWRX_DFE_LWRX_DFE_MSE_STAT_1_REG                            0x0000539c  
#define LWTX_FIR_LWTX_FIR_CONTROL_0_REG                             0x00000904  
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG                      0x00000910  
#define LWTX_FIR_LWTX_FIR_LUT_4T_RDAT_REG                           0x00000918  
#define LWTX_FIR_LWTX_FIR_LUT_4T_WDAT_REG                           0x00000914  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_11_REG                   0x0000096c  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_15_REG                   0x0000097c  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_19_REG                   0x0000098c  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_23_REG                   0x0000099c  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_27_REG                   0x000009ac  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_3_REG                    0x0000094c  
#define LWTX_FIR_LWTX_FIR_LUT_7TAP_CONTROL_7_REG                    0x0000095c  
#define MD_CDR_CDR_PMD_INTEG_CONTROL2                               0x00004d10  
#define VA_VA_CTRL_1_REG                                            0x00006300  
#define ANA_PLL_ANA_PLL_CTRL_19_REGISTER                            0x0000804c  
#define ANA_PLL_ANA_PLL_CTRL_23_REGISTER                            0x0000805c  
#define LWPLL_CTRL_LOCKDET_CTRL_0                                   0x00008124  
#define LWPLL_CTRL_LOCKDET_STAT_0                                   0x00008130  
#define LWPLL_CTRL_PLLCAL_CTRL_0                                    0x0000810c  
/****************************************************************************
* com_mgt :: COM_COM_CTRL 
***************************************************************************/
#define COM_COM_CTRL_BOOT_EN_OUT_MASK                                                              0x00000002   /*!< BOOT_EN_OUT */ 
#define COM_COM_CTRL_BOOT_EN_OUT_SHIFT                                                             1  
/****************************************************************************
* com_mgt :: GPIO_GPIO_EXT_PORTA 
***************************************************************************/
#define GPIO_GPIO_EXT_PORTA_GPIO_EXT_PORTA_MASK                                                    0x0000ffff   /*!< GPIO_EXT_PORTA */ 
#define GPIO_GPIO_EXT_PORTA_GPIO_EXT_PORTA_SHIFT                                                   0  
/****************************************************************************
* com_mgt :: GPIO_GPIO_SWPORTA_DDR 
***************************************************************************/
#define GPIO_GPIO_SWPORTA_DDR_GPIO_SWPORTA_DDR_MASK                                                0x0000ffff   /*!< GPIO_SWPORTA_DDR */ 
#define GPIO_GPIO_SWPORTA_DDR_GPIO_SWPORTA_DDR_SHIFT                                               0  
/****************************************************************************
* com_mgt :: GPIO_GPIO_SWPORTA_DR 
***************************************************************************/
#define GPIO_GPIO_SWPORTA_DR_GPIO_SWPORTA_DR_MASK                                                  0x0000ffff   /*!< GPIO_SWPORTA_DR */ 
#define GPIO_GPIO_SWPORTA_DR_GPIO_SWPORTA_DR_SHIFT                                                 0  
/****************************************************************************
* com_mgt :: PCR_RST_CTR 
***************************************************************************/
#define PCR_RST_CTR_M0P3_SRST_EN_MASK                                                              0x00001000   /*!< M0P3_SRST_EN */ 
#define PCR_RST_CTR_M0P2_SRST_EN_MASK                                                              0x00000800   /*!< M0P2_SRST_EN */ 
#define PCR_RST_CTR_M0P1_SRST_EN_MASK                                                              0x00000002   /*!< M0P1_SRST_EN */ 
#define PCR_RST_CTR_M0P0_SRST_EN_MASK                                                              0x00000001   /*!< M0P0_SRST_EN */ 
/****************************************************************************
* spif_amba :: SPIF_CTRL 
***************************************************************************/
#define SPIF_CTRL_SPIFEN_MASK                                                                      0x00000001   /*!< SPIFEN */ 
#define SPIF_CTRL_SPIFEN_SHIFT                                                                     0  
/****************************************************************************
* apb2_slv3_amba :: QUAD_CORE_CONFIG_CFG_CHIPID_0_RDB 
***************************************************************************/
#define QUAD_CORE_CONFIG_CFG_CHIPID_0_RDB_CFG_CHIPID_15_0_MASK                                     0x0000ffff   /*!< CFG_CHIPID_15_0 */ 
#define QUAD_CORE_CONFIG_CFG_CHIPID_0_RDB_CFG_CHIPID_15_0_SHIFT                                    0  
/****************************************************************************
* apb2_slv3_amba :: QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB 
***************************************************************************/
#define QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB_CFG_REVID_MASK                                           0x00000ff0   /*!< CFG_REVID */ 
#define QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB_CFG_REVID_SHIFT                                          4  
#define QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB_CFG_CHIPID_19_16_MASK                                    0x0000000f   /*!< CFG_CHIPID_19_16 */ 
#define QUAD_CORE_CONFIG_CFG_CHIPID_1_RDB_CFG_CHIPID_19_16_SHIFT                                   0  
#define QUAD_CORE_CONFIG_CFG_PKGID_1_RDB_CFG_PKGID_19_16_MASK                                      0x0000000f   /*!< CFG_PKGID_19_16 */ 
#define QUAD_CORE_CONFIG_CFG_PKGID_1_RDB_CFG_PKGID_19_16_SHIFT                                     0  
/****************************************************************************
* apb2_slv3_amba :: QUAD_CORE_EGRMGT_QC_CHIP_SW_RST_RDB 
***************************************************************************/
#define QUAD_CORE_EGRMGT_QC_CHIP_SW_RST_RDB_MD_CHIP_SW_RST_MASK                                    0x00000001   /*!< MD_CHIP_SW_RST */ 
#define QUAD_CORE_EGRMGT_QC_CHIP_SW_RST_RDB_MD_CHIP_SW_RST_SHIFT                                   0  
/****************************************************************************
* apb2_slv3_amba :: QUAD_CORE_EGRMGT_QC_DP_RST_RDB 
***************************************************************************/
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RTM_IGR_DP_RSTB_MASK                                     0x00000008   /*!< MD_RTM_IGR_DP_RSTB */ 
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RTM_IGR_DP_RSTB_SHIFT                                    3  
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RTM_EGR_DP_RSTB_MASK                                     0x00000004   /*!< MD_RTM_EGR_DP_RSTB */ 
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RTM_EGR_DP_RSTB_SHIFT                                    2  
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RPT_IGR_DP_RSTB_MASK                                     0x00000002   /*!< MD_RPT_IGR_DP_RSTB */ 
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RPT_IGR_DP_RSTB_SHIFT                                    1  
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RPT_EGR_DP_RSTB_MASK                                     0x00000001   /*!< MD_RPT_EGR_DP_RSTB */ 
#define QUAD_CORE_EGRMGT_QC_DP_RST_RDB_MD_RPT_EGR_DP_RSTB_SHIFT                                    0  
/****************************************************************************
* apb2_slv3_amba :: QUAD_CORE_EGRMGT_QC_SW_RST_RDB 
***************************************************************************/
#define QUAD_CORE_EGRMGT_QC_SW_RST_RDB_MD_RTM_ALL_RSTB_MASK                                        0x00000002   /*!< MD_RTM_ALL_RSTB */ 
#define QUAD_CORE_EGRMGT_QC_SW_RST_RDB_MD_RTM_ALL_RSTB_SHIFT                                       1  
/****************************************************************************
* apb2_slv3_amba :: REG_BOOT_EN_CTRL 
***************************************************************************/
#define REG_BOOT_EN_CTRL_BOOT_EN_VAL_MASK                                                          0x00000002   /*!< BOOT_EN_VAL */ 
#define REG_BOOT_EN_CTRL_BOOT_EN_VAL_SHIFT                                                         1  
#define REG_BOOT_EN_CTRL_BOOT_EN_OVR_MASK                                                          0x00000001   /*!< BOOT_EN_OVR */ 
#define REG_BOOT_EN_CTRL_BOOT_EN_OVR_SHIFT                                                         0  
#define CAPMEM_CM_CFG_RESERVED0_SHIFT                                                              20  
#define CAPMEM_CM_CFG_IVL_LEN_MASK                                                                 0x000f0000   /*!< IVL_LEN */ 
#define CAPMEM_CM_CFG_REPEAT_CAPTURE_MASK                                                          0x00008000   /*!< REPEAT_CAPTURE */ 
#define CAPMEM_CM_CFG_REPEAT_CAPTURE_SHIFT                                                         15  
/****************************************************************************
* lw_amba :: FFE_EQ_CTRL_1 
***************************************************************************/
#define FFE_EQ_CTRL_1_SLC_MODE_MASK                                                                0x00000c00   /*!< SLC_MODE */ 
#define FFE_EQ_CTRL_1_SLC_MODE_SHIFT                                                               10  
#define FFE_EQ_CTRL_1_DC_WANDER_ENABLE_MASK                                                        0x00000020   /*!< DC_WANDER_ENABLE */ 
#define FFE_EQ_CTRL_1_DC_WANDER_ENABLE_SHIFT                                                       5  
/****************************************************************************
* lw_amba :: FFE_FFE_MSE_CTRL_0 
***************************************************************************/
#define FFE_FFE_MSE_CTRL_0_MSE_OUTPUT_SELECT_MASK                                                  0x00000078   /*!< MSE_OUTPUT_SELECT */ 
#define FFE_FFE_MSE_CTRL_0_MSE_OUTPUT_SELECT_SHIFT                                                 3  
#define FFE_FFE_MSE_CTRL_0_MSE_OUTPUT_UPDT_MASK                                                    0x00000002   /*!< MSE_OUTPUT_UPDT */ 
#define FFE_FFE_MSE_CTRL_0_MSE_OUTPUT_UPDT_SHIFT                                                   1  
/****************************************************************************
* lw_amba :: LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG 
***************************************************************************/
#define LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG_MSE_OUTPUT_SELECT_MASK                                    0x00000078   /*!< MSE_OUTPUT_SELECT */ 
#define LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG_MSE_OUTPUT_SELECT_SHIFT                                   3  
#define LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG_MSE_OUTPUT_UPDT_MASK                                      0x00000002   /*!< MSE_OUTPUT_UPDT */ 
#define LWRX_DFE_LWRX_DFE_MSE_CTRL_0_REG_MSE_OUTPUT_UPDT_SHIFT                                     1  
/****************************************************************************
* lw_amba :: LWRX_DFE_LWRX_DFE_MSE_STAT_0_REG 
***************************************************************************/
#define LWRX_DFE_LWRX_DFE_MSE_STAT_0_REG_MSE_OPT_VALUE_LO_MASK                                     0x0000ffff   /*!< MSE_OPT_VALUE_LO */ 
#define LWRX_DFE_LWRX_DFE_MSE_STAT_0_REG_MSE_OPT_VALUE_LO_SHIFT                                    0  
/****************************************************************************
* lw_amba :: LWRX_DFE_LWRX_DFE_MSE_STAT_1_REG 
***************************************************************************/
#define LWRX_DFE_LWRX_DFE_MSE_STAT_1_REG_MSE_OPT_VALUE_HI_MASK                                     0x00000fff   /*!< MSE_OPT_VALUE_HI */ 
#define LWRX_DFE_LWRX_DFE_MSE_STAT_1_REG_MSE_OPT_VALUE_HI_SHIFT                                    0  
/****************************************************************************
* lw_amba :: LWTX_FIR_LWTX_FIR_CONTROL_0_REG 
***************************************************************************/
#define LWTX_FIR_LWTX_FIR_CONTROL_0_REG_LOAD_7TAP_LUT_MASK                                         0x00000008   /*!< LOAD_7TAP_LUT */ 
#define LWTX_FIR_LWTX_FIR_CONTROL_0_REG_LOAD_7TAP_LUT_SHIFT                                        3  
/****************************************************************************
* lw_amba :: LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG 
***************************************************************************/
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_ADR_MASK                                     0x0000ff00   /*!< LUT_4T_ADR */ 
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_ADR_SHIFT                                    8  
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_AUTO_ADR_EN_MASK                             0x00000004   /*!< LUT_4T_AUTO_ADR_EN */ 
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_AUTO_ADR_EN_SHIFT                            2  
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_AUTO_UPDTE_EN_MASK                           0x00000002   /*!< LUT_4T_AUTO_UPDTE_EN */ 
#define LWTX_FIR_LWTX_FIR_LUT_4T_CONTROL_0_REG_LUT_4T_AUTO_UPDTE_EN_SHIFT                          1  
/****************************************************************************
* lw_amba :: LWTX_FIR_LWTX_FIR_LUT_4T_WDAT_REG 
***************************************************************************/
#define LWTX_FIR_LWTX_FIR_LUT_4T_WDAT_REG_LUT_4T_WDAT_MASK                                         0x0000007f   /*!< LUT_4T_WDAT */ 
#define LWTX_FIR_LWTX_FIR_LUT_4T_WDAT_REG_LUT_4T_WDAT_SHIFT                                        0  
/****************************************************************************
* lw_amba :: MD_CDR_CDR_PMD_INTEG_CONTROL2 
***************************************************************************/
#define MD_CDR_CDR_PMD_INTEG_CONTROL2_BW_INTEG_MASK                                                0x000000f0   /*!< BW_INTEG */ 
#define MD_CDR_CDR_PMD_INTEG_CONTROL2_BW_INTEG_SHIFT                                               4  
#define MD_CDR_CDR_PMD_INTEG_CONTROL2_BW_PROP_MASK                                                 0x0000000f   /*!< BW_PROP */ 
#define MD_CDR_CDR_PMD_INTEG_CONTROL2_BW_PROP_SHIFT                                                0  
/****************************************************************************
* lw_amba :: VA_VA_CTRL_1_REG 
***************************************************************************/
#define VA_VA_CTRL_1_REG_VA_ENABLE_A_ADAP_MASK                                                     0x00000800   /*!< VA_ENABLE_A_ADAP */ 
#define VA_VA_CTRL_1_REG_VA_ENABLE_A_ADAP_SHIFT                                                    11  
/****************************************************************************
* lw_com_amba :: ANA_PLL_ANA_PLL_CTRL_19_REGISTER 
***************************************************************************/
#define ANA_PLL_ANA_PLL_CTRL_19_REGISTER_MD_ANA_PLL_EN_DOUBLER_MASK                                0x00000008   /*!< MD_ANA_PLL_EN_DOUBLER */ 
#define ANA_PLL_ANA_PLL_CTRL_19_REGISTER_MD_ANA_PLL_EN_DOUBLER_SHIFT                               3  
/****************************************************************************
* lw_com_amba :: ANA_PLL_ANA_PLL_CTRL_23_REGISTER 
***************************************************************************/
#define ANA_PLL_ANA_PLL_CTRL_23_REGISTER_MD_ANA_PLL_DIVRATIO_REG_CLK_MASK                          0x00000180   /*!< MD_ANA_PLL_DIVRATIO_REG_CLK */ 
#define ANA_PLL_ANA_PLL_CTRL_23_REGISTER_MD_ANA_PLL_DIVRATIO_REG_CLK_SHIFT                         7  
/****************************************************************************
* lw_com_amba :: LWPLL_CTRL_LOCKDET_CTRL_0 
***************************************************************************/
#define LWPLL_CTRL_LOCKDET_CTRL_0_LKDT_BYPASS_MASK                                                 0x00000001   /*!< LKDT_BYPASS */ 
#define LWPLL_CTRL_LOCKDET_CTRL_0_LKDT_BYPASS_SHIFT                                                0  
/****************************************************************************
* lw_com_amba :: LWPLL_CTRL_LOCKDET_STAT_0 
***************************************************************************/
#define LWPLL_CTRL_LOCKDET_STAT_0_PLL_LKDT_MASK                                                    0x00000001   /*!< PLL_LKDT */ 
#define LWPLL_CTRL_LOCKDET_STAT_0_PLL_LKDT_SHIFT                                                   0  
/****************************************************************************
* lw_com_amba :: LWPLL_CTRL_PLLCAL_CTRL_0 
***************************************************************************/
#define LWPLL_CTRL_PLLCAL_CTRL_0_PLLCAL_PLL_PWRDN_MASK                                             0x00000002   /*!< PLLCAL_PLL_PWRDN */ 
#define LWPLL_CTRL_PLLCAL_CTRL_0_PLLCAL_PLL_PWRDN_SHIFT                                            1  

#endif /* PORTOFINO_REGS_H */
