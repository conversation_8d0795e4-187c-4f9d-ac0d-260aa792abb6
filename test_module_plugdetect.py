#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
光模块插拔检测功能测试脚本
适用于DEBX-08112A-VA高速光模块测试设备

作者: AI Assistant
日期: 2025-01-13
版本: 1.0
"""

import serial
import time
import sys
import argparse

class ModulePlugDetectTester:
    def __init__(self, port='COM3', baudrate=115200, timeout=1):
        """
        初始化测试器
        
        Args:
            port: 串口端口号
            baudrate: 波特率
            timeout: 超时时间
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        
    def connect(self):
        """连接到设备"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=self.timeout)
            print(f"✅ 成功连接到 {self.port}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("🔌 已断开连接")
    
    def send_command(self, command):
        """
        发送命令并接收响应
        
        Args:
            command: 要发送的命令
            
        Returns:
            响应字符串
        """
        if not self.ser or not self.ser.is_open:
            print("❌ 设备未连接")
            return None
            
        try:
            # 发送命令
            cmd_bytes = (command + '\r\n').encode('utf-8')
            self.ser.write(cmd_bytes)
            print(f"📤 发送: {command}")
            
            # 接收响应
            response = self.ser.readline().decode('utf-8').strip()
            print(f"📥 接收: {response}")
            return response
            
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return None
    
    def test_device_info(self):
        """测试设备信息"""
        print("\n🔍 测试设备信息...")
        response = self.send_command("*IDN?")
        if response:
            print(f"📋 设备信息: {response}")
            return True
        return False
    
    def test_enable_detection(self, enable=True):
        """测试插拔检测使能"""
        print(f"\n🔧 {'启用' if enable else '禁用'}插拔检测...")
        
        # 设置检测使能
        cmd = f"SYS:MODULE:PLUGDETECT {1 if enable else 0}"
        response = self.send_command(cmd)
        
        if response == "1":
            print(f"✅ 插拔检测{'启用' if enable else '禁用'}成功")
            
            # 查询确认
            response = self.send_command("SYS:MODULE:PLUGDETECT?")
            expected = "1" if enable else "0"
            if response == expected:
                print(f"✅ 状态确认: {'已启用' if enable else '已禁用'}")
                return True
            else:
                print(f"❌ 状态确认失败，期望: {expected}, 实际: {response}")
        else:
            print(f"❌ 设置失败，响应: {response}")
        
        return False
    
    def test_debounce_setting(self, debounce_ms=150):
        """测试防抖时间设置"""
        print(f"\n⏱️ 设置防抖时间为 {debounce_ms}ms...")
        
        # 设置防抖时间
        response = self.send_command(f"SYS:MODULE:DEBOUNCE {debounce_ms}")
        
        if response == "1":
            print("✅ 防抖时间设置成功")
            
            # 查询确认
            response = self.send_command("SYS:MODULE:DEBOUNCE?")
            if response == str(debounce_ms):
                print(f"✅ 防抖时间确认: {response}ms")
                return True
            else:
                print(f"❌ 防抖时间确认失败，期望: {debounce_ms}, 实际: {response}")
        else:
            print(f"❌ 防抖时间设置失败，响应: {response}")
        
        return False
    
    def test_current_state(self):
        """测试当前状态读取"""
        print("\n📊 读取当前模块状态...")
        
        response = self.send_command("SYS:MODULE:STATE?")
        if response in ["0", "1"]:
            state_text = "在位" if response == "1" else "不在位"
            print(f"📍 当前模块状态: {state_text} ({response})")
            return True, int(response)
        else:
            print(f"❌ 状态读取失败，响应: {response}")
            return False, None
    
    def test_statistics(self):
        """测试统计数据读取"""
        print("\n📈 读取插拔统计数据...")
        
        response = self.send_command("SYS:MODULE:STATS?")
        if response and ',' in response:
            try:
                data = response.split(',')
                if len(data) == 6:
                    insert_count = int(data[0])
                    remove_count = int(data[1])
                    total_cycles = int(data[2])
                    present_time = int(data[3])
                    absent_time = int(data[4])
                    current_state = int(data[5])
                    
                    print("📊 统计数据:")
                    print(f"   🔌 插入次数: {insert_count}")
                    print(f"   🔌 拔出次数: {remove_count}")
                    print(f"   🔄 总插拔周期: {total_cycles}")
                    print(f"   ⏰ 在位时间: {present_time} 秒")
                    print(f"   ⏰ 离位时间: {absent_time} 秒")
                    print(f"   📍 当前状态: {'在位' if current_state else '不在位'}")
                    
                    return True, {
                        'insert_count': insert_count,
                        'remove_count': remove_count,
                        'total_cycles': total_cycles,
                        'present_time': present_time,
                        'absent_time': absent_time,
                        'current_state': current_state
                    }
                else:
                    print(f"❌ 数据格式错误，期望6个字段，实际: {len(data)}")
            except ValueError as e:
                print(f"❌ 数据解析失败: {e}")
        else:
            print(f"❌ 统计数据读取失败，响应: {response}")
        
        return False, None
    
    def test_reset_counters(self):
        """测试计数器重置"""
        print("\n🔄 重置插拔计数器...")
        
        response = self.send_command("SYS:MODULE:RESET 1")
        if response == "1":
            print("✅ 计数器重置成功")
            
            # 验证重置结果
            time.sleep(0.5)  # 等待保存完成
            success, stats = self.test_statistics()
            if success and stats:
                if (stats['insert_count'] == 0 and 
                    stats['remove_count'] == 0 and 
                    stats['total_cycles'] == 0):
                    print("✅ 计数器重置验证成功")
                    return True
                else:
                    print("❌ 计数器重置验证失败，仍有非零值")
            else:
                print("❌ 无法验证重置结果")
        else:
            print(f"❌ 计数器重置失败，响应: {response}")
        
        return False
    
    def monitor_plugging(self, duration=30):
        """监控插拔操作"""
        print(f"\n👀 开始监控插拔操作 ({duration}秒)...")
        print("💡 请在监控期间进行模块插拔操作")
        
        start_time = time.time()
        last_state = None
        
        while time.time() - start_time < duration:
            success, current_state = self.test_current_state()
            if success:
                if last_state is not None and last_state != current_state:
                    action = "插入" if current_state == 1 else "拔出"
                    print(f"🔔 检测到模块{action}操作!")
                    
                    # 读取最新统计
                    time.sleep(0.2)  # 等待统计更新
                    self.test_statistics()
                
                last_state = current_state
            
            time.sleep(1)  # 每秒检查一次
        
        print("⏰ 监控结束")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始光模块插拔检测功能测试")
        print("=" * 50)
        
        # 连接设备
        if not self.connect():
            return False
        
        try:
            # 测试设备信息
            if not self.test_device_info():
                print("❌ 设备信息测试失败")
                return False
            
            # 启用插拔检测
            if not self.test_enable_detection(True):
                print("❌ 插拔检测启用失败")
                return False
            
            # 设置防抖时间
            if not self.test_debounce_setting(150):
                print("❌ 防抖时间设置失败")
                return False
            
            # 读取当前状态
            self.test_current_state()
            
            # 读取统计数据
            self.test_statistics()
            
            # 重置计数器测试
            if not self.test_reset_counters():
                print("❌ 计数器重置测试失败")
                return False
            
            # 监控插拔操作
            self.monitor_plugging(30)
            
            # 最终统计
            print("\n📊 最终统计数据:")
            self.test_statistics()
            
            print("\n✅ 所有测试完成!")
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            return False
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            return False
        finally:
            self.disconnect()

def main():
    parser = argparse.ArgumentParser(description='光模块插拔检测功能测试')
    parser.add_argument('--port', default='COM3', help='串口端口号 (默认: COM3)')
    parser.add_argument('--baudrate', type=int, default=115200, help='波特率 (默认: 115200)')
    parser.add_argument('--timeout', type=float, default=1.0, help='超时时间 (默认: 1.0秒)')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = ModulePlugDetectTester(args.port, args.baudrate, args.timeout)
    
    # 运行测试
    success = tester.run_full_test()
    
    # 退出
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
