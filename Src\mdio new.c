
#include "main.h"
#include "mdio.h"
//=====================================
#define mdio_SDA(PinState) HAL_GPIO_WritePin(SDA_GPIO_Port,SDA_Pin,(GPIO_PinState)PinState)
#define mdio_SCL(PinState) HAL_GPIO_WritePin(SCL_GPIO_Port,SCL_Pin,(GPIO_PinState)PinState)


unsigned int MDIO_CLK_WIDE = 40;		// = 0 ???????609ns;38=400K;191=100K
unsigned char MDIO_CLK_WIDE_Low = 40;
void delay_ns(uint32_t nTime);
//=====================================
//start
void mdio_start(void)
{
	mdio_SCL(1); //SCL = 1
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SDA(1); //SDA = 1;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SDA(0); //SDA = 0;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(0); //SCL = 0
	delay_ns(MDIO_CLK_WIDE);
}


//=====================================
//stop
void mdio_stop(void)
{
	mdio_SCL(0); //SCL = 0
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SDA(0); //SDA = 0;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(1); //SCL = 1
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SDA(1); //SDA = 1;
	delay_ns(MDIO_CLK_WIDE);
}

//==========================================
//MASTER NO ACK
void mdio_master_no_ack(void)
{

	mdio_SDA(1); //SDA = 1;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(1); //SCL = 1
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(0); //SCL = 0
	delay_ns(MDIO_CLK_WIDE);
}


//=====================================
//MASTER  ACK
void mdio_master_ack(void)
{
	mdio_SDA(0); //SDA = 0;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(1); //SCL = 1
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(0); //SCL = 0
	delay_ns(MDIO_CLK_WIDE);

}

/*--------------------------------------
* SLAVE ACK
* Return:   0: Success	1: Failure.
---------------------------------------*/
uint8_t mdio_slave_ack(void)
{
	unsigned char error_time = 255;
	
	mdio_SDA(1); //SDA = 1;
	delay_ns(MDIO_CLK_WIDE);
	
	mdio_SCL(1); //SCL = 1
	while(HAL_GPIO_ReadPin(SDA_GPIO_Port,SDA_Pin))	  //wait SDA = 0
	{
		error_time--;
		if(error_time == 0)	//timeout
		{
			mdio_SCL(0); //SCL = 0
			delay_ns(MDIO_CLK_WIDE);
			mdio_SCL(1); //SCL = 1
			delay_ns(MDIO_CLK_WIDE);
			mdio_SCL(0); //SCL = 0
			mdio_stop();
			return(1);
		}
	}
	delay_ns(MDIO_CLK_WIDE);
	mdio_SCL(0); //SCL = 0
	delay_ns(MDIO_CLK_WIDE);
	return(0);
}


//==========================================
//write 8 bit
void mdio_write_8bit(uint8_t byte)
{
	uint8_t i=8;
	while(i--)
	{
		mdio_SDA(byte & 0x80);
		byte <<=1;
		mdio_SCL(1); //SCL = 1
		delay_ns(MDIO_CLK_WIDE);	
		mdio_SCL(0); //SCL = 0
		delay_ns(MDIO_CLK_WIDE_Low);	
	}
}

//==========================================
//read 8 bit
static uint8_t mdio_read_8bit()
{
	uint8_t receive,i;
	
	mdio_SDA(1); //SDA = 1;
	delay_ns(MDIO_CLK_WIDE);	
	
	receive =0;
	i =8;
	while(i--)
	{
		receive <<=1;
		mdio_SCL(1); //SCL = 1
		delay_ns(MDIO_CLK_WIDE);			 //clk width
		if(HAL_GPIO_ReadPin(SDA_GPIO_Port,SDA_Pin))receive++;
		mdio_SCL(0); //SCL = 0
		delay_ns(MDIO_CLK_WIDE);			 //clk width
	}
	return(receive);
}

/*----------------------------------------------
* ?? count ?????,?? buffer ?????
* Return:   1: Success			0: Failure.
-----------------------------------------------*/
uint8_t mdio_ReadBytes(uint16_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length)
{
	mdio_start();
	
	mdio_write_8bit(Addr);			//set device Address
	if(mdio_slave_ack()) return(0);
	mdio_write_8bit(Reg);			//set Byte address
	if(mdio_slave_ack()) return(0);
	
	mdio_start();
	mdio_write_8bit(Addr+1);   	//command read	= device_address + 1
	if(mdio_slave_ack()) return(0);

	while(Length--)
	{
		*pBuffer = mdio_read_8bit();
		pBuffer++;
		if(Length !=0 )
		{
			mdio_master_ack();
		}
	}

	mdio_master_no_ack();	  				//n = 0 end send NO ACK
	mdio_stop();
	return(1);
}

/*----------------------------------------------
* ?? count ?????,?? buffer ?????
* Return:   1: Success			0: Failure.
-----------------------------------------------*/
uint8_t mdio_ReadByte(uint16_t Addr, uint8_t Reg,uint8_t *value)
{
	mdio_start();
	
	mdio_write_8bit(Addr);			//set device Address
	if(mdio_slave_ack()) return(0);
	mdio_write_8bit(Reg);			//set Byte address
	if(mdio_slave_ack()) return(0);
	
	mdio_start();
	mdio_write_8bit(Addr+1);   	//command read	= device_address + 1
	if(mdio_slave_ack()) return(0);
	*value = mdio_read_8bit();
	mdio_master_no_ack();	  				//n = 0 end send NO ACK
	mdio_stop();
	return(1);
}

/*---------------------------------------------
* ? buffer ?????,?? count ?????
* Return:   1: Success	0: Failure.
-----------------------------------------------*/
uint8_t mdio_WriteBytes(uint16_t Addr, uint8_t Reg, uint8_t *pBuffer, uint16_t Length)
{
	mdio_start();
	mdio_write_8bit(Addr);		//set device Address
	if(mdio_slave_ack()) return(0);
	mdio_write_8bit(Reg);			//set Byte address
	mdio_slave_ack();
	while(Length--)
	{
		mdio_write_8bit(*pBuffer);
		pBuffer++;
		if(mdio_slave_ack()) return(0);
	}
	mdio_stop();
	return(1);
}

uint8_t mdio_WriteByte(uint16_t Addr, uint8_t Reg, uint8_t Value)
{
	mdio_start();
	mdio_write_8bit(Addr);			//set device Address
	if(mdio_slave_ack()) return(0);
	mdio_write_8bit(Reg);			//set Byte address
	mdio_slave_ack();
	mdio_write_8bit(Value);
	if(mdio_slave_ack()) return(0);
	mdio_stop();
	return(1);
}

void delay_ns(uint32_t nTime)
{
	while(nTime--);	//120MHz,time = 8.33ns,while = 0:58ns; = n:58+n*25; n = 1= 3cycle = 25ns
}


//unsigned long sal_readAhbI2cByte(unsigned char devaddr, uint32_t regaddr)
//{
//	unsigned long retdat, byte1, byte2, byte3, byte4;
//	
//	writeI2C(devaddr, 0x7F, 0xFF);
//	writeI2C(devaddr, IND_ADDR0_I2C, (unsigned char)regaddr);
//	writeI2C(devaddr, IND_ADDR1_I2C, (unsigned char)(regaddr >> 8));
//	writeI2C(devaddr, IND_ADDR2_I2C, (unsigned char)(regaddr >> 16));
//	writeI2C(devaddr, IND_ADDR3_I2C, (unsigned char)(regaddr >> 24));
//	
//	writeI2C(devaddr, IND_LEN0_I2C, 4);
//	writeI2C(devaddr, IND_LEN1_I2C, 0);
//	writeI2C(devaddr, IND_CTRL_I2C, 1);
//	byte1 = readI2C(devaddr, RFIFO_I2C); //16bit bit0 start
//	byte2 = readI2C(devaddr, RFIFO_I2C);
//	byte3 = readI2C(devaddr, RFIFO_I2C);
//	byte4 = readI2C(devaddr, RFIFO_I2C);
//	retdat = byte1 | (byte2 << 8) | (byte3 << 16) | (byte4 << 24);
//	return retdat;
//}

//void sal_writeAhbI2cByte(unsigned char devaddr, uint32_t regaddr, uint32_t data)
//{
//	writeI2C(devaddr, 0x7F, 0xFF);
//	writeI2C(devaddr, IND_ADDR0_I2C, (unsigned char)regaddr);
//	writeI2C(devaddr, IND_ADDR1_I2C, (unsigned char)(regaddr >> 8));
//	writeI2C(devaddr, IND_ADDR2_I2C, (unsigned char)(regaddr >> 16));
//	writeI2C(devaddr, IND_ADDR3_I2C, (unsigned char)(regaddr >> 24));

//	writeI2C(devaddr, IND_LEN0_I2C, 4);
//	writeI2C(devaddr, IND_LEN1_I2C, 0);
//	writeI2C(devaddr, IND_CTRL_I2C, 3); //Write 0x03 to IND_CTRL (Write)
//	writeI2C(devaddr, WFIFO_I2C, (unsigned char)data);
//	writeI2C(devaddr, WFIFO_I2C, (unsigned char)(data >> 8));
//	writeI2C(devaddr, WFIFO_I2C, (unsigned char)(data >> 16));
//	writeI2C(devaddr, WFIFO_I2C, (unsigned char)(data >> 24));
//}

unsigned long sal_readAhbI2cByte(unsigned char devaddr, uint32_t regaddr)
{
	unsigned char cmdData[7],rData[4];
	unsigned long retdat;

	devaddr = 0xa0;
	cmdData[0] = (unsigned char)regaddr;
	cmdData[1] = (unsigned char)(regaddr >> 8);
	cmdData[2] = (unsigned char)(regaddr >> 16);
	cmdData[3] = (unsigned char)(regaddr >> 24);
	cmdData[4] = 4;
	cmdData[5] = 0;
	cmdData[6] = 1;
	
	mdio_WriteByte(devaddr, 0x7F, 0xFF);
	mdio_WriteBytes(devaddr, IND_ADDR0_I2C,cmdData,7);
	
	mdio_ReadBytes(devaddr, RFIFO_I2C,rData,4);
	retdat = rData[0] | (rData[1] << 8) | (rData[2] << 16) | (rData[3] << 24);
	return retdat;
}

void sal_writeAhbI2cByte(unsigned char devaddr, uint32_t regaddr, uint32_t data)
{
	unsigned char wData[4],cmdData[7];
	cmdData[0]=(unsigned char)regaddr;
	cmdData[1]=(unsigned char)(regaddr >> 8);
	cmdData[2]=(unsigned char)(regaddr >> 16);
	cmdData[3]=(unsigned char)(regaddr >> 24);
	cmdData[4] = 4;
	cmdData[5] = 0;
	cmdData[6] = 3;
	
	devaddr = 0xa0;
	
	mdio_WriteByte(devaddr, 0x7F, 0xFF);
	mdio_WriteBytes(devaddr, IND_ADDR0_I2C,cmdData,7);

	wData[0]=(unsigned char)data;
	wData[1]=(unsigned char)(data >> 8);
	wData[2]=(unsigned char)(data >> 16);
	wData[3]=(unsigned char)(data >> 24);
	mdio_WriteBytes(devaddr, WFIFO_I2C,wData,4);
}

/* #define I2C_ACCESS */

uint32_t sal_rd_reg_ex(uint32_t address, uint8_t mdio_device, uint8_t mdio_port)
{
	unsigned long data;
	//    data = sal_rdmdio(mdio_port, mdio_device, address);
	data = sal_readAhbI2cByte(0x50, address);
	return data;
}

void sal_wr_reg_ex(uint32_t address, uint32_t data,
	uint8_t mdio_device, uint8_t mdio_port)
{
	//    sal_wrmdio(mdio_port, mdio_device, address, data);
	sal_writeAhbI2cByte(0x50, address, data);   //32bit
}


/** Read register value at specified address using default MDIO device type and
*  port address.
* @param address the address of the register
* @return the content of the register
*/
uint32_t sal_rd_reg(uint32_t address)
{
	return sal_rd_reg_ex(address, sal_current_mdio_dev, sal_current_mdio_port);
}

/** Write a value to the specified register along with MDIO device type and
*  port address.
* @param address the address of the register
* @param data the value to be written
*/
void sal_wr_reg(uint32_t address, uint32_t data)
{
	sal_wr_reg_ex(address, data, sal_current_mdio_dev, sal_current_mdio_port);
}

uint32_t rd_reg(uint32_t address)
{
	return sal_rd_reg(address);
}

void wr_reg(uint32_t address, uint32_t data)
{
	sal_wr_reg(address, data);
}

long rd_reg_ex(long a, long port)
{
	return rd_reg((uint32_t)a);
}

void wr_reg_ex(long a, long d, long port)
{
	wr_reg((uint32_t)a, d);
}
