/**
 *
 * @file cw_config.c
 * <AUTHOR> @date     09/01/2018
 * @version 1.0
 *
 * 
 * $Copyright: Copyright 2018 Broadcom INC.
 * This program is the proprietary software of Broadcom INC
 * and/or its licensors, and may only be used, duplicated, modified
 * or distributed pursuant to the terms and conditions of a separate,
 * written license agreement executed between you and Broadcom
 * (an "Authorized License").  Except as set forth in an Authorized
 * License, Broadcom grants no license (express or implied), right
 * to use, or waiver of any kind with respect to the Software, and
 * Broadcom expressly reserves all rights in and to the Software
 * and all intellectual property rights therein.  IF YOU HAVE
 * NO AUTHORIZED LICENSE, THEN YOU HAVE NO RIGHT TO USE THIS SOFTWARE
 * IN ANY WAY, AND SHOULD IMMEDIATELY NOTIFY BROADCOM AND DISCONTINUE
 * ALL USE OF THE SOFTWARE.
 * Except as expressly set forth in the Authorized License,
 * 1.     This program, including its structure, sequence and organization,
 * constitutes the valuable trade secrets of Broadcom, and you shall use
 * all reasonable efforts to protect the confidentiality thereof,
 * and to use this information only in connection with your use of
 * Broadcom integrated circuit products.
 * 2.     TO THE MAXIMUM EXTENT PERMITTED BY LAW, THE SOFTWARE IS
 * PROVIDED "AS IS" AND WITH ALL FAULTS AND BROADCOM MAKES NO PROMISES,
 * REPRESENTATIONS OR WARRANTIES, EITHER EXPRESS, IMPLIED, STATUTORY,
 * OR OTHERWISE, WITH RESPECT TO THE SOFTWARE.  BROADCOM SPECIFICALLY
 * DISCLAIMS ANY AND ALL IMPLIED WARRANTIES OF TITLE, MERCHANTABILITY,
 * NONINFRINGEMENT, FITNESS FOR A PARTICULAR PURPOSE, LACK OF VIRUSES,
 * ACCURACY OR COMPLETENESS, QUIET ENJOYMENT, QUIET POSSESSION OR
 * CORRESPONDENCE TO DESCRIPTION. YOU ASSUME THE ENTIRE RISK ARISING
 * OUT OF USE OR PERFORMANCE OF THE SOFTWARE.
 * 3.     TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT SHALL
 * BROADCOM OR ITS LICENSORS BE LIABLE FOR   CONSEQUENTIAL,
 * INCIDENTAL, SPECIAL, INDIRECT, OR EXEMPLARY DAMAGES WHATSOEVER
 * ARISING OUT OF OR IN ANY WAY RELATING TO YOUR USE OF OR INABILITY
 * TO USE THE SOFTWARE EVEN IF BROADCOM HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGES; OR (ii) ANY AMOUNT IN EXCESS OF
 * THE AMOUNT ACTUALLY PAID FOR THE SOFTWARE ITSELF OR USD 1.00,
 * WHICHEVER IS GREATER. THESE LIMITATIONS SHALL APPLY NOTWITHSTANDING
 * ANY FAILURE OF ESSENTIAL PURPOSE OF ANY LIMITED REMEDY.$
 * 
 *
 * @brief   this file includes cw port params.
 *
 * @section
 * 
 */

/* NOTE: This file is chip dependent */

#include "type_defns.h" 
#include "common_def.h"
#include "chip_mode_def.h"
#include "capi_def.h"



#ifdef __cplusplus
extern "C" {
extern
#endif

const cw_port_fec_mode_t port_fec_mode_array[CAPI_FORWARD_ERROR_CORR_MODE_MAX] = {
    {CHIP_FEC_TYPE_NONE,   CHIP_TERM_BYPASS,             CHIP_FEC_TYPE_NONE},       /**< HOST_NONE_TERM_BYPASS_MEDIA_NONE = 0              */
    {CHIP_FEC_TYPE_RS544,  CHIP_TERM_DEC_FWD,            CHIP_FEC_TYPE_RS544},      /**< HOST_RS544_TERM_DEC_FWD_MEDIA_RS544 = 1             */    
    {CHIP_FEC_TYPE_RS544,  CHIP_TERM_DEC_ENC,            CHIP_FEC_TYPE_RS544},      /**< HOST_RS544_TERM_DEC_ENC_MEDIA_RS544 = 2             */    
    {CHIP_FEC_TYPE_RS544,  CHIP_TERM_DEC_XDEC_XENC_ENC,  CHIP_FEC_TYPE_RS544},      /**< HOST_RS544_TERM_XDEC_XENC_MEDIA_RS544 = 3             */    
    {CHIP_FEC_TYPE_RS528,  CHIP_TERM_DEC_FWD,            CHIP_FEC_TYPE_RS528},      /**< HOST_RS528_TERM_DEC_FWD_MEDIA_RS528 = 4              */    
    {CHIP_FEC_TYPE_RS528,  CHIP_TERM_DEC_ENC,            CHIP_FEC_TYPE_RS528},      /**< HOST_RS528_TERM_DEC_ENC_MEDIA_RS528 = 5             */    
    {CHIP_FEC_TYPE_RS528,  CHIP_TERM_DEC_XDEC_XENC_ENC,  CHIP_FEC_TYPE_RS528},      /**< HOST_RS528_TERM_XDEC_XENC_MEDIA_RS528 = 6             */    
    {CHIP_FEC_TYPE_RS528,  CHIP_TERM_DEC_ENC,            CHIP_FEC_TYPE_RS544},      /**< HOST_RS528_TERM_DEC_ENC_MEDIA_RS544 = 7             */    
    {CHIP_FEC_TYPE_RS528,  CHIP_TERM_DEC_XDEC_XENC_ENC,  CHIP_FEC_TYPE_RS544},      /**< HOST_RS528_TERM_XDEC_XENC_MEDIA_RS544 = 8             */    
    {CHIP_FEC_TYPE_RS544,  CHIP_TERM_DEC_ENC,            CHIP_FEC_TYPE_RS528},      /**< HOST_RS544_TERM_DEC_ENC_MEDIA_RS528 = 9             */    
    {CHIP_FEC_TYPE_RS544,  CHIP_TERM_DEC_XDEC_XENC_ENC,  CHIP_FEC_TYPE_RS528},      /**< HOST_RS544_TERM_XDEC_XENC_MEDIA_RS528 = 10             */    
    {CHIP_FEC_TYPE_PCS,    CHIP_TERM_PCS_XENC,           CHIP_FEC_TYPE_RS528},      /**< HOST_PCS_TERM_PCS_XENC_MEDIA_RS528 = 11             */    
    {CHIP_FEC_TYPE_PCS,    CHIP_TERM_PCS_XENC,           CHIP_FEC_TYPE_RS544},      /**< HOST_PCS_TERM_PCS_XENC_MEDIA_RS544 = 12             */    
};

const cw_port_entry_t ports[] = {
    {CHIP_MODE_1X106G_KP4PAM_TO_1X106G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_1X53G_KP4PAM_TO_1X53G_KP4PAM, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_1X51G_KR4PAM_TO_1X51G_KR4PAM, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_1x25G_KR4NRZ_TO_1x25G_KR4NRZ, CHIP_MODE_25G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_1X26G_KP4NRZ_TO_1X26G_KP4NRZ, CHIP_MODE_25G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8x25G_KR4NRZ_TO_2x103G_KR4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_103_13,   CHIP_MOD_PAM4,CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_8X106G_KP4PAM_TO_8X106G_KP4PAM, CHIP_MODE_800G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X106G_KP4PAM_TO_4X106G_KP4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X106G_KP4PAM_TO_2X106G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X53G_KP4PAM_TO_8X53G_KP4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X53G_KP4PAM_TO_4X53G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X53G_KP4PAM_TO_2X53G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X53G_KP4PAM_TO_1X106G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X53G_KP4PAM_TO_2X106G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X25G_KR4NRZ_TO_1X51G_KR4PAM, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4,CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_4X25G_KR4NRZ_TO_1x103G_KR4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_103_13,   CHIP_MOD_PAM4,CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_8x25G_KR4NRZ_TO_8x25G_KR4NRZ, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_8X26G_KP4NRZ_TO_8X26G_KP4NRZ, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X53G_KP4PAM_TO_4X106G_KP4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X26G_KP4NRZ_TO_4X53G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,  CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4x25G_NRZ_TO_2X53G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X25G_NRZ_TO_1x106G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8x25G_NRZ_TO_4X53G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X51G_KR4PAM_TO_2X51G_KR4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_4X51G_KR4PAM_TO_4X51G_KR4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_8X51G_KR4PAM_TO_8X51G_KR4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_2x25G_NRZ_TO_1X53G_KP4PAM, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X51G_KR4PAM_TO_4X103G_KR4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_103_13,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_1X53G_NRZ_TO_1X53G_NRZ, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_NRZ, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_NRZ, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X26G_KP4NRZ_TO_2X106G_KP4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,  CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X26G_KP4NRZ_TO_2X26G_KP4NRZ, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X26G_KP4NRZ_TO_4X26G_KP4NRZ, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_26_5625,   CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X26G_KP4NRZ_TO_2X53G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,  CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X26G_KP4NRZ_TO_1X53G_KP4PAM, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,  CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_2X51G_KR4PAM_TO_1X103G_KR4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_103_13,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_4X51G_KR4PAM_TO_2X103G_KR4PAM, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_51_565,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_103_13,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_2X25G_KR4NRZ_TO_2X25G_KR4NRZ, CHIP_MODE_50G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_4x25G_KR4NRZ_TO_4x25G_KR4NRZ, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00},
    {CW_LW_BR_25_78125, CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_165, CW_LW_VCO_51P563, 0x00}},

    {CHIP_MODE_4X26G_KP4NRZ_TO_1X106G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_26_5625,  CHIP_MOD_NRZ, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_4X106G_KP4PAM_TO_8X53G_KP4PAM, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_1X106G_KP4PAM_TO_2X53G_KP4PAM, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},

    {CHIP_MODE_8X53G_KP4NRZ_TO_8X53G_KP4NRZ, CHIP_MODE_400G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125, CHIP_MOD_NRZ, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_53_125, CHIP_MOD_NRZ, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},  

    {CHIP_MODE_2X53G_KP4PAM_TO_1X106G_KP4PAM_M1, CHIP_MODE_100G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},    

    {CHIP_MODE_4X53G_KP4PAM_TO_2X106G_KP4PAM_M1, CHIP_MODE_200G, CHIP_PORT_FEC_TERM_BYPASS, CHIP_PORT_FEC_TERM_BYPASS, CHIP_HOST_FEC_TYPE_NA, CHIP_LINE_FEC_TYPE_NA,
    {CW_LW_BR_53_125,   CHIP_MOD_PAM4, CW_LW_OSR_2, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00},
    {CW_LW_BR_106_25,   CHIP_MOD_PAM4, CW_LW_OSR_1, CW_LW_PLL_MULTIPLIER_170, CW_LW_VCO_53P125, 0x00}},    
};


/*CW chip mode port_mask and lane_mask definition*/
const cw_chip_port_info_t cw_cmode[] = {
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1X106G_KP4PAM_TO_1X106G_KP4PAM */
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1X53G_KP4PAM_TO_1X53G_KP4PAM */
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1X51G_KR4PAM_TO_1X51G_KR4PAM */
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1x25G_KR4NRZ_TO_1x25G_KR4NRZ */
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1X26G_KP4NRZ_TO_1X26G_KP4NRZ */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8x25G_KR4NRZ_TO_2x103G_KR4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X106G_KP4PAM_TO_8X106G_KP4PAM */
    {0x11, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X106G_KP4PAM_TO_4X106G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X106G_KP4PAM_TO_2X106G_KP4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X53G_KP4PAM_TO_8X53G_KP4PAM */
    {0x11, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X53G_KP4PAM_TO_4X53G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X53G_KP4PAM_TO_2X53G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, 0x02FF, 0x010F},/* CHIP_MODE_2X53G_KP4PAM_TO_1X106G_KP4PAM */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x03, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00}, 0x04FF, 0x020F},/* CHIP_MODE_4X53G_KP4PAM_TO_2X106G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X25G_KR4NRZ_TO_1X51G_KR4PAM */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x01, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X25G_KR4NRZ_TO_1x103G_KR4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8x25G_KR4NRZ_TO_8x25G_KR4NRZ */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X26G_KP4NRZ_TO_8X26G_KP4NRZ */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X53G_KP4PAM_TO_4X106G_KP4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X26G_KP4NRZ_TO_4X53G_KP4PAM */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x03, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4x25G_NRZ_TO_2X53G_KP4PAM */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x01, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X25G_NRZ_TO_1x106G_KP4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8x25G_NRZ_TO_4X53G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X51G_KR4PAM_TO_2X51G_KR4PAM */
    {0x11, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X51G_KR4PAM_TO_4X51G_KR4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X51G_KR4PAM_TO_8X51G_KR4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2x25G_NRZ_TO_1X53G_KP4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X51G_KR4PAM_TO_4X103G_KR4PAM */    
    {0xFF, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, 0x00FF, 0x00FF},/* CHIP_MODE_1X53G_NRZ_TO_1X53G_NRZ */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X26G_KP4NRZ_TO_2X106G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X26G_KP4NRZ_TO_2X26G_KP4NRZ */
    {0x11, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X26G_KP4NRZ_TO_4X26G_KP4NRZ */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x03, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X26G_KP4NRZ_TO_2X53G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X26G_KP4NRZ_TO_1X53G_KP4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X51G_KR4PAM_TO_1X103G_KR4PAM */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x03, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X51G_KR4PAM_TO_2X103G_KR4PAM */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_2X25G_KR4NRZ_TO_2X25G_KR4NRZ */
    {0x11, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, {0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4x25G_KR4NRZ_TO_4x25G_KR4NRZ */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x01, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X26G_KP4NRZ_TO_1X106G_KP4PAM */
    {0x01, {0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_4X106G_KP4PAM_TO_8X53G_KP4PAM */
    {0x55, {0x01, 0x00, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00}, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_1X106G_KP4PAM_TO_2X53G_KP4PAM */
    {0x01, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, {0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, 0x00FF, 0x00FF},/* CHIP_MODE_8X53G_KP4NRZ_TO_8X53G_KP4NRZ */
    {0x55, {0x03, 0x00, 0x0C, 0x00, 0x30, 0x00, 0xC0, 0x00}, {0x01, 0x00, 0x02, 0x00, 0x10, 0x00, 0x20, 0x00}, 0x02FF, 0x0133},/* CHIP_MODE_2X53G_KP4PAM_TO_1X106G_KP4PAM_M1 */
    {0x11, {0x0f, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00}, {0x03, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00}, 0x04FF, 0x0233},/* CHIP_MODE_4X53G_KP4PAM_TO_2X106G_KP4PAM_M1 */
    
};



uint8_t port_total_entries = sizeof(ports)/sizeof(cw_port_entry_t);

