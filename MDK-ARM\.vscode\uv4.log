*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\stm32\ARM\ARMCC\Bin'
Build target 'D00094A_FW_VA'
compiling stm32f2xx_it.c...
compiling gpio.c...
compiling stm32f2xx_hal_msp.c...
compiling usb_device.c...
compiling usbd_desc.c...
compiling usbd_conf.c...
compiling main.c...
../Src/main.c(377): warning:  #177-D: variable "j"  was declared but never referenced
    uint8_t i,j;
../Src/main.c(378): warning:  #177-D: variable "adc_swap"  was declared but never referenced
    uint32_t ADC_Sum = 0,adc_swap;
../Src/main.c(379): warning:  #177-D: variable "adc_value"  was declared but never referenced
  	uint16_t adc_value[16];
../Src/main.c(452): warning:  #550-D: variable "slope"  was set but never used
  	unsigned int slope = 0,diffVal = 0;
../Src/main.c(782): warning:  #550-D: variable "sn"  was set but never used
  			char sn[16]={32};
../Src/main.c: 5 warnings, 0 errors
compiling rng.c...
compiling mdio.c...
compiling dac.c...
compiling master_i2c.c...
..\Src\master_i2c.c(123): warning:  #188-D: enumerated type mixed with another type
  		SDA(byte & 0x80);
..\Src\master_i2c.c: 1 warning, 0 errors
compiling usbd_cdc_if.c...
compiling dma.c...
compiling bcm87800.c...
..\Src\bcm87800.c(942): warning:  #550-D: variable "chip_id_lsb"  was set but never used
  	uint32_t chip_id_lsb ,chip_id_msb;
..\Src\bcm87800.c(942): warning:  #550-D: variable "chip_id_msb"  was set but never used
  	uint32_t chip_id_lsb ,chip_id_msb;
..\Src\bcm87800.c(1120): warning:  #550-D: variable "clkref"  was set but never used
  	float clkref = 0,data_rate;
..\Src\bcm87800.c: 3 warnings, 0 errors
compiling master_i2c_pm.c...
compiling stm32f2xx_hal_pcd_ex.c...
compiling stm32f2xx_hal_pcd.c...
compiling adc.c...
compiling usart.c...
compiling stm32f2xx_hal_adc.c...
compiling stm32f2xx_ll_usb.c...
compiling stm32f2xx_hal.c...
compiling stm32f2xx_hal_tim.c...
compiling stm32f2xx_hal_spi.c...
compiling stm32f2xx_hal_rcc.c...
compiling stm32f2xx_hal_adc_ex.c...
compiling stm32f2xx_hal_tim_ex.c...
compiling stm32f2xx_hal_i2c.c...
compiling stm32f2xx_hal_gpio.c...
compiling stm32f2xx_hal_cortex.c...
compiling stm32f2xx_hal_dma.c...
compiling stm32f2xx_hal_dma_ex.c...
compiling stm32f2xx_hal_rcc_ex.c...
compiling stm32f2xx_hal_flash_ex.c...
compiling stm32f2xx_hal_flash.c...
compiling system_stm32f2xx.c...
compiling stm32f2xx_hal_rng.c...
compiling stm32f2xx_hal_dac_ex.c...
compiling stm32f2xx_hal_dac.c...
compiling stm32f2xx_hal_uart.c...
compiling usbd_ctlreq.c...
assembling startup_stm32f205xx.s...
compiling usbd_core.c...
compiling capi_custom.c...
compiling capi_test.c...
..\api\capi\src\capi_test.c(200): warning:  #188-D: enumerated type mixed with another type
         return host_fec_clear_prbs_status(phy_info_ptr, prbs_st_ptr->prbs_type);
..\api\capi\src\capi_test.c(336): warning:  #111-D: statement is unreachable
              break;
..\api\capi\src\capi_test.c: 2 warnings, 0 errors
compiling capi_diag.c...
compiling host_avs.c...
compiling capi.c...
..\api\capi\src\capi.c(1390): warning:  #188-D: enumerated type mixed with another type
      return_result_t ret =0;
..\api\capi\src\capi.c(1665): warning:  #177-D: variable "capi_phy"  was declared but never referenced
      capi_phy_info_t capi_phy;
..\api\capi\src\capi.c: 2 warnings, 0 errors
compiling host_chip_wrapper.c...
compiling host_gpio_util.c...
compiling host_diag_util.c...
compiling host_diag.c...
compiling host_fec_prbs.c...
..\api\capi\src\host_fec_prbs.c(772): warning:  #188-D: enumerated type mixed with another type
      host_diag_cw_kp4_gen_port_cfg(config_info.func_mode, port_info.port_idx, &port_cfg);
..\api\capi\src\host_fec_prbs.c(826): warning:  #188-D: enumerated type mixed with another type
      host_diag_cw_kp4_gen_port_cfg(config_info.func_mode, port_info.port_idx, &port_cfg);
..\api\capi\src\host_fec_prbs.c(892): warning:  #188-D: enumerated type mixed with another type
      host_diag_cw_kp4_gen_port_cfg(config_info.func_mode, port_info.port_idx, &port_cfg);
..\api\capi\src\host_fec_prbs.c(955): warning:  #188-D: enumerated type mixed with another type
      host_diag_cw_kp4_gen_port_cfg(config_info.func_mode, port_info.port_idx, &port_cfg);
..\api\capi\src\host_fec_prbs.c: 4 warnings, 0 errors
compiling host_diag_fec_statistics.c...
..\api\capi\src\host_diag_fec_statistics.c(1496): warning:  #188-D: enumerated type mixed with another type
          ERR_FUNC(host_diag_cw_rptr_fec_stats_mon_config_handler(phy_info_ptr, capi_fec_dump_status_ptr, 1));
..\api\capi\src\host_diag_fec_statistics.c(1559): warning:  #188-D: enumerated type mixed with another type
          ERR_FUNC(host_diag_cw_rptr_fec_stats_mon_config_handler(phy_info_ptr, capi_fec_dump_status_ptr, 0));
..\api\capi\src\host_diag_fec_statistics.c: 2 warnings, 0 errors
compiling host_log_util.c...
..\api\capi\src\host_log_util.c(68): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_ALL)
..\api\capi\src\host_log_util.c(69): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_TXPI_OVERRIDE)
..\api\capi\src\host_log_util.c(70): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_LOW_POWER_MODE)
..\api\capi\src\host_log_util.c(71): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_LOW_POWER_MODE)
..\api\capi\src\host_log_util.c(72): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_POLARITY)
..\api\capi\src\host_log_util.c(73): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_POLARITY)
..\api\capi\src\host_log_util.c(74): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_LANE_CTRL_INFO)
..\api\capi\src\host_log_util.c(75): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_LANE_CTRL_INFO)
..\api\capi\src\host_log_util.c(76): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_CONFIG_INFO)
..\api\capi\src\host_log_util.c(77): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_CONFIG_INFO)
..\api\capi\src\host_log_util.c(78): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_LOOPBACK_INFO)
..\api\capi\src\host_log_util.c(79): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_LOOPBACK_INFO)
..\api\capi\src\host_log_util.c(80): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_PRBS_INFO)
..\api\capi\src\host_log_util.c(81): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_PRBS_INFO)
..\api\capi\src\host_log_util.c(82): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_CLEAR_PRBS_STATUS)
..\api\capi\src\host_log_util.c(83): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_PRBS_STATUS)
..\api\capi\src\host_log_util.c(84): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_INJ_PRBS_ERROR)
..\api\capi\src\host_log_util.c(85): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_DIAG_LANE_STATUS)
..\api\capi\src\host_log_util.c(86): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_LANE_CONFIG_INFO)
..\api\capi\src\host_log_util.c(87): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_LANE_CONFIG_INFO)
..\api\capi\src\host_log_util.c(88): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_LANE_INFO)
..\api\capi\src\host_log_util.c(89): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_ARCHIVE_INFO)
..\api\capi\src\host_log_util.c(90): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_ARCHIVE_INFO)
..\api\capi\src\host_log_util.c(91): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_CMIS_INFO)
..\api\capi\src\host_log_util.c(92): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_CLIENT_SIDE)
..\api\capi\src\host_log_util.c(93): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_CLIENT_TX_DRIVER_VOLTAGE)
..\api\capi\src\host_log_util.c(94): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_LW_SIDE)
..\api\capi\src\host_log_util.c(95): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_CW_SIDE)
..\api\capi\src\host_log_util.c(96): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_CW_RPTR_FEC_MON_CONFIG)
..\api\capi\src\host_log_util.c(97): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_RECOVERED_CLOCK_INFO)
..\api\capi\src\host_log_util.c(98): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_RECOVERED_CLOCK_INFO)
..\api\capi\src\host_log_util.c(99): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_SET_GPIO_INFO)
..\api\capi\src\host_log_util.c(100): warning:  #111-D: statement is unreachable
      ENUM_TO_STR_CASE(COMMAND_ID_GET_GPIO_INFO)
..\api\capi\src\host_log_util.c: 33 warnings, 0 errors
compiling host_test.c...
compiling host_power_util.c...
..\api\capi\src\host_power_util.c(961): warning:  #1-D: last line of file ends without a newline
  }
..\api\capi\src\host_power_util.c: 1 warning, 0 errors
compiling host_lw_wrapper.c...
compiling host_to_chip_ipc.c...
compiling hw_mutex_handler.c...
compiling chal_cw_prbs.c...
compiling chal_cw_rtmr_status_check.c...
compiling chal_cw_rtmr_kp4prbs.c...
compiling chal_cw_rtmr_clkrst_control.c...
compiling chal_cw_rtmr_clockrst_mux.c...
compiling chal_cw_top.c...
compiling chal_cw_rtmr_xdec.c...
compiling chal_cw_rtmr_xenc.c...
compiling chal_cw_utils.c...
compiling chal_gpio.c...
compiling chip_config.c...
compiling dsp_config.c...
compiling dsp_utils.c...
compiling host_chip_mem_map.c...
compiling ml_cw_rtmr_handler.c...
compiling ml_cw_rtmr_modes.c...
compiling chal_cw_rtmr_datapath_cfg.c...
compiling common_util.c...
compiling usbd_ioreq.c...
compiling ml_cw_xbar.c...
compiling usbd_cdc.c...
compiling hr_time.c...
linking...
Program Size: Code=161940 RO-data=5940 RW-data=724 ZI-data=17476  
FromELF: creating hex file...
".\DEBX-08112A-VA\D00094A_FW_VA.axf" - 0 Error(s), 53 Warning(s).
Build Time Elapsed:  00:00:36
