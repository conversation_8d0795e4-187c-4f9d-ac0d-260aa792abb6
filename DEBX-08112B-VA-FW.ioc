#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_1
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_TEMPSENSOR
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,master,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,NbrOfConversion
ADC1.NbrOfConversion=3
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.RequestsNb=1
File.Version=6
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.Family=STM32F2
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP2=DMA
Mcu.IP3=I2C1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SPI1
Mcu.IP7=SYS
Mcu.IP8=USB_DEVICE
Mcu.IP9=USB_OTG_FS
Mcu.IPNb=10
Mcu.Name=STM32F205R(B-C-E-F-G)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PB12
Mcu.Pin11=PB13
Mcu.Pin12=PB14
Mcu.Pin13=PB15
Mcu.Pin14=PC6
Mcu.Pin15=PC7
Mcu.Pin16=PC8
Mcu.Pin17=PC9
Mcu.Pin18=PA9
Mcu.Pin19=PA11
Mcu.Pin2=PC1
Mcu.Pin20=PA12
Mcu.Pin21=PA13
Mcu.Pin22=PA14
Mcu.Pin23=PB3
Mcu.Pin24=PB4
Mcu.Pin25=PB5
Mcu.Pin26=PB6
Mcu.Pin27=PB7
Mcu.Pin28=PB8
Mcu.Pin29=PB9
Mcu.Pin3=PC2
Mcu.Pin30=VP_ADC1_TempSens_Input
Mcu.Pin31=VP_SYS_VS_Systick
Mcu.Pin32=VP_USB_DEVICE_VS_USB_DEVICE_CDC_FS
Mcu.Pin4=PC3
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA4
Mcu.Pin8=PA5
Mcu.Pin9=PC5
Mcu.PinsNb=33
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F205RGTx
MxCube.Version=5.2.1
MxDb.Version=DB.5.0.21
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.OTG_FS_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=VADC1
PA1.Signal=ADCx_IN1
PA11.Mode=Device_Only
PA11.Signal=USB_OTG_FS_DM
PA12.Mode=Device_Only
PA12.Signal=USB_OTG_FS_DP
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.GPIOParameters=GPIO_Label
PA2.GPIO_Label=VADC2
PA2.Signal=ADCx_IN2
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=VSET1
PA4.Signal=COMP_DAC1_group
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=VSET2
PA5.Signal=COMP_DAC2_group
PA9.Mode=Activate_VBUS
PA9.Signal=USB_OTG_FS_VBUS
PB12.GPIOParameters=PinState,GPIO_Label,GPIO_ModeDefaultOutputPP
PB12.GPIO_Label=ResetL1
PB12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB12.Locked=true
PB12.PinState=GPIO_PIN_SET
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=IntL1
PB13.Locked=true
PB13.Signal=GPIO_Input
PB14.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PB14.GPIO_Label=ModPrsL1
PB14.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PB15.GPIO_Label=LPMode1
PB15.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=SCK
PB3.Mode=Full_Duplex_Master
PB3.Signal=SPI1_SCK
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=MISO
PB4.Mode=Full_Duplex_Master
PB4.Signal=SPI1_MISO
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=MOSI
PB5.Mode=Full_Duplex_Master
PB5.Signal=SPI1_MOSI
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=SCL
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=SDA
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.GPIOParameters=PinState,GPIO_Label,GPIO_ModeDefaultOutputPP
PB8.GPIO_Label=SCL2
PB8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB8.Locked=true
PB8.PinState=GPIO_PIN_SET
PB8.Signal=GPIO_Output
PB9.GPIOParameters=PinState,GPIO_Label,GPIO_ModeDefaultOutputPP
PB9.GPIO_Label=SDA2
PB9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PB9.Locked=true
PB9.PinState=GPIO_PIN_SET
PB9.Signal=GPIO_Output
PC1.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PC1.GPIO_Label=LINK
PC1.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC1.Locked=true
PC1.Signal=GPIO_Output
PC2.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PC2.GPIO_Label=PM1
PC2.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PC2.Locked=true
PC2.Signal=GPIO_Output
PC3.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PC3.GPIO_Label=PM2
PC3.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PC3.Locked=true
PC3.Signal=GPIO_Output
PC5.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PC5.GPIO_Label=RESET_DSP
PC5.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.GPIOParameters=PinState,GPIO_Label,GPIO_ModeDefaultOutputPP
PC6.GPIO_Label=ResetL2
PC6.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC6.Locked=true
PC6.PinState=GPIO_PIN_SET
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Label
PC7.GPIO_Label=IntL2
PC7.Locked=true
PC7.Signal=GPIO_Input
PC8.GPIOParameters=GPIO_Label,GPIO_ModeDefaultOutputPP
PC8.GPIO_Label=ModPrsL2
PC8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC8.Locked=true
PC8.Signal=GPIO_Output
PC9.GPIOParameters=GPIO_Label
PC9.GPIO_Label=LPMode2
PC9.Locked=true
PC9.Signal=GPIO_Output
PCC.Checker=false
PCC.Line=STM32F2x5
PCC.MCU=STM32F205R(B-C-E-F-G)Tx
PCC.PartNumber=STM32F205RGTx
PCC.Seq0=0
PCC.Series=STM32F2
PCC.Temperature=25
PCC.Vdd=3.3
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F205RGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F2 V1.7.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x800
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=DEBX-08112B-VA-FW.ioc
ProjectManager.ProjectName=DEBX-08112B-VA-FW
ProjectManager.StackSize=0x1000
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-MX_DMA_Init-DMA-false-HAL-true,3-SystemClock_Config-RCC-false-HAL-false,4-MX_ADC1_Init-ADC1-false-HAL-true,5-MX_DAC_Init-DAC-false-HAL-true,6-MX_I2C1_Init-I2C1-false-HAL-true,7-MX_SPI1_Init-SPI1-false-HAL-true,8-MX_USB_DEVICE_Init-USB_DEVICE-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=120000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=30000000
RCC.APB1TimFreq_Value=60000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=60000000
RCC.APB2TimFreq_Value=120000000
RCC.CortexFreq_Value=120000000
RCC.EthernetFreq_Value=120000000
RCC.FCLKCortexFreq_Value=120000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=120000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=120000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLQ,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=120000000
RCC.PLLCLKFreq_Value=120000000
RCC.PLLM=20
RCC.PLLQ=5
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=120000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=240000000
RCC.VCOInputFreq_Value=1250000
RCC.VCOOutputFreq_Value=240000000
RCC.VcooutputI2S=120000000
SH.ADCx_IN1.0=ADC1_IN1,IN1
SH.ADCx_IN1.ConfNb=1
SH.ADCx_IN2.0=ADC1_IN2,IN2
SH.ADCx_IN2.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SH.COMP_DAC2_group.0=DAC_OUT2,DAC_OUT2
SH.COMP_DAC2_group.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI1.CalculateBaudRate=30.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
USB_DEVICE.CLASS_NAME_FS=CDC
USB_DEVICE.IPParameters=VirtualMode-CDC_FS,VirtualModeFS,CLASS_NAME_FS
USB_DEVICE.VirtualMode-CDC_FS=Cdc
USB_DEVICE.VirtualModeFS=Cdc_FS
USB_OTG_FS.IPParameters=VirtualMode
USB_OTG_FS.VirtualMode=Device_Only
VP_ADC1_TempSens_Input.Mode=IN-TempSens
VP_ADC1_TempSens_Input.Signal=ADC1_TempSens_Input
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_USB_DEVICE_VS_USB_DEVICE_CDC_FS.Mode=CDC_FS
VP_USB_DEVICE_VS_USB_DEVICE_CDC_FS.Signal=USB_DEVICE_VS_USB_DEVICE_CDC_FS
board=custom
