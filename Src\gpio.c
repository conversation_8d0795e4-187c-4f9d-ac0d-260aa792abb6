/**
  ******************************************************************************
  * File Name          : gpio.c
  * Description        : This file provides code for the configuration
  *                      of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as 
        * Analog 
        * Input 
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOH_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();
	__HAL_RCC_GPIOE_CLK_ENABLE();

	
    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOC, LINK_Pin|SPICS_Pin|PM_Pin, GPIO_PIN_SET);
	
    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOB,LPMode_Pin|ResetL_Pin, GPIO_PIN_SET);//OSFP
		//HAL_GPIO_WritePin(GPIOB,LPMode_Pin, GPIO_PIN_RESET);//QDD
		//HAL_GPIO_WritePin(GPIOB,ResetL_Pin, GPIO_PIN_SET);  //QDD
	
    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(GPIOB,SPIRST_Pin, GPIO_PIN_SET);
  
	/*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(SCL_GPIO_Port, SCL_Pin|SDA_Pin, GPIO_PIN_SET);		
		HAL_GPIO_WritePin(SCLSI_GPIO_Port, SCLSI_Pin|SDASI_Pin, GPIO_PIN_SET);

    /*Configure GPIO pin Output Level */
    HAL_GPIO_WritePin(RESET_DSP_GPIO_Port, RESET_DSP_Pin, GPIO_PIN_RESET);
	
    /*Configure GPIO pins : PCPin PCPin PCPin PCPin */
    GPIO_InitStruct.Pin = LINK_Pin|SPICS_Pin|PM_Pin|RESET_DSP_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
	
	/*Configure GPIO pins : PCPin PCPin PCPin PCPin */
    GPIO_InitStruct.Pin = ResetL_Pin|LPMode_Pin|SPIRST_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

	/*Configure GPIO pin : PtPin */
    GPIO_InitStruct.Pin = ModPrsL_Pin|IntL_Pin|ModSEL_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
	/**I2C1 GPIO Configuration    
	PB6     ------> I2C1_SCL
	PB7     ------> I2C1_SDA 
	*/
    GPIO_InitStruct.Pin = SCL_Pin|SDA_Pin|SCLSI_Pin|SDASI_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(SCL_GPIO_Port, &GPIO_InitStruct);

}

/* USER CODE BEGIN 2 */

/* USER CODE END 2 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
