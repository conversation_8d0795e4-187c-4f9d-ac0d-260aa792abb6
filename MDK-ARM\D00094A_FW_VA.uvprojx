<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>D00094A_FW_VA</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F205RGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F2xx_DFP.2.9.0</PackID>
          <PackURL>http://www.keil.com/pack</PackURL>
          <Cpu>IRAM(0x20000000-0x2001FFFF) IROM(0x8000000-0x80FFFFF)  CLOCK(25000000) CPUTYPE("Cortex-M3")</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId></DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F205RGTx$CMSIS\SVD\STM32F20x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\DEBX-08112A-VA\</OutputDirectory>
          <OutputName>D00094A_FW_VA</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\DEBX-08112A-VA\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>-MPU -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>6</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_HAL_DRIVER,STM32F205xx</Define>
              <Undefine></Undefine>
              <IncludePath>../Inc;../Drivers/STM32F2xx_HAL_Driver/Inc;../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy;../Middlewares/ST/STM32_USB_Device_Library/Core/Inc;../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc;../Drivers/CMSIS/Device/ST/STM32F2xx/Include;../Drivers/CMSIS/Include;..\api;..\api\capi\inc;..\api\chip\dep\chal\inc;..\api\chip\dep\common\inc;..\api\chip\dep\ml\inc;..\api\chip\indep\inc;..\api\platform\reg_access\inc;..\api\platform\utils\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/main.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/gpio.c</FilePath>
            </File>
            <File>
              <FileName>usb_device.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/usb_device.c</FilePath>
            </File>
            <File>
              <FileName>usbd_conf.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/usbd_conf.c</FilePath>
            </File>
            <File>
              <FileName>usbd_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/usbd_desc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/stm32f2xx_it.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Src/stm32f2xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>master_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\master_i2c.c</FilePath>
            </File>
            <File>
              <FileName>mdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\mdio.c</FilePath>
            </File>
            <File>
              <FileName>usbd_cdc_if.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\usbd_cdc_if.c</FilePath>
            </File>
            <File>
              <FileName>bcm87800.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\bcm87800.c</FilePath>
            </File>
            <File>
              <FileName>rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\rng.c</FilePath>
            </File>
            <File>
              <FileName>dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\dac.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\dma.c</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\adc.c</FilePath>
            </File>
            <File>
              <FileName>master_i2c_pm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\master_i2c_pm.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Src\usart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/STM32F2xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32f2xx_hal_pcd_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_pcd_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_pcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_pcd.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_ll_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_ll_usb.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32f2xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_uart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32f2xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..//Src/system_stm32f2xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Middlewares/USB_Device_Library</GroupName>
          <Files>
            <File>
              <FileName>usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c</FilePath>
            </File>
            <File>
              <FileName>usbd_ctlreq.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c</FilePath>
            </File>
            <File>
              <FileName>usbd_ioreq.c</FileName>
              <FileType>1</FileType>
              <FilePath>../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c</FilePath>
            </File>
            <File>
              <FileName>usbd_cdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Middlewares\ST\STM32_USB_Device_Library\Class\CDC\Src\usbd_cdc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f205xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\startup_stm32f205xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>api</GroupName>
          <Files>
            <File>
              <FileName>capi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\capi.c</FilePath>
            </File>
            <File>
              <FileName>capi_custom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\capi_custom.c</FilePath>
            </File>
            <File>
              <FileName>capi_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\capi_diag.c</FilePath>
            </File>
            <File>
              <FileName>capi_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\capi_test.c</FilePath>
            </File>
            <File>
              <FileName>host_avs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_avs.c</FilePath>
            </File>
            <File>
              <FileName>host_chip_wrapper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_chip_wrapper.c</FilePath>
            </File>
            <File>
              <FileName>host_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_diag.c</FilePath>
            </File>
            <File>
              <FileName>host_diag_fec_statistics.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_diag_fec_statistics.c</FilePath>
            </File>
            <File>
              <FileName>host_diag_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_diag_util.c</FilePath>
            </File>
            <File>
              <FileName>host_fec_prbs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_fec_prbs.c</FilePath>
            </File>
            <File>
              <FileName>host_gpio_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_gpio_util.c</FilePath>
            </File>
            <File>
              <FileName>host_log_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_log_util.c</FilePath>
            </File>
            <File>
              <FileName>host_lw_wrapper.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_lw_wrapper.c</FilePath>
            </File>
            <File>
              <FileName>host_power_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_power_util.c</FilePath>
            </File>
            <File>
              <FileName>host_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_test.c</FilePath>
            </File>
            <File>
              <FileName>host_to_chip_ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\host_to_chip_ipc.c</FilePath>
            </File>
            <File>
              <FileName>hw_mutex_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\capi\src\hw_mutex_handler.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_prbs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_prbs.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_clkrst_control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_clkrst_control.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_clockrst_mux.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_clockrst_mux.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_datapath_cfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_datapath_cfg.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_kp4prbs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_kp4prbs.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_status_check.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_status_check.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_xdec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_xdec.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_rtmr_xenc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_rtmr_xenc.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_top.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_top.c</FilePath>
            </File>
            <File>
              <FileName>chal_cw_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_cw_utils.c</FilePath>
            </File>
            <File>
              <FileName>chal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\chal\src\chal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>chip_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\common\src\chip_config.c</FilePath>
            </File>
            <File>
              <FileName>dsp_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\common\src\dsp_config.c</FilePath>
            </File>
            <File>
              <FileName>dsp_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\common\src\dsp_utils.c</FilePath>
            </File>
            <File>
              <FileName>host_chip_mem_map.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\common\src\host_chip_mem_map.c</FilePath>
            </File>
            <File>
              <FileName>ml_cw_rtmr_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\ml\src\ml_cw_rtmr_handler.c</FilePath>
            </File>
            <File>
              <FileName>ml_cw_rtmr_modes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\ml\src\ml_cw_rtmr_modes.c</FilePath>
            </File>
            <File>
              <FileName>ml_cw_xbar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\dep\ml\src\ml_cw_xbar.c</FilePath>
            </File>
            <File>
              <FileName>common_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\chip\indep\src\common_util.c</FilePath>
            </File>
            <File>
              <FileName>hr_time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\api\platform\utils\src\hr_time.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
