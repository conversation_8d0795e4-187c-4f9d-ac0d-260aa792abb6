# 光模块插拔检测功能说明

## 功能概述

本功能为DEBX-08112A-VA高速光模块测试设备添加了光模块插拔次数检测和统计功能，能够实时监控光模块的插入和拔出操作，并提供详细的统计信息。

## 主要特性

### 🔍 实时检测
- 基于ModPrsL引脚(GPIO_PIN_14, GPIOB)检测模块在位状态
- 低电平有效：模块插入时为低电平，拔出时为高电平
- 支持可配置的防抖时间，避免误检测

### 📊 统计功能
- **插入次数计数**：记录模块插入的总次数
- **拔出次数计数**：记录模块拔出的总次数  
- **插拔周期统计**：计算完整的插拔周期数
- **在位时间统计**：累计模块在位的总时间(秒)
- **离位时间统计**：累计模块离位的总时间(秒)
- **当前状态显示**：实时显示模块当前在位状态

### 💾 数据持久化
- 所有统计数据自动保存到Flash存储器
- 支持断电保持，重启后数据不丢失
- 每10次状态变化自动保存一次，避免频繁写入

## 配置参数

### 数据结构扩展
在原有的配置结构体中新增以下字段：

```c
typedef struct {
    // ... 原有字段 ...
    
    // 光模块插拔检测相关参数
    uint32_t module_insert_count;   // 模块插入次数计数器
    uint32_t module_remove_count;   // 模块拔出次数计数器  
    uint32_t total_plugin_cycles;   // 总插拔周期数
    uint32_t module_present_time;   // 模块在位时间(秒)
    uint32_t module_absent_time;    // 模块离位时间(秒)
    uint8_t  module_present_state;  // 当前模块在位状态
    uint8_t  module_detect_enable;  // 插拔检测使能
    uint16_t debounce_time_ms;      // 防抖时间(毫秒)
    uint32_t last_state_change_time;// 上次状态变化时间
} Config_TypeDef;
```

### 默认配置
- **检测使能**：默认开启(1)
- **防抖时间**：默认100毫秒
- **所有计数器**：默认清零

## 命令接口

### 1. 插拔检测使能控制
```
SYS:MODULE:PLUGDETECT <value>  // 设置检测使能 (0=禁用, 1=启用)
SYS:MODULE:PLUGDETECT?         // 查询检测使能状态
```

**示例：**
```
SYS:MODULE:PLUGDETECT 1        // 启用插拔检测
SYS:MODULE:PLUGDETECT?         // 返回: 1
```

### 2. 防抖时间设置
```
SYS:MODULE:DEBOUNCE <time_ms>  // 设置防抖时间(毫秒)
SYS:MODULE:DEBOUNCE?           // 查询防抖时间
```

**示例：**
```
SYS:MODULE:DEBOUNCE 200        // 设置防抖时间为200ms
SYS:MODULE:DEBOUNCE?           // 返回: 200
```

### 3. 统计数据查询
```
SYS:MODULE:STATS?              // 读取完整统计数据
```

**返回格式：**
```
<插入次数>,<拔出次数>,<总周期>,<在位时间>,<离位时间>,<当前状态>
```

**示例：**
```
SYS:MODULE:STATS?              // 返回: 25,24,24,3600,120,1
// 解释：插入25次，拔出24次，24个完整周期，在位3600秒，离位120秒，当前在位
```

### 4. 当前状态查询
```
SYS:MODULE:STATE?              // 读取当前模块状态
```

**返回值：**
- `1`：模块在位
- `0`：模块不在位

### 5. 计数器重置
```
SYS:MODULE:RESET 1             // 重置所有插拔计数器
```

## 技术实现

### 核心函数

#### 1. 初始化函数
```c
void Module_PlugDetect_Init(void)
```
- 检查是否首次使用，设置默认参数
- 读取当前模块状态
- 初始化相关变量

#### 2. 检测处理函数
```c
void Module_PlugDetect_Process(void)
```
- 在主循环中调用
- 实现状态检测和防抖处理
- 更新统计数据

#### 3. 状态读取函数
```c
uint8_t Module_Get_Present_State(void)
```
- 读取ModPrsL引脚状态
- 返回模块在位状态(1=在位, 0=不在位)

#### 4. 计数器重置函数
```c
void Module_PlugDetect_Reset_Counters(void)
```
- 重置所有计数器为0
- 立即保存到Flash

#### 5. 数据保存函数
```c
void Module_Update_Statistics(void)
```
- 将统计数据保存到Flash存储器

### 防抖机制
- 使用时间戳进行防抖处理
- 可配置防抖时间(默认100ms)
- 只有状态稳定超过防抖时间才确认变化

### 自动保存策略
- 每10次状态变化自动保存一次
- 手动重置时立即保存
- 避免频繁Flash写入，延长存储器寿命

## 使用场景

### 1. 生产测试
- 监控生产线上模块插拔操作
- 统计测试过程中的插拔次数
- 评估连接器的可靠性

### 2. 可靠性测试
- 长期插拔寿命测试
- 连接器磨损评估
- 故障率统计分析

### 3. 维护监控
- 设备使用频率统计
- 预防性维护计划
- 设备生命周期管理

## 注意事项

### ⚠️ 重要提醒
1. **防抖时间设置**：根据实际使用环境调整防抖时间，避免误检测
2. **Flash写入限制**：避免频繁重置计数器，Flash有写入次数限制
3. **断电保护**：统计数据在断电后会保持，但未保存的数据可能丢失
4. **硬件依赖**：功能依赖ModPrsL引脚的正确连接和配置

### 🔧 故障排除
1. **检测不工作**：检查module_detect_enable是否为1
2. **误检测频繁**：增加debounce_time_ms的值
3. **数据不保存**：检查Flash写入功能是否正常
4. **状态读取错误**：检查ModPrsL引脚硬件连接

## 扩展功能建议

### 未来可能的增强功能
1. **插拔速度检测**：记录插拔操作的时间间隔
2. **异常检测**：检测异常的插拔模式
3. **报警功能**：插拔次数超过阈值时报警
4. **历史记录**：保存详细的插拔时间戳记录
5. **网络上报**：通过网络接口上报统计数据

---

*本功能已集成到DEBX-08112A-VA固件V1.84中，可通过USB CDC或UART接口进行控制和查询。*
