
#include "main.h"
#include "mdio.h"
#include "mdio.h"
/*
  ******************************************************************************
  * @file           : mdio.c
  * @version        : v1.0
  * @brief          : spi to mdio.
  ******************************************************************************
*/
/*
  ******************************************************************************
  * @file           : mdio.c
  * @version        : v1.0
  * @brief          : spi to mdio.
  ******************************************************************************
*/

SPI_HandleTypeDef hspi1;
void SPI_Cmd(SPI_TypeDef* SPIx, FunctionalState NewState);

/* SPI1 init function */
void MX_SPI1_Init(void)
{
  hspi1.Instance = SPI1;
  hspi1.Init.Mode = SPI_MODE_MASTER;
  hspi1.Init.Direction = SPI_DIRECTION_2LINES;
  hspi1.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi1.Init.CLKPolarity = SPI_POLARITY_HIGH;
  hspi1.Init.CLKPhase = SPI_PHASE_2EDGE;
  hspi1.Init.NSS = SPI_NSS_SOFT;
  hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_8;
  hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi1.Init.CRCPolynomial = 10;
  if (HAL_SPI_Init(&hspi1) != HAL_OK)
  {
    Error_Handler();
  }
	SPI_Cmd(hspi1.Instance,ENABLE);
}

void HAL_SPI_MspInit(SPI_HandleTypeDef* spiHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct;
  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspInit 0 */

  /* USER CODE END SPI1_MspInit 0 */
    /* SPI1 clock enable */
    __HAL_RCC_SPI1_CLK_ENABLE();
  
    /**SPI1 GPIO Configuration    
    PA5     ------> SPI1_SCK
    PA6     ------> SPI1_MISO
    PA7     ------> SPI1_MOSI 
    */
    GPIO_InitStruct.Pin = MDC_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_MEDIUM;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
    HAL_GPIO_Init(MDC_GPIO_Port, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = MDIO_Pin|MDIOIN_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_MEDIUM;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
    HAL_GPIO_Init(MDIO_GPIO_Port, &GPIO_InitStruct);

  /* USER CODE BEGIN SPI1_MspInit 1 */

  /* USER CODE END SPI1_MspInit 1 */
  }
}

void HAL_SPI_MspDeInit(SPI_HandleTypeDef* spiHandle)
{

  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspDeInit 0 */

  /* USER CODE END SPI1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI1_CLK_DISABLE();
  
    /**SPI1 GPIO Configuration    
    PA5     ------> SPI1_SCK
    PA6     ------> SPI1_MISO
    PA7     ------> SPI1_MOSI 
    */
    HAL_GPIO_DeInit(GPIOB, MDC_Pin|MDIO_Pin|MDIOIN_Pin);

  /* USER CODE BEGIN SPI1_MspDeInit 1 */

  /* USER CODE END SPI1_MspDeInit 1 */
  }
} 

FlagStatus SPI_I2S_GetFlagStatus(SPI_TypeDef* SPIx, uint16_t SPI_I2S_FLAG)
{
  FlagStatus bitstatus = RESET;
  /* Check the parameters */
  assert_param(IS_SPI_ALL_PERIPH(SPIx));
  assert_param(IS_SPI_I2S_GET_FLAG(SPI_I2S_FLAG));
  
  /* Check the status of the specified SPI flag */
  if ((SPIx->SR & SPI_I2S_FLAG) != (uint16_t)RESET)
  {
    /* SPI_I2S_FLAG is set */
    bitstatus = SET;
  }
  else
  {
    /* SPI_I2S_FLAG is reset */
    bitstatus = RESET;
  }
  /* Return the SPI_I2S_FLAG status */
  return  bitstatus;
}

uint16_t SPI_I2S_ReceiveData(SPI_TypeDef* SPIx)
{
  /* Check the parameters */
  assert_param(IS_SPI_ALL_PERIPH(SPIx));
  
  /* Return the data in the DR register */
  return SPIx->DR;
}

void SPI_I2S_SendData(SPI_TypeDef* SPIx, uint16_t Data)
{
  /* Check the parameters */
  assert_param(IS_SPI_ALL_PERIPH(SPIx));
  
  /* Write in the DR register the data to be sent */
  SPIx->DR = Data;
}

void SPIx_Send_byte(uint16_t data)
{
	while(SPI_I2S_GetFlagStatus(hspi1.Instance, SPI_FLAG_TXE)==RESET);
	SPI_I2S_SendData(hspi1.Instance,data);

	while(SPI_I2S_GetFlagStatus(hspi1.Instance, SPI_FLAG_RXNE)==RESET);
	SPI_I2S_ReceiveData(hspi1.Instance);
}

uint16_t SPIx_Receive_byte(void)
{
	while(SPI_I2S_GetFlagStatus(hspi1.Instance, SPI_FLAG_TXE)==RESET);
	SPI_I2S_SendData(hspi1.Instance,0xffff);
	
	while(SPI_I2S_GetFlagStatus(hspi1.Instance, SPI_FLAG_RXNE)==RESET);
	return SPI_I2S_ReceiveData(hspi1.Instance);
}

void SPI_Cmd(SPI_TypeDef* SPIx, FunctionalState NewState)
{
  /* Check the parameters */
  assert_param(IS_SPI_ALL_PERIPH(SPIx));
  assert_param(IS_FUNCTIONAL_STATE(NewState));
  if (NewState != DISABLE)
  {
    /* Enable the selected SPI peripheral */
    SPIx->CR1 |= SPI_CR1_SPE;
  }
  else
  {
    /* Disable the selected SPI peripheral */
    SPIx->CR1 &= (uint16_t)~((uint16_t)SPI_CR1_SPE);
  }
}

//============== MDIO_Write =========================================
uint16_t mdio_write(uint16_t phyad,uint16_t devadr, uint16_t regadr, uint16_t value)
{
    uint8_t ST=0x00,OP=0x00,TA=0x02;
    uint16_t Preamble = 0xFFFF,CondiFrame = 0x0000;
    
    //============== first frame of Preamble 32bit========
    SPIx_Send_byte(Preamble);
    SPIx_Send_byte(Preamble);
    //============= Second frame of Conditon 16bit==========
    OP=0x00;    //Write Address
    CondiFrame = ST + (OP<<12) + (phyad<<7) + (devadr<<2) + TA;
    SPIx_Send_byte(CondiFrame);
    //============= Third frame of Write Address 16bit==========
    SPIx_Send_byte(regadr);
    
    //============== first frame of Preamble 32bit========
    SPIx_Send_byte(Preamble);
    SPIx_Send_byte(Preamble);
    //============= Second frame of Conditon 16bit==========
    OP=0x01;    //Write Data
    CondiFrame = ST + (OP<<12) + (phyad<<7) + (devadr<<2) + TA;
    SPIx_Send_byte(CondiFrame);
    //============= Third frame of Write Data  16bit==========
    SPIx_Send_byte(value);
		return 1;
}

//============ MDIO_Read =========================================
uint16_t mdio_read(uint16_t phyad,uint16_t devadr,uint16_t regadr)
{
	  uint8_t ST=0x00;
    uint8_t OP=0x00;
    uint8_t TA=0x02;
    uint16_t CondiFrame,value;
    
    //============== first frame of Preamble 32bit========
    SPIx_Send_byte(0xFFFF);
    SPIx_Send_byte(0xFFFF);
    //============= Second frame of Conditon 16bit==========
    OP=0x00;    //Write Address
    CondiFrame = ST | (OP<<12) | (phyad<<7) | (devadr<<2) | TA;
    SPIx_Send_byte(CondiFrame);
    //============= Third frame of Write Address 16bit==========
    SPIx_Send_byte(regadr);
    
    //============== first frame of Preamble 32bit========
    SPIx_Send_byte(0xFFFF);
    SPIx_Send_byte(0xFFFF);
    //============= Second frame of Conditon 16bit==========
    OP=0x03;    //Read Data
    CondiFrame = ST | (OP<<12) | (phyad<<7) | (devadr<<2) | TA;
    SPIx_Send_byte(CondiFrame);
    //============= Third frame of Data address 16bit==========
    value = SPIx_Receive_byte();
    //============= IDLE ======================================
    //SPIx_Send_byte(0xFF);
    return value;
}

/* #define I2C_ACCESS */

uint32_t sal_rdmdio(uint16_t phyad,uint16_t devadr,uint32_t regadr)
{
	uint16_t regadr_lsb = regadr&0xffff;
	uint16_t regadr_msb = regadr>>16;
	uint16_t data_lsb;
	uint16_t data_msb;
	
	mdio_write(phyad,devadr,IND_CTRL,0x60);
	mdio_write(phyad,devadr,IND_ADDRL,regadr_lsb);
	mdio_write(phyad,devadr,IND_ADDRH,regadr_msb);
	data_lsb = mdio_read(phyad,devadr,IND_DATAL);
	data_msb = mdio_read(phyad,devadr,IND_DATAH);
	
	return (data_msb<<16)+ data_lsb;
}

uint16_t sal_wrmdio(uint16_t phyad,uint16_t devadr, uint32_t regadr, uint32_t value)
{
	uint16_t regadr_lsb = regadr&0xffff;
	uint16_t regadr_msb = regadr>>16;
	uint16_t data_lsb = value&0xffff;
	uint16_t data_msb = value>>16;
	
	mdio_write(phyad,devadr,IND_CTRL,0x60);
	mdio_write(phyad,devadr,IND_ADDRL,regadr_lsb);
	mdio_write(phyad,devadr,IND_ADDRH,regadr_msb);
	mdio_write(phyad,devadr,IND_DATAL,data_lsb);
	mdio_write(phyad,devadr,IND_DATAH,data_msb);
	
	return 1;
}

uint32_t sal_rd_reg_ex(uint32_t address, uint8_t mdio_device, uint8_t mdio_port)
{
	unsigned long data;
	data = sal_rdmdio(mdio_port, mdio_device, address);
	//data = sal_readAhbI2cByte(0x50, address);
	return data;
}

void sal_wr_reg_ex(uint32_t address, uint32_t data,
	uint8_t mdio_device, uint8_t mdio_port)
{
   sal_wrmdio(mdio_port, mdio_device, address, data);
	//sal_writeAhbI2cByte(0x50, address, data);   //32bit
}


/** Read register value at specified address using default MDIO device type and
*  port address.
* @param address the address of the register
* @return the content of the register
*/
uint32_t sal_rd_reg(uint32_t address)
{
	return sal_rd_reg_ex(address, sal_current_mdio_dev, sal_current_mdio_port);
}

/** Write a value to the specified register along with MDIO device type and
*  port address.
* @param address the address of the register
* @param data the value to be written
*/
void sal_wr_reg(uint32_t address, uint32_t data)
{
	sal_wr_reg_ex(address, data, sal_current_mdio_dev, sal_current_mdio_port);
}

uint32_t rd_reg(uint32_t address)
{
	return sal_rd_reg(address);
}

void wr_reg(uint32_t address, uint32_t data)
{
	sal_wr_reg(address, data);
}

long rd_reg_ex(long a, long port)
{
	return rd_reg((uint32_t)a);
}

void wr_reg_ex(long a, long d, long port)
{
	wr_reg((uint32_t)a, d);
}
