/**
 *
 * @file     portofino_rtmr_pp_regs.h
 * <AUTHOR> Team
 * @date     08-18-2020
 * @version  1.0
 *
 * @property
 * $Copyright: (c) 2020 Broadcom.
 * Broadcom Proprietary and Confidential. All rights reserved.$
 *
 * @brief    This file contains definitions for public registers.
 *
 *           NOTE: DO NOT EDIT THIS FILE MANUALLY. This is a generated file.
 *           Refer to details given in portofino_rtmr_pp_regs.h
 *
 */

#ifndef PORTOFINO_RTMR_PP_REGS_H
#define PORTOFINO_RTMR_PP_REGS_H

#define RETIMER_EGR0                    0x00010000
#define RETIMER_EGR1                    0x00030000
#define RETIMER_IGR0                    0x00000000
#define RETIMER_IGR1                    0x00020000
#define RTMR_QC_PGEN_IGR_0   0x00000000
#define RTMR_QC_PGEN_IGR_1   0x00000100
#define RTMR_QC_PGEN_IGR_2   0x00000200
#define RTMR_QC_PGEN_IGR_3   0x00000300
#define RTMR_QC_PGEN_IGR_4   0x00000400
#define RTMR_QC_PGEN_IGR_5   0x00000500
#define RTMR_QC_PGEN_IGR_6   0x00000600
#define RTMR_QC_PGEN_IGR_7   0x00000700

#define RTMR_QC_PGEN_EGR_0   0x00010000
#define RTMR_QC_PGEN_EGR_1   0x00010100
#define RTMR_QC_PGEN_EGR_2   0x00010200
#define RTMR_QC_PGEN_EGR_3   0x00010300
#define RTMR_QC_PGEN_EGR_4   0x00010400
#define RTMR_QC_PGEN_EGR_5   0x00010500
#define RTMR_QC_PGEN_EGR_6   0x00010600
#define RTMR_QC_PGEN_EGR_7   0x00010700
#define RTMR_RSFEC_DEC_TOP_GAP      0x00000400
#define RTMR_RSFEC_ENC_TOP_GAP      0x00000400
#define LANE_OFFSET_KP4_KR4_FEC_DEC             0x00000400
#define RETIMER_CDR_CLK_CTRL_RDB               0x00005d90  
#define RETIMER_CDR_RSTB_CTRL_RDB              0x00005d98  
#define RETIMER_CMU_CLK_CTRL_RDB               0x00005d94  
#define RETIMER_CMU_RSTB_CTRL_RDB              0x00005d9c  
#define TMT_IFC_MUX_CTRL_RDB                   0x00005c40  
#define BYPASS_DEINT_REG                   0x00004500  
#define DISABLE_SKEW_MEM_REG               0x000044fc  
#define FEC_DATA_MUX_SEL0_REG              0x00004480  
#define FEC_DATA_MUX_SEL1_REG              0x00004484  
#define FEC_DATA_MUX_SEL2_REG              0x00004488  
#define FEC_DATA_MUX_SEL3_REG              0x0000448c  
#define FEC_DATA_MUX_SEL4_REG              0x00004490  
#define FEC_DATA_MUX_SEL5_REG              0x00004494  
#define FEC_DATA_MUX_SEL6_REG              0x00004498  
#define FEC_DATA_MUX_SEL7_REG              0x0000449c  
#define FEC_RX_MODE_REG                    0x00004404  
#define FEC_RX_SPEED_OVR_VAL_REG           0x00004418  
#define KX2KY_GBOX_100G0_50G01_CTRL0_REG   0x00004540  
#define KX2KY_GBOX_100G1_50G23_CTRL0_REG   0x0000454c  
#define KX2KY_GBOX_100G2_50G45_CTRL0_REG   0x00004558  
#define KX2KY_GBOX_100G3_50G67_CTRL0_REG   0x00004564  
#define TRAFFIC_SPEED_REG                  0x00004400  
#define FEC_PCS_TOP_TX_FEC_TX_FEC_MODE_REG                0x00004000  
#define BLOCK_CLKEN_CTRL1_REG         0x00004890  
#define BLOCK_CLKEN_CTRL3_REG         0x00004898  
#define BLOCK_CLKEN_CTRL4_REG         0x0000489c  
#define BLOCK_CLKEN_CTRL6_REG         0x000048a4  
#define BLOCK_CLKEN_CTRL9_REG         0x000048b0  
#define BLOCK_RESET_CTRL11_REG        0x000048f8  
#define BLOCK_RESET_CTRL1_REG         0x000048d0  
#define BLOCK_RESET_CTRL3_REG         0x000048d8  
#define BLOCK_RESET_CTRL4_REG         0x000048dc  
#define BLOCK_RESET_CTRL6_REG         0x000048e4  
#define BLOCK_RESET_CTRL9_REG         0x000048f0  
#define CMU2CDR_LANE_MAP_REG          0x00004864  
#define FEC_RCV_CLK_RST_MUX_REG       0x00004800  
#define FEC_RCV_TMT_CLK_RST_MUX_REG   0x00004848  
#define FEC_TMT_CLK_RST_MUX_REG       0x00004828  
#define LANE_PRBS_CLK_RST_MUX_REG     0x00004840  
#define PCS_RCV_CLK_RST_MUX_REG       0x00004804  
#define PCS_RCV_TMT_CLK_RST_MUX_REG   0x0000484c  
#define PCS_TMT_RCLK_RST_MUX_REG      0x00004820  
#define PCS_TMT_TCLK_RST_MUX_REG      0x00004824  
#define PFIFO_CLK_RST_MUX_REG         0x00004844  
#define RCV_CLK40_GAPCLK_CTRL0_REG    0x00004808  
#define RCV_CLK40_GAPCLK_CTRL1_REG    0x0000480c  
#define RCV_CLK64_GAPCLK_CTRL0_REG    0x00004818  
#define RCV_CLK64_GAPCLK_CTRL1_REG    0x0000481c  
#define TMT_CLK40_GAPCLK_CTRL0_REG    0x0000482c  
#define TMT_CLK64_GAPCLK_CTRL0_REG    0x00004838  
#define TMT_CLK64_GAPCLK_CTRL1_REG    0x0000483c  
#define TOP_RESET_CTRL0_REG           0x000048c0  
#define TOP_RESET_CTRL1_REG           0x000048c4  
#define TOP_RESET_CTRL2_REG           0x000048c8  
#define RCV_PAM_NRZ_CTRL_REG              0x00005900  
#define RCV_PFIFO_DMUX0_REG               0x00005928  
#define RCV_PFIFO_DMUX1_REG               0x0000592c  
#define RCV_PFIFO_DMUX2_REG               0x00005930  
#define TMT_PAM_NRZ_CTRL_REG              0x00005904  
#define TMT_PFIFO_DMUX0_REG               0x00005934  
#define TMT_PFIFO_DMUX1_REG               0x00005938  
#define TMT_PFIFO_DMUX2_REG               0x0000593c  
#define AM_DEBUG0_REG                            0x00001c04  
#define AM_LANE_ALIGNMENT_STATUS_REG             0x00001c44  
#define AM_LANE_ALIGNMENT_UNLOCK_IRQ_STICKY_REG  0x00001cf0  
#define AM_LOSS_LOCK_LANE_STICKY_STATUS_REG      0x00001c40  
#define DESKEW_CTRL_REG                          0x00001c20  
#define PCS_AM_DEBUG0_REG                        0x00001828  
#define PCS_AM_DEBUG2_REG                        0x00001830  
#define PCS_AM_DEBUG3_REG                        0x00001834  
#define PCS_AM_DEBUG4_REG                        0x00001838  
#define PCS_AM_DEBUG5_REG                        0x0000183c  
#define QC_PGEN_CFG0_REGISTER                 0x00006004  
#define QC_PGEN_CTRL0_REGISTER                0x00006000  
#define QC_PGEN_DBG0_REGISTER                 0x00006008  
#define QC_PMON_CFG0_REG                      0x00006804  
#define QC_PMON_CHK_NERE_HI_REG               0x00006830  
#define QC_PMON_CHK_BERE_LO_REG               0x0000682c  
#define QC_PMON_CHK_BERO_HI_REG               0x00006828  
#define QC_PMON_CHK_BERO_LO_REG               0x00006824  
#define QC_PMON_CHK_CSR0_REG                  0x0000681c  
#define QC_PMON_CHK_CSR1_REG                  0x00006820  
#define QC_PMON_CTRL0_REG                     0x00006800  
#define QC_PMON_DBG0_REG                      0x00006818  
#define QC_PMON_LOCK0_REG                     0x0000680c  
#define QC_PMON_PBI0_REG                      0x00006814  
#define QC_PMON_UNLOCK0_REG                   0x00006810  
#define FEC_FIFO_BUNDLE_DIS_REG            0x00004a24  
#define FEC_FIFO_CLK_RST_SEL_REG           0x00004a2c  
#define FEC_FIFO_CORR_MODE_REG             0x00004a28  
#define FEC_FIFO_LAT_CTL_REG               0x00004a18  
#define FIFO_COLLISION_STICKY_STATUS_REG   0x00004ac0  
#define FEC_COUNTER_CONTROL_REGISTER    0x0000201c  
#define MD_TOT_BIT0S_CORR_HI_REGISTER   0x00002048  
#define MD_TOT_BIT0S_CORR_LO_REGISTER   0x00002050  
#define MD_TOT_BIT0S_CORR_MD_REGISTER   0x0000204c  
#define MD_TOT_FRAMES_0ERR_HI_REGISTER  0x00002054  
#define MD_TOT_FRAMES_0ERR_LO_REGISTER  0x0000205c  
#define MD_TOT_FRAMES_0ERR_MD_REGISTER  0x00002058  
#define MD_TOT_FRAMES_RCV_HI_REGISTER   0x00002024  
#define MD_TOT_FRAMES_RCV_LO_REGISTER   0x0000202c  
#define MD_TOT_FRAMES_RCV_MD_REGISTER   0x00002028  
#define MD_TOT_FRAMES_UNCORR_HI_REGISTER  0x00002030  
#define MD_TOT_FRAMES_UNCORR_LO_REGISTER  0x00002038  
#define MD_TOT_FRAMES_UNCORR_MD_REGISTER  0x00002034  
#define MODE_CONTROL_REGISTER           0x00002000  
#define MODE_CONTROL_REGISTER_ENCODER   0x00002200  
#define PCS_STATUS1_REGISTER_50G_100G_0  0x00000168  
#define PCS_STATUS1_REGISTER_50G_100G_2  0x00000568  
#define PCS_STATUS1_REGISTER_50G_100G_4  0x00000968  
#define PCS_STATUS1_REGISTER_50G_100G_6  0x00000d68  
#define TMT_FEC_FIFO_BUNDLE_DIS_REG        0x00004b2c  
#define TMT_FEC_FIFO_CLK_CTRL_REG          0x00004b04  
#define TMT_FEC_FIFO_CLK_RST_SEL_REG       0x00004b34  
#define TMT_FEC_FIFO_CORR_MODE_REG         0x00004b30  
#define TMT_FEC_FIFO_LAT_CTL_REG           0x00004b1c  
#define TMT_FEC_FIFO_RESETB_REG            0x00004b08  
#define TMT_FEC_PCS_DATA_MUX_CTRL_REG      0x00004b00  
#define TMT_FIFO_COLLISION_STICKY_STATUS_REG  0x00004bc0  
#define TMT_PCS_GBOX_RESETB                0x00004b8c  
#define XDEC_FEC_TYPE_CTRL_REG                   0x00004dc8  
#define XDEC_GBOX_160_257_IRQ_STICKY_REG         0x00004d9c  
#define XDEC_GBOX_GAP_1_REG                      0x00004c58  
#define XDEC_GBOX_GAP_2_REG                      0x00004c5c  
#define XDEC_GBOX_GAP_3_REG                      0x00004c60  
#define XDEC_GBOX_GAP_REG                        0x00004c54  
#define XDEC_GBOX_WIDTH_CTRL_REG                 0x00004dc4  
#define SCRAMBLER_CTRL_REG                       0x00004ee8  
#define XENC_GBOX_257_160_IRQ_STICKY_REG         0x00004ec0  
#define XENC_GBOX_GAP_CTRL_REG                   0x00004e08  
#define XENC_GBOX_GAP_CTRL1_REG                  0x00004e0c  
#define XENC_GBOX_GAP_CTRL2_REG                  0x00004ed4  
#define XENC_GBOX_GAP_CTRL3_REG                  0x00004ed8  
#define XENC_GBOX_WIDTH_CTRL_REG                 0x00004e3c  
#define XENC_SLICE_CTRL_REG                      0x00004ef4  
/************************************************************* 
* apb2_slv0_amba :: RETIMER_CDR_CLK_CTRL_RDB 
*************************************************************/ 
#define RETIMER_CDR_CLK_CTRL_RDB_MD_RTM_CDR_PCS_CKEN_MASK                     0x0000ff00   /*!< MD_RTM_CDR_PCS_CKEN */ 
#define RETIMER_CDR_CLK_CTRL_RDB_MD_RTM_CDR_PCS_CKEN_SHIFT                    8  
#define RETIMER_CDR_CLK_CTRL_RDB_MD_RTM_CDR_CKEN_MASK                         0x000000ff   /*!< MD_RTM_CDR_CKEN */ 
#define RETIMER_CDR_CLK_CTRL_RDB_MD_RTM_CDR_CKEN_SHIFT                        0  
/************************************************************* 
* apb2_slv0_amba :: RETIMER_CDR_RSTB_CTRL_RDB
*************************************************************/ 
#define RETIMER_CDR_RSTB_CTRL_RDB_MD_RTM_CDR_PCS_RSTB_MASK                    0x0000ff00   /*!< MD_RTM_CDR_PCS_RSTB */ 
#define RETIMER_CDR_RSTB_CTRL_RDB_MD_RTM_CDR_PCS_RSTB_SHIFT                   8  
#define RETIMER_CDR_RSTB_CTRL_RDB_MD_RTM_CDR_RSTB_MASK                        0x000000ff   /*!< MD_RTM_CDR_RSTB */ 
#define RETIMER_CDR_RSTB_CTRL_RDB_MD_RTM_CDR_RSTB_SHIFT                       0  
/************************************************************* 
* apb2_slv0_amba :: RETIMER_CMU_CLK_CTRL_RDB
*************************************************************/ 
#define RETIMER_CMU_CLK_CTRL_RDB_MD_RTM_CMU_PCS_CKEN_MASK                     0x0000ff00   /*!< MD_RTM_CMU_PCS_CKEN */ 
#define RETIMER_CMU_CLK_CTRL_RDB_MD_RTM_CMU_PCS_CKEN_SHIFT                    8  
#define RETIMER_CMU_CLK_CTRL_RDB_MD_RTM_CMU_CKEN_MASK                         0x000000ff   /*!< MD_RTM_CMU_CKEN */ 
#define RETIMER_CMU_CLK_CTRL_RDB_MD_RTM_CMU_CKEN_SHIFT                        0  
/************************************************************* 
* apb2_slv0_amba :: RETIMER_CMU_RSTB_CTRL_RDB
*************************************************************/ 
#define RETIMER_CMU_RSTB_CTRL_RDB_MD_RTM_CMU_PCS_RSTB_MASK                    0x0000ff00   /*!< MD_RTM_CMU_PCS_RSTB */ 
#define RETIMER_CMU_RSTB_CTRL_RDB_MD_RTM_CMU_PCS_RSTB_SHIFT                   8  
#define RETIMER_CMU_RSTB_CTRL_RDB_MD_RTM_CMU_RSTB_MASK                        0x000000ff   /*!< MD_RTM_CMU_RSTB */ 
#define RETIMER_CMU_RSTB_CTRL_RDB_MD_RTM_CMU_RSTB_SHIFT                       0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL0_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL0_REG_MD_PCS_XDEC_DATA_MUX0_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX0 */ 
#define FEC_DATA_MUX_SEL0_REG_MD_PCS_XDEC_DATA_MUX0_SHIFT                 9  
#define FEC_DATA_MUX_SEL0_REG_MD_FEC_ENC_DATA_MUX0_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX0 */ 
#define FEC_DATA_MUX_SEL0_REG_MD_FEC_ENC_DATA_MUX0_SHIFT                  6  
#define FEC_DATA_MUX_SEL0_REG_MD_FEC_DEC_DATA_MUX0_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX0 */ 
#define FEC_DATA_MUX_SEL0_REG_MD_FEC_DEC_DATA_MUX0_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL1_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL1_REG_MD_PCS_XDEC_DATA_MUX1_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX1 */ 
#define FEC_DATA_MUX_SEL1_REG_MD_PCS_XDEC_DATA_MUX1_SHIFT                 9  
#define FEC_DATA_MUX_SEL1_REG_MD_FEC_ENC_DATA_MUX1_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX1 */ 
#define FEC_DATA_MUX_SEL1_REG_MD_FEC_ENC_DATA_MUX1_SHIFT                  6  
#define FEC_DATA_MUX_SEL1_REG_MD_FEC_DEC_DATA_MUX1_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX1 */ 
#define FEC_DATA_MUX_SEL1_REG_MD_FEC_DEC_DATA_MUX1_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL2_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL2_REG_MD_PCS_XDEC_DATA_MUX2_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX2 */ 
#define FEC_DATA_MUX_SEL2_REG_MD_PCS_XDEC_DATA_MUX2_SHIFT                 9  
#define FEC_DATA_MUX_SEL2_REG_MD_FEC_ENC_DATA_MUX2_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX2 */ 
#define FEC_DATA_MUX_SEL2_REG_MD_FEC_ENC_DATA_MUX2_SHIFT                  6  
#define FEC_DATA_MUX_SEL2_REG_MD_FEC_DEC_DATA_MUX2_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX2 */ 
#define FEC_DATA_MUX_SEL2_REG_MD_FEC_DEC_DATA_MUX2_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL3_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL3_REG_MD_PCS_XDEC_DATA_MUX3_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX3 */ 
#define FEC_DATA_MUX_SEL3_REG_MD_PCS_XDEC_DATA_MUX3_SHIFT                 9  
#define FEC_DATA_MUX_SEL3_REG_MD_FEC_ENC_DATA_MUX3_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX3 */ 
#define FEC_DATA_MUX_SEL3_REG_MD_FEC_ENC_DATA_MUX3_SHIFT                  6  
#define FEC_DATA_MUX_SEL3_REG_MD_FEC_DEC_DATA_MUX3_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX3 */ 
#define FEC_DATA_MUX_SEL3_REG_MD_FEC_DEC_DATA_MUX3_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL4_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL4_REG_MD_PCS_XDEC_DATA_MUX4_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX4 */ 
#define FEC_DATA_MUX_SEL4_REG_MD_PCS_XDEC_DATA_MUX4_SHIFT                 9  
#define FEC_DATA_MUX_SEL4_REG_MD_FEC_ENC_DATA_MUX4_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX4 */ 
#define FEC_DATA_MUX_SEL4_REG_MD_FEC_ENC_DATA_MUX4_SHIFT                  6  
#define FEC_DATA_MUX_SEL4_REG_MD_FEC_DEC_DATA_MUX4_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX4 */ 
#define FEC_DATA_MUX_SEL4_REG_MD_FEC_DEC_DATA_MUX4_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL5_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL5_REG_MD_PCS_XDEC_DATA_MUX5_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX5 */ 
#define FEC_DATA_MUX_SEL5_REG_MD_PCS_XDEC_DATA_MUX5_SHIFT                 9  
#define FEC_DATA_MUX_SEL5_REG_MD_FEC_ENC_DATA_MUX5_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX5 */ 
#define FEC_DATA_MUX_SEL5_REG_MD_FEC_ENC_DATA_MUX5_SHIFT                  6  
#define FEC_DATA_MUX_SEL5_REG_MD_FEC_DEC_DATA_MUX5_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX5 */ 
#define FEC_DATA_MUX_SEL5_REG_MD_FEC_DEC_DATA_MUX5_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL6_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL6_REG_MD_PCS_XDEC_DATA_MUX6_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX6 */ 
#define FEC_DATA_MUX_SEL6_REG_MD_PCS_XDEC_DATA_MUX6_SHIFT                 9  
#define FEC_DATA_MUX_SEL6_REG_MD_FEC_ENC_DATA_MUX6_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX6 */ 
#define FEC_DATA_MUX_SEL6_REG_MD_FEC_ENC_DATA_MUX6_SHIFT                  6  
#define FEC_DATA_MUX_SEL6_REG_MD_FEC_DEC_DATA_MUX6_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX6 */ 
#define FEC_DATA_MUX_SEL6_REG_MD_FEC_DEC_DATA_MUX6_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_DATA_MUX_SEL7_REG
*************************************************************/ 
#define FEC_DATA_MUX_SEL7_REG_MD_PCS_XDEC_DATA_MUX7_MASK                  0x00000e00   /*!< MD_PCS_XDEC_DATA_MUX7 */ 
#define FEC_DATA_MUX_SEL7_REG_MD_PCS_XDEC_DATA_MUX7_SHIFT                 9  
#define FEC_DATA_MUX_SEL7_REG_MD_FEC_ENC_DATA_MUX7_MASK                   0x000001c0   /*!< MD_FEC_ENC_DATA_MUX7 */ 
#define FEC_DATA_MUX_SEL7_REG_MD_FEC_ENC_DATA_MUX7_SHIFT                  6  
#define FEC_DATA_MUX_SEL7_REG_MD_FEC_DEC_DATA_MUX7_MASK                   0x00000007   /*!< MD_FEC_DEC_DATA_MUX7 */ 
#define FEC_DATA_MUX_SEL7_REG_MD_FEC_DEC_DATA_MUX7_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_TOP_RX_IGR0_FEC_RX_MODE_REG_IGR0 
*************************************************************/ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_7_MASK                               0x00008000   /*!< MD_SPEED_25G_7 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_7_SHIFT                              15  
#define FEC_RX_MODE_REG_MD_SPEED_25G_6_MASK                               0x00004000   /*!< MD_SPEED_25G_6 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_6_SHIFT                              14  
#define FEC_RX_MODE_REG_MD_SPEED_25G_5_MASK                               0x00002000   /*!< MD_SPEED_25G_5 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_5_SHIFT                              13  
#define FEC_RX_MODE_REG_MD_SPEED_25G_4_MASK                               0x00001000   /*!< MD_SPEED_25G_4 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_4_SHIFT                              12  
#define FEC_RX_MODE_REG_MD_SPEED_25G_3_MASK                               0x00000800   /*!< MD_SPEED_25G_3 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_3_SHIFT                              11  
#define FEC_RX_MODE_REG_MD_SPEED_25G_2_MASK                               0x00000400   /*!< MD_SPEED_25G_2 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_2_SHIFT                              10  
#define FEC_RX_MODE_REG_MD_SPEED_25G_1_MASK                               0x00000200   /*!< MD_SPEED_25G_1 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_1_SHIFT                              9  
#define FEC_RX_MODE_REG_MD_SPEED_25G_0_MASK                               0x00000100   /*!< MD_SPEED_25G_0 */ 
#define FEC_RX_MODE_REG_MD_SPEED_25G_0_SHIFT                              8  
#define FEC_RX_MODE_REG_MD_FEC_MODE_KR4_MASK                              0x000000ff   /*!< MD_FEC_MODE_KR4 */ 
#define FEC_RX_MODE_REG_MD_FEC_MODE_KR4_SHIFT                             0  
/************************************************************* 
* apb2_slv0_amba :: KX2KY_GBOX_100G0_50G01_CTRL0_REG
*************************************************************/ 
#define KX2KY_GBOX_100G0_50G01_CTRL0_REG_MD_KXKY_EN_KR2KP_CH01_MASK       0x00000002   /*!< MD_KXKY_EN_KR2KP_CH01 */ 
#define KX2KY_GBOX_100G0_50G01_CTRL0_REG_MD_KXKY_EN_KR2KP_CH01_SHIFT      1  
#define KX2KY_GBOX_100G0_50G01_CTRL0_REG_MD_KXKY_EN_100G_CH01_MASK        0x00000001   /*!< MD_KXKY_EN_100G_CH01 */ 
#define KX2KY_GBOX_100G0_50G01_CTRL0_REG_MD_KXKY_EN_100G_CH01_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: KX2KY_GBOX_100G1_50G23_CTRL0_REG
*************************************************************/ 
#define KX2KY_GBOX_100G1_50G23_CTRL0_REG_MD_KXKY_EN_KR2KP_CH23_MASK       0x00000002   /*!< MD_KXKY_EN_KR2KP_CH23 */ 
#define KX2KY_GBOX_100G1_50G23_CTRL0_REG_MD_KXKY_EN_KR2KP_CH23_SHIFT      1  
#define KX2KY_GBOX_100G1_50G23_CTRL0_REG_MD_KXKY_EN_100G_CH23_MASK        0x00000001   /*!< MD_KXKY_EN_100G_CH23 */ 
#define KX2KY_GBOX_100G1_50G23_CTRL0_REG_MD_KXKY_EN_100G_CH23_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: KX2KY_GBOX_100G2_50G45_CTRL0_REG
*************************************************************/ 
#define KX2KY_GBOX_100G2_50G45_CTRL0_REG_MD_KXKY_EN_KR2KP_CH45_MASK       0x00000002   /*!< MD_KXKY_EN_KR2KP_CH45 */ 
#define KX2KY_GBOX_100G2_50G45_CTRL0_REG_MD_KXKY_EN_KR2KP_CH45_SHIFT      1  
#define KX2KY_GBOX_100G2_50G45_CTRL0_REG_MD_KXKY_EN_100G_CH45_MASK        0x00000001   /*!< MD_KXKY_EN_100G_CH45 */ 
#define KX2KY_GBOX_100G2_50G45_CTRL0_REG_MD_KXKY_EN_100G_CH45_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: KX2KY_GBOX_100G3_50G67_CTRL0_REG
*************************************************************/ 
#define KX2KY_GBOX_100G3_50G67_CTRL0_REG_MD_KXKY_EN_KR2KP_CH67_MASK       0x00000002   /*!< MD_KXKY_EN_KR2KP_CH67 */ 
#define KX2KY_GBOX_100G3_50G67_CTRL0_REG_MD_KXKY_EN_KR2KP_CH67_SHIFT      1  
#define KX2KY_GBOX_100G3_50G67_CTRL0_REG_MD_KXKY_EN_100G_CH67_MASK        0x00000001   /*!< MD_KXKY_EN_100G_CH67 */ 
#define KX2KY_GBOX_100G3_50G67_CTRL0_REG_MD_KXKY_EN_100G_CH67_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: TRAFFIC_SPEED_REG
*************************************************************/ 
#define TRAFFIC_SPEED_REG_MD_SPEED_400G_MASK                              0x00004000   /*!< MD_SPEED_400G */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_400G_SHIFT                             14  
#define TRAFFIC_SPEED_REG_MD_SPEED_200G_1_MASK                            0x00002000   /*!< MD_SPEED_200G_1 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_200G_1_SHIFT                           13  
#define TRAFFIC_SPEED_REG_MD_SPEED_200G_0_MASK                            0x00001000   /*!< MD_SPEED_200G_0 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_200G_0_SHIFT                           12  
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_3_MASK                            0x00000800   /*!< MD_SPEED_100G_3 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_3_SHIFT                           11  
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_2_MASK                            0x00000400   /*!< MD_SPEED_100G_2 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_2_SHIFT                           10  
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_1_MASK                            0x00000200   /*!< MD_SPEED_100G_1 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_1_SHIFT                           9  
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_0_MASK                            0x00000100   /*!< MD_SPEED_100G_0 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_100G_0_SHIFT                           8  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_7_MASK                             0x00000080   /*!< MD_SPEED_50G_7 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_7_SHIFT                            7  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_6_MASK                             0x00000040   /*!< MD_SPEED_50G_6 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_6_SHIFT                            6  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_5_MASK                             0x00000020   /*!< MD_SPEED_50G_5 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_5_SHIFT                            5  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_4_MASK                             0x00000010   /*!< MD_SPEED_50G_4 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_4_SHIFT                            4  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_3_MASK                             0x00000008   /*!< MD_SPEED_50G_3 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_3_SHIFT                            3  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_2_MASK                             0x00000004   /*!< MD_SPEED_50G_2 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_2_SHIFT                            2  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_1_MASK                             0x00000002   /*!< MD_SPEED_50G_1 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_1_SHIFT                            1  
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_0_MASK                             0x00000001   /*!< MD_SPEED_50G_0 */ 
#define TRAFFIC_SPEED_REG_MD_SPEED_50G_0_SHIFT                            0  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_TOP_TX_IGR0_FEC_TX_FEC_MODE_REG_IGR0 
*************************************************************/ 
#define FEC_PCS_TOP_TX_FEC_TX_FEC_MODE_REG_MD_FEC_TX_MODE_KR4_MASK                       0x000000ff   /*!< MD_FEC_TX_MODE_KR4 */ 
#define FEC_PCS_TOP_TX_FEC_TX_FEC_MODE_REG_MD_FEC_TX_MODE_KR4_SHIFT                      0  
/************************************************************* 
* apb2_slv0_amba :: BLOCK_CLKEN_CTRL1_REG
*************************************************************/ 
#define BLOCK_CLKEN_CTRL1_REG_MD_CKE_FEC_XDEC_MASK                   0x000000ff   /*!< MD_CKE_FEC_XDEC */ 
#define BLOCK_CLKEN_CTRL1_REG_MD_CKE_FEC_XDEC_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: BLOCK_CLKEN_CTRL9_REG 
*************************************************************/ 
#define BLOCK_CLKEN_CTRL9_REG_MD_CKE_FEC_XENC_MASK                   0x000000ff   /*!< MD_CKE_FEC_XENC */ 
#define BLOCK_CLKEN_CTRL9_REG_MD_CKE_FEC_XENC_SHIFT                  0  
/************************************************************* 
* apb2_slv0_amba :: FEC_RCV_CLK_RST_MUX_REG
*************************************************************/ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE7_CLK_RST_MUX_MASK   0x0000c000   /*!< MD_FEC_RCV_SLICE7_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE7_CLK_RST_MUX_SHIFT  14  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE6_CLK_RST_MUX_MASK   0x00003000   /*!< MD_FEC_RCV_SLICE6_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE6_CLK_RST_MUX_SHIFT  12  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE5_CLK_RST_MUX_MASK   0x00000c00   /*!< MD_FEC_RCV_SLICE5_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE5_CLK_RST_MUX_SHIFT  10  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE4_CLK_RST_MUX_MASK   0x00000100   /*!< MD_FEC_RCV_SLICE4_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE4_CLK_RST_MUX_SHIFT  8  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE3_CLK_RST_MUX_MASK   0x000000c0   /*!< MD_FEC_RCV_SLICE3_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE3_CLK_RST_MUX_SHIFT  6  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE2_CLK_RST_MUX_MASK   0x00000030   /*!< MD_FEC_RCV_SLICE2_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE2_CLK_RST_MUX_SHIFT  4  
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE1_CLK_RST_MUX_MASK   0x00000004   /*!< MD_FEC_RCV_SLICE1_CLK_RST_MUX */ 
#define FEC_RCV_CLK_RST_MUX_REG_MD_FEC_RCV_SLICE1_CLK_RST_MUX_SHIFT  2  
/************************************************************* 
* apb2_slv0_amba :: FEC_RCV_TMT_CLK_RST_MUX_REG
*************************************************************/ 
#define FEC_RCV_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_CLK66_RST_MUX_LN_MASK 0x0000ff00   /*!< MD_FEC_TMT_CLK66_RST_MUX_LN */ 
#define FEC_RCV_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_CLK66_RST_MUX_LN_SHIFT 8  
#define FEC_RCV_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_CLK40_RST_MUX_LN_MASK 0x000000ff   /*!< MD_FEC_TMT_CLK40_RST_MUX_LN */ 
#define FEC_RCV_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_CLK40_RST_MUX_LN_SHIFT 0  
/************************************************************* 
* apb2_slv0_amba :: FEC_TMT_CLK_RST_MUX_REG
*************************************************************/ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE7_CLK_RST_MUX_MASK   0x0000c000   /*!< MD_FEC_TMT_SLICE7_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE7_CLK_RST_MUX_SHIFT  14  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE6_CLK_RST_MUX_MASK   0x00003000   /*!< MD_FEC_TMT_SLICE6_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE6_CLK_RST_MUX_SHIFT  12  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE5_CLK_RST_MUX_MASK   0x00000c00   /*!< MD_FEC_TMT_SLICE5_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE5_CLK_RST_MUX_SHIFT  10  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE4_CLK_RST_MUX_MASK   0x00000100   /*!< MD_FEC_TMT_SLICE4_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE4_CLK_RST_MUX_SHIFT  8  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE3_CLK_RST_MUX_MASK   0x000000c0   /*!< MD_FEC_TMT_SLICE3_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE3_CLK_RST_MUX_SHIFT  6  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE2_CLK_RST_MUX_MASK   0x00000030   /*!< MD_FEC_TMT_SLICE2_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE2_CLK_RST_MUX_SHIFT  4  
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE1_CLK_RST_MUX_MASK   0x00000004   /*!< MD_FEC_TMT_SLICE1_CLK_RST_MUX */ 
#define FEC_TMT_CLK_RST_MUX_REG_MD_FEC_TMT_SLICE1_CLK_RST_MUX_SHIFT  2  
/************************************************************* 
* apb2_slv0_amba :: PCS_RCV_CLK_RST_MUX_REG
*************************************************************/ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE7_CLK_RST_MUX_MASK   0x0000c000   /*!< MD_PCS_RCV_SLICE7_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE7_CLK_RST_MUX_SHIFT  14  
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE6_CLK_RST_MUX_MASK   0x00001000   /*!< MD_PCS_RCV_SLICE6_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE6_CLK_RST_MUX_SHIFT  12  
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE5_CLK_RST_MUX_MASK   0x00000400   /*!< MD_PCS_RCV_SLICE5_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE5_CLK_RST_MUX_SHIFT  10  
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE3_CLK_RST_MUX_MASK   0x000000c0   /*!< MD_PCS_RCV_SLICE3_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE3_CLK_RST_MUX_SHIFT  6  
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE2_CLK_RST_MUX_MASK   0x00000010   /*!< MD_PCS_RCV_SLICE2_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE2_CLK_RST_MUX_SHIFT  4  
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE1_CLK_RST_MUX_MASK   0x00000004   /*!< MD_PCS_RCV_SLICE1_CLK_RST_MUX */ 
#define PCS_RCV_CLK_RST_MUX_REG_MD_PCS_RCV_SLICE1_CLK_RST_MUX_SHIFT  2  
/************************************************************* 
* apb2_slv0_amba :: PCS_TMT_RCLK_RST_MUX_REG
*************************************************************/ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE7_RCLK_RST_MUX_MASK 0x0000c000   /*!< MD_PCS_TMT_SLICE7_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE7_RCLK_RST_MUX_SHIFT 14  
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE6_RCLK_RST_MUX_MASK 0x00003000   /*!< MD_PCS_TMT_SLICE6_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE6_RCLK_RST_MUX_SHIFT 12  
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE5_RCLK_RST_MUX_MASK 0x00000400   /*!< MD_PCS_TMT_SLICE5_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE5_RCLK_RST_MUX_SHIFT 10  
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE3_RCLK_RST_MUX_MASK 0x000000c0   /*!< MD_PCS_TMT_SLICE3_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE3_RCLK_RST_MUX_SHIFT 6  
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE2_RCLK_RST_MUX_MASK 0x00000030   /*!< MD_PCS_TMT_SLICE2_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE2_RCLK_RST_MUX_SHIFT 4  
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE1_RCLK_RST_MUX_MASK 0x00000004   /*!< MD_PCS_TMT_SLICE1_RCLK_RST_MUX */ 
#define PCS_TMT_RCLK_RST_MUX_REG_MD_PCS_TMT_SLICE1_RCLK_RST_MUX_SHIFT 2  
/************************************************************* 
* apb2_slv0_amba :: PCS_TMT_TCLK_RST_MUX_REG
*************************************************************/ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE7_TCLK_RST_MUX_MASK 0x0000c000   /*!< MD_PCS_TMT_SLICE7_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE7_TCLK_RST_MUX_SHIFT 14  
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE6_TCLK_RST_MUX_MASK 0x00001000   /*!< MD_PCS_TMT_SLICE6_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE6_TCLK_RST_MUX_SHIFT 12  
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE5_TCLK_RST_MUX_MASK 0x00000400   /*!< MD_PCS_TMT_SLICE5_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE5_TCLK_RST_MUX_SHIFT 10  
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE3_TCLK_RST_MUX_MASK 0x000000c0   /*!< MD_PCS_TMT_SLICE3_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE3_TCLK_RST_MUX_SHIFT 6  
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE2_TCLK_RST_MUX_MASK 0x00000010   /*!< MD_PCS_TMT_SLICE2_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE2_TCLK_RST_MUX_SHIFT 4  
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE1_TCLK_RST_MUX_MASK 0x00000004   /*!< MD_PCS_TMT_SLICE1_TCLK_RST_MUX */ 
#define PCS_TMT_TCLK_RST_MUX_REG_MD_PCS_TMT_SLICE1_TCLK_RST_MUX_SHIFT 2  
/************************************************************* 
* apb2_slv0_amba :: PFIFO_CLK_RST_MUX_REG
*************************************************************/ 
#define PFIFO_CLK_RST_MUX_REG_MD_TMT_PFIFO_SLICE5_WCLK_RST_MUX_MASK  0x00002000   /*!< MD_TMT_PFIFO_SLICE5_WCLK_RST_MUX */ 
#define PFIFO_CLK_RST_MUX_REG_MD_TMT_PFIFO_SLICE5_WCLK_RST_MUX_SHIFT 13  
#define PFIFO_CLK_RST_MUX_REG_MD_TMT_PFIFO_SLICE1_WCLK_RST_MUX_MASK  0x00000200   /*!< MD_TMT_PFIFO_SLICE1_WCLK_RST_MUX */ 
#define PFIFO_CLK_RST_MUX_REG_MD_TMT_PFIFO_SLICE1_WCLK_RST_MUX_SHIFT 9  
#define PFIFO_CLK_RST_MUX_REG_MD_RCV_PFIFO_SLICE6_WCLK_RST_MUX_MASK  0x00000040   /*!< MD_RCV_PFIFO_SLICE6_WCLK_RST_MUX */ 
#define PFIFO_CLK_RST_MUX_REG_MD_RCV_PFIFO_SLICE6_WCLK_RST_MUX_SHIFT 6  
#define PFIFO_CLK_RST_MUX_REG_MD_RCV_PFIFO_SLICE2_WCLK_RST_MUX_MASK  0x00000004   /*!< MD_RCV_PFIFO_SLICE2_WCLK_RST_MUX */ 
#define PFIFO_CLK_RST_MUX_REG_MD_RCV_PFIFO_SLICE2_WCLK_RST_MUX_SHIFT 2  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_WRAPPER_IGR0_RCV_PAM_NRZ_CTRL_REG_IGR0 
*************************************************************/ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_7_MASK                          0x0000c000   /*!< RCV_NRZ_PAM_7 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_7_SHIFT                         14  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_6_MASK                          0x00003000   /*!< RCV_NRZ_PAM_6 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_6_SHIFT                         12  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_5_MASK                          0x00000c00   /*!< RCV_NRZ_PAM_5 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_5_SHIFT                         10  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_4_MASK                          0x00000300   /*!< RCV_NRZ_PAM_4 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_4_SHIFT                         8  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_3_MASK                          0x000000c0   /*!< RCV_NRZ_PAM_3 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_3_SHIFT                         6  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_2_MASK                          0x00000030   /*!< RCV_NRZ_PAM_2 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_2_SHIFT                         4  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_1_MASK                          0x0000000c   /*!< RCV_NRZ_PAM_1 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_1_SHIFT                         2  
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_0_MASK                          0x00000003   /*!< RCV_NRZ_PAM_0 */ 
#define RCV_PAM_NRZ_CTRL_REG_RCV_NRZ_PAM_0_SHIFT                         0  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_WRAPPER_IGR0_RCV_PFIFO_DMUX0_REG_IGR0 
*************************************************************/ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX5_MASK                     0x00007000   /*!< RCV_PFIFO_DOUT_MUX5 */ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX5_SHIFT                    12  
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX4_MASK                     0x00000e00   /*!< RCV_PFIFO_DOUT_MUX4 */ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX4_SHIFT                    9  
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX3_MASK                     0x000001c0   /*!< RCV_PFIFO_DOUT_MUX3 */ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX3_SHIFT                    6  
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX2_MASK                     0x00000038   /*!< RCV_PFIFO_DOUT_MUX2 */ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX2_SHIFT                    3  
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX1_MASK                     0x00000007   /*!< RCV_PFIFO_DOUT_MUX1 */ 
#define RCV_PFIFO_DMUX0_REG_RCV_PFIFO_DOUT_MUX1_SHIFT                    0  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_WRAPPER_IGR0_RCV_PFIFO_DMUX1_REG_IGR0 
*************************************************************/ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX10_MASK                    0x00007000   /*!< RCV_PFIFO_DOUT_MUX10 */ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX10_SHIFT                   12  
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX9_MASK                     0x00000e00   /*!< RCV_PFIFO_DOUT_MUX9 */ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX9_SHIFT                    9  
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX8_MASK                     0x000001c0   /*!< RCV_PFIFO_DOUT_MUX8 */ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX8_SHIFT                    6  
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX7_MASK                     0x00000038   /*!< RCV_PFIFO_DOUT_MUX7 */ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX7_SHIFT                    3  
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX6_MASK                     0x00000007   /*!< RCV_PFIFO_DOUT_MUX6 */ 
#define RCV_PFIFO_DMUX1_REG_RCV_PFIFO_DOUT_MUX6_SHIFT                    0  
/************************************************************* 
* apb2_slv0_amba :: FEC_PCS_WRAPPER_IGR0_RCV_PFIFO_DMUX2_REG_IGR0 
*************************************************************/ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX15_MASK                    0x00007000   /*!< RCV_PFIFO_DOUT_MUX15 */ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX15_SHIFT                   12  
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX14_MASK                    0x00000e00   /*!< RCV_PFIFO_DOUT_MUX14 */ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX14_SHIFT                   9  
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX13_MASK                    0x000001c0   /*!< RCV_PFIFO_DOUT_MUX13 */ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX13_SHIFT                   6  
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX12_MASK                    0x00000038   /*!< RCV_PFIFO_DOUT_MUX12 */ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX12_SHIFT                   3  
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX11_MASK                    0x00000007   /*!< RCV_PFIFO_DOUT_MUX11 */ 
#define RCV_PFIFO_DMUX2_REG_RCV_PFIFO_DOUT_MUX11_SHIFT                   0  
/************************************************************* 
* apb2_slv0_amba :: TMT_PAM_NRZ_CTRL_REG
*************************************************************/ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_7_MASK                          0x0000c000   /*!< TMT_NRZ_PAM_7 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_7_SHIFT                         14  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_6_MASK                          0x00003000   /*!< TMT_NRZ_PAM_6 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_6_SHIFT                         12  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_5_MASK                          0x00000c00   /*!< TMT_NRZ_PAM_5 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_5_SHIFT                         10  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_4_MASK                          0x00000300   /*!< TMT_NRZ_PAM_4 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_4_SHIFT                         8  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_3_MASK                          0x000000c0   /*!< TMT_NRZ_PAM_3 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_3_SHIFT                         6  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_2_MASK                          0x00000030   /*!< TMT_NRZ_PAM_2 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_2_SHIFT                         4  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_1_MASK                          0x0000000c   /*!< TMT_NRZ_PAM_1 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_1_SHIFT                         2  
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_0_MASK                          0x00000003   /*!< TMT_NRZ_PAM_0 */ 
#define TMT_PAM_NRZ_CTRL_REG_TMT_NRZ_PAM_0_SHIFT                         0  
/************************************************************* 
* apb2_slv0_amba :: TMT_PFIFO_DMUX0_REG
*************************************************************/ 
#define TMT_PFIFO_DMUX0_REG_RESERVED0_MASK                               0x00008000   /*!< RESERVED0 */ 
#define TMT_PFIFO_DMUX0_REG_RESERVED0_SHIFT                              15  
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX5_MASK                      0x00007000   /*!< TMT_PFIFO_DIN_MUX5 */ 
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX5_SHIFT                     12  
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX4_MASK                      0x00000e00   /*!< TMT_PFIFO_DIN_MUX4 */ 
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX4_SHIFT                     9  
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX3_MASK                      0x000001c0   /*!< TMT_PFIFO_DIN_MUX3 */ 
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX3_SHIFT                     6  
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX2_MASK                      0x00000038   /*!< TMT_PFIFO_DIN_MUX2 */ 
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX2_SHIFT                     3  
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX1_MASK                      0x00000007   /*!< TMT_PFIFO_DIN_MUX1 */ 
#define TMT_PFIFO_DMUX0_REG_TMT_PFIFO_DIN_MUX1_SHIFT                     0  
/************************************************************* 
* apb2_slv0_amba :: TMT_PFIFO_DMUX1_REG
*************************************************************/ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX10_MASK                     0x00007000   /*!< TMT_PFIFO_DIN_MUX10 */ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX10_SHIFT                    12  
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX9_MASK                      0x00000e00   /*!< TMT_PFIFO_DIN_MUX9 */ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX9_SHIFT                     9  
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX8_MASK                      0x000001c0   /*!< TMT_PFIFO_DIN_MUX8 */ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX8_SHIFT                     6  
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX7_MASK                      0x00000038   /*!< TMT_PFIFO_DIN_MUX7 */ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX7_SHIFT                     3  
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX6_MASK                      0x00000007   /*!< TMT_PFIFO_DIN_MUX6 */ 
#define TMT_PFIFO_DMUX1_REG_TMT_PFIFO_DIN_MUX6_SHIFT                     0  
/************************************************************* 
* apb2_slv0_amba :: TMT_PFIFO_DMUX2_REG
*************************************************************/ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX15_MASK                     0x00007000   /*!< TMT_PFIFO_DIN_MUX15 */ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX15_SHIFT                    12  
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX14_MASK                     0x00000e00   /*!< TMT_PFIFO_DIN_MUX14 */ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX14_SHIFT                    9  
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX13_MASK                     0x000001c0   /*!< TMT_PFIFO_DIN_MUX13 */ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX13_SHIFT                    6  
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX12_MASK                     0x00000038   /*!< TMT_PFIFO_DIN_MUX12 */ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX12_SHIFT                    3  
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX11_MASK                     0x00000007   /*!< TMT_PFIFO_DIN_MUX11 */ 
#define TMT_PFIFO_DMUX2_REG_TMT_PFIFO_DIN_MUX11_SHIFT                    0  
/************************************************************* 
* apb2_slv0_amba :: FEC_SYNC_IGR0_AM_LANE_ALIGNMENT_UNLOCK_IRQ_STICKY_REG_IGR0 
*************************************************************/ 
#define AM_LANE_ALIGNMENT_UNLOCK_IRQ_STICKY_REG_ALIGN_FAIL_FAULT_STICKY_MASK    0x0000ff00   /*!< ALIGN_FAIL_FAULT_STICKY */ 
#define AM_LANE_ALIGNMENT_UNLOCK_IRQ_STICKY_REG_ALIGN_FAIL_FAULT_STICKY_SHIFT   8  
/************************************************************* 
* apb2_slv0_amba :: PCS_SYNC_IGR0_PCS_AM_DEBUG0_REG_IGR0 
*************************************************************/ 
#define PCS_AM_DEBUG0_REG_OVR_AM_DIST_EN_MASK                                   0x0000000f   /*!< OVR_AM_DIST_EN */ 
#define PCS_AM_DEBUG0_REG_OVR_AM_DIST_EN_SHIFT                                  0  
/************************************************************* 
* apb2_slv0_amba :: PCS_AM_DEBUG2_REG
*************************************************************/ 
#define PCS_AM_DEBUG2_REG_OVR_AM_DIST_0_MASK                                    0x00007fff   /*!< OVR_AM_DIST_0 */ 
#define PCS_AM_DEBUG2_REG_OVR_AM_DIST_0_SHIFT                                   0  
#define PCS_AM_DEBUG3_REG_OVR_AM_DIST_1_MASK                                    0x00007fff   /*!< OVR_AM_DIST_1 */ 
#define PCS_AM_DEBUG3_REG_OVR_AM_DIST_1_SHIFT                                   0  
/************************************************************* 
* apb2_slv0_amba :: PCS_AM_DEBUG4_REG
*************************************************************/ 
#define PCS_AM_DEBUG4_REG_OVR_AM_DIST_2_MASK                                    0x00007fff   /*!< OVR_AM_DIST_2 */ 
#define PCS_AM_DEBUG4_REG_OVR_AM_DIST_2_SHIFT                                   0  
/************************************************************* 
* apb2_slv0_amba :: PCS_AM_DEBUG5_REG
*************************************************************/ 
#define PCS_AM_DEBUG5_REG_OVR_AM_DIST_3_MASK                                    0x00007fff   /*!< OVR_AM_DIST_3 */ 
#define PCS_AM_DEBUG5_REG_OVR_AM_DIST_3_SHIFT                                   0  
/************************************************************* 
* apb2_slv0_amba :: QC_PGEN_IGR0_0_QC_PGEN_CFG0_REGISTER 
*************************************************************/ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_0LOW_1HIGH_2EVENODD_MASK               0x0000c000   /*!< MD_PGEN_0LOW_1HIGH_2EVENODD */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_0LOW_1HIGH_2EVENODD_SHIFT              14  
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_1PAM4_0NRZ_MASK                        0x00002000   /*!< MD_PGEN_1PAM4_0NRZ */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_1PAM4_0NRZ_SHIFT                       13  
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_FEC_MODE_ENA_MASK                      0x00000800   /*!< MD_PGEN_FEC_MODE_ENA */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_FEC_MODE_ENA_SHIFT                     11  
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_FAULT_MODE_ENA_MASK                    0x00000400   /*!< MD_PGEN_FAULT_MODE_ENA */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_FAULT_MODE_ENA_SHIFT                   10  
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_DOUT_INV_ENA_MASK                      0x00000040   /*!< MD_PGEN_DOUT_INV_ENA */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_DOUT_INV_ENA_SHIFT                     6  
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_SEED_ID_MASK                           0x0000001f   /*!< MD_PGEN_SEED_ID */ 
#define QC_PGEN_CFG0_REGISTER_MD_PGEN_SEED_ID_SHIFT                          0  
/************************************************************* 
* apb2_slv0_amba :: QC_PGEN_IGR0_0_QC_PGEN_CTRL0_REGISTER 
*************************************************************/ 
#define QC_PGEN_CTRL0_REGISTER_PGEN_MD_ACTIVE_FLAG_MASK                      0x00008000   /*!< PGEN_MD_ACTIVE_FLAG */ 
#define QC_PGEN_CTRL0_REGISTER_PGEN_MD_ACTIVE_FLAG_SHIFT                     15  
#define QC_PGEN_CTRL0_REGISTER_MD_PGEN_POLY_SELECT_MASK                      0x000003f0   /*!< MD_PGEN_POLY_SELECT */ 
#define QC_PGEN_CTRL0_REGISTER_MD_PGEN_POLY_SELECT_SHIFT                     4  
#define QC_PGEN_CTRL0_REGISTER_MD_PGEN_ENABLE_MASK                           0x00000001   /*!< MD_PGEN_ENABLE */ 
#define QC_PGEN_CTRL0_REGISTER_MD_PGEN_ENABLE_SHIFT                          0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CFG0_REG 
*************************************************************/ 
#define QC_PMON_CFG0_REG_MD_PMON_0LOW_1HIGH_2EVEN_3ODD_MASK                  0x0000c000   /*!< MD_PMON_0LOW_1HIGH_2EVEN_3ODD */ 
#define QC_PMON_CFG0_REG_MD_PMON_0LOW_1HIGH_2EVEN_3ODD_SHIFT                 14  
#define QC_PMON_CFG0_REG_MD_PMON_1PAM4_0NRZ_MASK                             0x00002000   /*!< MD_PMON_1PAM4_0NRZ */ 
#define QC_PMON_CFG0_REG_MD_PMON_1PAM4_0NRZ_SHIFT                            13  
#define QC_PMON_CFG0_REG_MD_PMON_FEC_MODE_ENA_MASK                           0x00000800   /*!< MD_PMON_FEC_MODE_ENA */ 
#define QC_PMON_CFG0_REG_MD_PMON_FEC_MODE_ENA_SHIFT                          11  
#define QC_PMON_CFG0_REG_MD_PMON_DIN_INV_ENA_MASK                            0x00000002   /*!< MD_PMON_DIN_INV_ENA */ 
#define QC_PMON_CFG0_REG_MD_PMON_DIN_INV_ENA_SHIFT                           1  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CHK_NERE_HI_REG 
*************************************************************/ 
#define QC_PMON_CHK_NERE_HI_REG_PMON_MD_BERR_VALUE_EVEN_HI_MASK              0x0000ffff   /*!< PMON_MD_BERR_VALUE_EVEN_HI */ 
#define QC_PMON_CHK_NERE_HI_REG_PMON_MD_BERR_VALUE_EVEN_HI_SHIFT             0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CHK_BERE_LO_REG 
*************************************************************/ 
#define QC_PMON_CHK_BERE_LO_REG_PMON_MD_BERR_VALUE_EVEN_LO_MASK              0x0000ffff   /*!< PMON_MD_BERR_VALUE_EVEN_LO */ 
#define QC_PMON_CHK_BERE_LO_REG_PMON_MD_BERR_VALUE_EVEN_LO_SHIFT             0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CHK_BERO_HI_REG 
*************************************************************/ 
#define QC_PMON_CHK_BERO_HI_REG_PMON_MD_BERR_VALUE_ODD_HI_MASK               0x0000ffff   /*!< PMON_MD_BERR_VALUE_ODD_HI */ 
#define QC_PMON_CHK_BERO_HI_REG_PMON_MD_BERR_VALUE_ODD_HI_SHIFT              0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CHK_BERO_LO_REG 
*************************************************************/ 
#define QC_PMON_CHK_BERO_LO_REG_PMON_MD_BERR_VALUE_ODD_LO_MASK               0x0000ffff   /*!< PMON_MD_BERR_VALUE_ODD_LO */ 
#define QC_PMON_CHK_BERO_LO_REG_PMON_MD_BERR_VALUE_ODD_LO_SHIFT              0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CHK_CSR0_REG 
*************************************************************/ 
#define QC_PMON_CHK_CSR0_REG_PMON_MD_BERR_OFLOW_ODD_MASK                     0x00000200   /*!< PMON_MD_BERR_OFLOW_ODD */ 
#define QC_PMON_CHK_CSR0_REG_PMON_MD_BERR_OFLOW_ODD_SHIFT                    9  
#define QC_PMON_CHK_CSR0_REG_PMON_MD_BERR_OFLOW_EVEN_MASK                    0x00000100   /*!< PMON_MD_BERR_OFLOW_EVEN */ 
#define QC_PMON_CHK_CSR0_REG_PMON_MD_BERR_OFLOW_EVEN_SHIFT                   8  
#define QC_PMON_CHK_CSR0_REG_MD_PMON_BERR_OFLOW_CLR_MASK                     0x00000008   /*!< MD_PMON_BERR_OFLOW_CLR */ 
#define QC_PMON_CHK_CSR0_REG_MD_PMON_BERR_OFLOW_CLR_SHIFT                    3  
#define QC_PMON_CHK_CSR0_REG_MD_PMON_BERR_CLR_MASK                           0x00000004   /*!< MD_PMON_BERR_CLR */ 
#define QC_PMON_CHK_CSR0_REG_MD_PMON_BERR_CLR_SHIFT                          2  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_CTRL0_REG 
*************************************************************/ 
#define QC_PMON_CTRL0_REG_PMON_MD_ACTIVE_FLAG_MASK                           0x00008000   /*!< PMON_MD_ACTIVE_FLAG */ 
#define QC_PMON_CTRL0_REG_PMON_MD_ACTIVE_FLAG_SHIFT                          15  
#define QC_PMON_CTRL0_REG_PMON_MD_LOCK_FLAG_MASK                             0x00004000   /*!< PMON_MD_LOCK_FLAG */ 
#define QC_PMON_CTRL0_REG_PMON_MD_LOCK_FLAG_SHIFT                            14  
#define QC_PMON_CTRL0_REG_PMON_LOL_IRQ_STAT_MASK                             0x00002000   /*!< PMON_LOL_IRQ_STAT */ 
#define QC_PMON_CTRL0_REG_PMON_LOL_IRQ_STAT_SHIFT                            13  
#define QC_PMON_CTRL0_REG_MD_PMON_POLY_SELECT_MASK                           0x000003f0   /*!< MD_PMON_POLY_SELECT */ 
#define QC_PMON_CTRL0_REG_MD_PMON_POLY_SELECT_SHIFT                          4  
#define QC_PMON_CTRL0_REG_MD_PMON_BERR_STAT_FREEZE_MASK                      0x00000002   /*!< MD_PMON_BERR_STAT_FREEZE */ 
#define QC_PMON_CTRL0_REG_MD_PMON_BERR_STAT_FREEZE_SHIFT                     1  
#define QC_PMON_CTRL0_REG_MD_PMON_ENABLE_MASK                                0x00000001   /*!< MD_PMON_ENABLE */ 
#define QC_PMON_CTRL0_REG_MD_PMON_ENABLE_SHIFT                               0  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_LOCK0_REG 
*************************************************************/ 
#define QC_PMON_LOCK0_REG_MD_PMON_RELOCK_ENA_MASK                            0x00000010   /*!< MD_PMON_RELOCK_ENA */ 
#define QC_PMON_LOCK0_REG_MD_PMON_RELOCK_ENA_SHIFT                           4  
/************************************************************* 
* apb2_slv0_amba :: QC_PMON_IGR0_0_QC_PMON_UNLOCK0_REG 
*************************************************************/ 
#define QC_PMON_UNLOCK0_REG_MD_PMON_UNLOCK_THR_MASK                          0x00000f00   /*!< MD_PMON_UNLOCK_THR */ 
#define QC_PMON_UNLOCK0_REG_MD_PMON_UNLOCK_THR_SHIFT                         8  
/************************************************************* 
* apb2_slv0_amba :: FEC_FIFO_BUNDLE_DIS_REG
*************************************************************/ 
#define FEC_FIFO_BUNDLE_DIS_REG_FEC_FIFO_BUNDLE_DIS_MASK                  0x0000000f   /*!< FEC_FIFO_BUNDLE_DIS */ 
#define FEC_FIFO_BUNDLE_DIS_REG_FEC_FIFO_BUNDLE_DIS_SHIFT                 0  
/************************************************************* 
* apb2_slv0_amba :: FEC_FIFO_CLK_RST_SEL_REG
*************************************************************/ 
#define FEC_FIFO_CLK_RST_SEL_REG_WR_6_5_MUX_MASK                          0x00000200   /*!< WR_6_5_MUX */ 
#define FEC_FIFO_CLK_RST_SEL_REG_WR_6_5_MUX_SHIFT                         9  
#define FEC_FIFO_CLK_RST_SEL_REG_WR_2_1_MUX_MASK                          0x00000100   /*!< WR_2_1_MUX */ 
#define FEC_FIFO_CLK_RST_SEL_REG_WR_2_1_MUX_SHIFT                         8  
#define FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_OB_SELECT_MASK                  0x000000f0   /*!< FEC_FIFO_OB_SELECT */ 
#define FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_OB_SELECT_SHIFT                 4  
#define FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_IB_SELECT_MASK                  0x0000000f   /*!< FEC_FIFO_IB_SELECT */ 
#define FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_IB_SELECT_SHIFT                 0  
/************************************************************* 
* apb2_slv0_amba :: RCV_PFIFO_GBOX_IGR0_FIFO_COLLISION_STICKY_STATUS_REG_IGR0 
*************************************************************/ 
#define FIFO_COLLISION_STICKY_STATUS_REG_FIFO_COLLISION_FAULT_STICKY_MASK 0x0000ff00   /*!< FIFO_COLLISION_FAULT_STICKY */ 
#define FIFO_COLLISION_STICKY_STATUS_REG_FIFO_COLLISION_FAULT_STICKY_SHIFT 8  
/************************************************************* 
* apb2_slv0_amba :: RSFEC_DEC_TOP_IGR0_0_FEC_COUNTER_CONTROL_REGISTER 
*************************************************************/ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_ERR_COUNT_SAT_EN_MASK          0x00000010   /*!< MD_ERR_COUNT_SAT_EN */ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_ERR_COUNT_SAT_EN_SHIFT         4  
#define FEC_COUNTER_CONTROL_REGISTER_MD_DEC_CLR_ALL_MASK               0x00000008   /*!< MD_DEC_CLR_ALL */ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_DEC_CLR_ALL_SHIFT              3  
#define FEC_COUNTER_CONTROL_REGISTER_MD_STAT_CLR_ALL_MASK              0x00000004   /*!< MD_STAT_CLR_ALL */ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_STAT_CLR_ALL_SHIFT             2  
#define FEC_COUNTER_CONTROL_REGISTER_MD_STAT_LATCH_ALL_MASK            0x00000002   /*!< MD_STAT_LATCH_ALL */ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_STAT_LATCH_ALL_SHIFT           1  
#define FEC_COUNTER_CONTROL_REGISTER_MD_ERR_STAT_EN_MASK               0x00000001   /*!< MD_ERR_STAT_EN */ 
#define FEC_COUNTER_CONTROL_REGISTER_MD_ERR_STAT_EN_SHIFT              0  
/************************************************************* 
* apb2_slv0_amba :: MODE_CONTROL_REGISTER 
*************************************************************/ 
#define MODE_CONTROL_REGISTER_MD_DEC_DIN_OFF_MASK                      0x00000020   /*!< MD_DEC_DIN_OFF */ 
#define MODE_CONTROL_REGISTER_MD_DEC_DIN_OFF_SHIFT                     5  
/************************************************************* 
* apb2_slv0_amba :: MODE_CONTROL_REGISTER_ENCODER 
*************************************************************/ 
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_DIN_OFF_MASK              0x00000080   /*!< MD_ENC_DIN_OFF */ 
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_DIN_OFF_SHIFT             7  
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_PASS_THRU_UNCORR_MASK     0x00000040   /*!< MD_ENC_PASS_THRU_UNCORR */ 
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_PASS_THRU_UNCORR_SHIFT    6  
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_BYPASS_MASK               0x00000020   /*!< MD_ENC_BYPASS */ 
#define MODE_CONTROL_REGISTER_ENCODER_MD_ENC_BYPASS_SHIFT              5  
/************************************************************* 
* apb2_slv0_amba :: RSFEC_IEEE_50G_100G_IGR0_0_PCS_STATUS1_REGISTER_50G_100G_IGR0_0 
*************************************************************/ 
#define PCS_STATUS1_REGISTER_50G_100G_0_PCS_BLOCK_LOCK_MASK        0x00000001   /*!< PCS_BLOCK_LOCK */ 
#define PCS_STATUS1_REGISTER_50G_100G_0_PCS_BLOCK_LOCK_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: RSFEC_IEEE_50G_100G_IGR0_2_PCS_STATUS1_REGISTER_50G_100G_IGR0_2 
*************************************************************/ 
#define PCS_STATUS1_REGISTER_50G_100G_2_PCS_BLOCK_LOCK_MASK        0x00000001   /*!< PCS_BLOCK_LOCK */ 
#define PCS_STATUS1_REGISTER_50G_100G_2_PCS_BLOCK_LOCK_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: RSFEC_IEEE_50G_100G_IGR0_4_PCS_STATUS1_REGISTER_50G_100G_IGR0_4 
*************************************************************/ 
#define PCS_STATUS1_REGISTER_50G_100G_4_PCS_BLOCK_LOCK_MASK        0x00000001   /*!< PCS_BLOCK_LOCK */ 
#define PCS_STATUS1_REGISTER_50G_100G_4_PCS_BLOCK_LOCK_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: RSFEC_IEEE_50G_100G_IGR0_6_PCS_STATUS1_REGISTER_50G_100G_IGR0_6 
*************************************************************/ 
#define PCS_STATUS1_REGISTER_50G_100G_6_PCS_BLOCK_LOCK_MASK        0x00000001   /*!< PCS_BLOCK_LOCK */ 
#define PCS_STATUS1_REGISTER_50G_100G_6_PCS_BLOCK_LOCK_SHIFT       0  
/************************************************************* 
* apb2_slv0_amba :: TMT_FEC_FIFO_BUNDLE_DIS_REG
*************************************************************/ 
#define TMT_FEC_FIFO_BUNDLE_DIS_REG_FEC_FIFO_BUNDLE_DIS_MASK              0x0000000f   /*!< FEC_FIFO_BUNDLE_DIS */ 
#define TMT_FEC_FIFO_BUNDLE_DIS_REG_FEC_FIFO_BUNDLE_DIS_SHIFT             0  
/************************************************************* 
* apb2_slv0_amba :: TMT_FEC_FIFO_CLK_RST_SEL_REG
*************************************************************/ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RESERVED0_MASK                       0x0000fc00   /*!< RESERVED0 */ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RESERVED0_SHIFT                      10  
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RD_6_5_MUX_MASK                      0x00000200   /*!< RD_6_5_MUX */ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RD_6_5_MUX_SHIFT                     9  
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RD_2_1_MUX_MASK                      0x00000100   /*!< RD_2_1_MUX */ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_RD_2_1_MUX_SHIFT                     8  
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_OB_SELECT_MASK              0x000000f0   /*!< FEC_FIFO_OB_SELECT */ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_OB_SELECT_SHIFT             4  
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_IB_SELECT_MASK              0x0000000f   /*!< FEC_FIFO_IB_SELECT */ 
#define TMT_FEC_FIFO_CLK_RST_SEL_REG_FEC_FIFO_IB_SELECT_SHIFT             0  
/************************************************************* 
* apb2_slv0_amba :: TMT_PFIFO_GBOX_IGR0_TMT_FIFO_COLLISION_STICKY_STATUS_REG_IGR0 
*************************************************************/ 
#define TMT_FIFO_COLLISION_STICKY_STATUS_REG_FIFO_COLLISION_FAULT_STICKY_MASK 0x0000ff00   /*!< FIFO_COLLISION_FAULT_STICKY */ 
#define TMT_FIFO_COLLISION_STICKY_STATUS_REG_FIFO_COLLISION_FAULT_STICKY_SHIFT 8  
/************************************************************* 
* apb2_slv0_amba :: XDEC_FEC_TYPE_CTRL_REG
*************************************************************/ 
#define XDEC_FEC_TYPE_CTRL_REG_DESCRAMBLER_CKEN_1_MASK                          0x00004000   /*!< DESCRAMBLER_CKEN_1 */ 
#define XDEC_FEC_TYPE_CTRL_REG_DESCRAMBLER_CKEN_1_SHIFT                         14  
#define XDEC_FEC_TYPE_CTRL_REG_BYPASS_DESCRAMBLER_1_MASK                        0x00001000   /*!< BYPASS_DESCRAMBLER_1 */ 
#define XDEC_FEC_TYPE_CTRL_REG_BYPASS_DESCRAMBLER_1_SHIFT                       12  
#define XDEC_FEC_TYPE_CTRL_REG_DESCRAMBLER_CKEN_0_MASK                          0x00000400   /*!< DESCRAMBLER_CKEN_0 */ 
#define XDEC_FEC_TYPE_CTRL_REG_DESCRAMBLER_CKEN_0_SHIFT                         10  
#define XDEC_FEC_TYPE_CTRL_REG_BYPASS_DESCRAMBLER_0_MASK                        0x00000100   /*!< BYPASS_DESCRAMBLER_0 */ 
#define XDEC_FEC_TYPE_CTRL_REG_BYPASS_DESCRAMBLER_0_SHIFT                       8  
/************************************************************* 
* apb2_slv0_amba :: XDECODER_IGR0_XDEC_GBOX_160_257_IRQ_STICKY_REG_IGR0 
*************************************************************/ 
#define XDEC_GBOX_160_257_IRQ_STICKY_REG_XDECODER_GBOX_160_257_COLLISIION_FAULT_STICKY_MASK 0x0000ff00   /*!< XDECODER_GBOX_160_257_COLLISIION_FAULT_STICKY */ 
#define XDEC_GBOX_160_257_IRQ_STICKY_REG_XDECODER_GBOX_160_257_COLLISIION_FAULT_STICKY_SHIFT 8  
/************************************************************* 
* apb2_slv0_amba :: XDEC_GBOX_GAP_1_REG
*************************************************************/ 
#define XDEC_GBOX_GAP_1_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_3_MASK                    0x00003f80   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_3 */ 
#define XDEC_GBOX_GAP_1_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_3_SHIFT                   7  
#define XDEC_GBOX_GAP_1_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_2_MASK                    0x0000007f   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_2 */ 
#define XDEC_GBOX_GAP_1_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_2_SHIFT                   0  
/************************************************************* 
* apb2_slv0_amba :: XDEC_GBOX_GAP_2_REG 
*************************************************************/ 
#define XDEC_GBOX_GAP_2_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_5_MASK                    0x00003f80   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_5 */ 
#define XDEC_GBOX_GAP_2_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_5_SHIFT                   7  
#define XDEC_GBOX_GAP_2_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_4_MASK                    0x0000007f   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_4 */ 
#define XDEC_GBOX_GAP_2_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_4_SHIFT                   0  
/************************************************************* 
* apb2_slv0_amba :: XDEC_GBOX_GAP_3_REG
*************************************************************/ 
#define XDEC_GBOX_GAP_3_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_7_MASK                    0x00003f80   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_7 */ 
#define XDEC_GBOX_GAP_3_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_7_SHIFT                   7  
#define XDEC_GBOX_GAP_3_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_6_MASK                    0x0000007f   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_6 */ 
#define XDEC_GBOX_GAP_3_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_6_SHIFT                   0  
/************************************************************* 
* apb2_slv0_amba :: XDEC_GBOX_GAP_REG
*************************************************************/ 
#define XDEC_GBOX_GAP_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_1_MASK                      0x00003f80   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_1 */ 
#define XDEC_GBOX_GAP_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_1_SHIFT                     7  
#define XDEC_GBOX_GAP_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_0_MASK                      0x0000007f   /*!< MD_XDEC_GBOX_EXTRA_GAP_M1_0 */ 
#define XDEC_GBOX_GAP_REG_MD_XDEC_GBOX_EXTRA_GAP_M1_0_SHIFT                     0  
/************************************************************* 
* apb2_slv0_amba :: XDEC_GBOX_WIDTH_CTRL_REG 
*************************************************************/ 
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_6_MASK                              0x00000008   /*!< MD_160_80B_6 */ 
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_6_SHIFT                             3  
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_4_MASK                              0x00000004   /*!< MD_160_80B_4 */ 
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_4_SHIFT                             2  
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_2_MASK                              0x00000002   /*!< MD_160_80B_2 */ 
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_2_SHIFT                             1  
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_0_MASK                              0x00000001   /*!< MD_160_80B_0 */ 
#define XDEC_GBOX_WIDTH_CTRL_REG_MD_160_80B_0_SHIFT                             0  
/************************************************************* 
* apb2_slv0_amba :: XENCODER_IGR0_SCRAMBLER_CTRL_REG
*************************************************************/ 
#define SCRAMBLER_CTRL_REG_SCRAMBLER_CKEN_1_MASK                                0x00000400   /*!< SCRAMBLER_CKEN_1 */ 
#define SCRAMBLER_CTRL_REG_SCRAMBLER_CKEN_1_SHIFT                               10  
#define SCRAMBLER_CTRL_REG_BYPASS_SCRAMBLER_1_MASK                              0x00000100   /*!< BYPASS_SCRAMBLER_1 */ 
#define SCRAMBLER_CTRL_REG_BYPASS_SCRAMBLER_1_SHIFT                             8  
#define SCRAMBLER_CTRL_REG_SCRAMBLER_CKEN_0_MASK                                0x00000004   /*!< SCRAMBLER_CKEN_0 */ 
#define SCRAMBLER_CTRL_REG_SCRAMBLER_CKEN_0_SHIFT                               2  
#define SCRAMBLER_CTRL_REG_BYPASS_SCRAMBLER_0_MASK                              0x00000001   /*!< BYPASS_SCRAMBLER_0 */ 
#define SCRAMBLER_CTRL_REG_BYPASS_SCRAMBLER_0_SHIFT                             0  
/************************************************************* 
* apb2_slv0_amba :: XENCODER_IGR0_XENC_GBOX_257_160_IRQ_STICKY_REG_IGR0 
*************************************************************/ 
#define XENC_GBOX_257_160_IRQ_STICKY_REG_XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY_EXTRA_MASK 0x0000f000   /*!< XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY_EXTRA */ 
#define XENC_GBOX_257_160_IRQ_STICKY_REG_XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY_EXTRA_SHIFT 12  
#define XENC_GBOX_257_160_IRQ_STICKY_REG_XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY_MASK 0x000000f0   /*!< XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY */ 
#define XENC_GBOX_257_160_IRQ_STICKY_REG_XENCODER_GBOX_257_160_COLLISIION_FAULT_STICKY_SHIFT 4  
/************************************************************* 
* apb2_slv0_amba :: XENC_GBOX_GAP_CTRL_REG_IGR0 
*************************************************************/ 
#define XENC_GBOX_GAP_CTRL_REG_MD_XENC_GBOX_EXTRA_GAP_M1_1_MASK                 0x00003f80   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_1 */ 
#define XENC_GBOX_GAP_CTRL_REG_MD_XENC_GBOX_EXTRA_GAP_M1_1_SHIFT                7  
#define XENC_GBOX_GAP_CTRL_REG_MD_XENC_GBOX_EXTRA_GAP_M1_0_MASK                 0x0000007f   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_0 */ 
#define XENC_GBOX_GAP_CTRL_REG_MD_XENC_GBOX_EXTRA_GAP_M1_0_SHIFT                0  
/************************************************************* 
* apb2_slv0_amba :: XENC_GBOX_GAP_CTRL1_REG
*************************************************************/ 
#define XENC_GBOX_GAP_CTRL1_REG_MD_XENC_GBOX_EXTRA_GAP_M1_3_MASK                0x00003f80   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_3 */ 
#define XENC_GBOX_GAP_CTRL1_REG_MD_XENC_GBOX_EXTRA_GAP_M1_3_SHIFT               7  
#define XENC_GBOX_GAP_CTRL1_REG_MD_XENC_GBOX_EXTRA_GAP_M1_2_MASK                0x0000007f   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_2 */ 
#define XENC_GBOX_GAP_CTRL1_REG_MD_XENC_GBOX_EXTRA_GAP_M1_2_SHIFT               0  
/************************************************************* 
* apb2_slv0_amba :: XENC_GBOX_GAP_CTRL2_REG
*************************************************************/ 
#define XENC_GBOX_GAP_CTRL2_REG_MD_XENC_GBOX_EXTRA_GAP_M1_5_MASK                0x00003f80   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_5 */ 
#define XENC_GBOX_GAP_CTRL2_REG_MD_XENC_GBOX_EXTRA_GAP_M1_5_SHIFT               7  
#define XENC_GBOX_GAP_CTRL2_REG_MD_XENC_GBOX_EXTRA_GAP_M1_4_MASK                0x0000007f   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_4 */ 
#define XENC_GBOX_GAP_CTRL2_REG_MD_XENC_GBOX_EXTRA_GAP_M1_4_SHIFT               0  
/************************************************************* 
* apb2_slv0_amba :: XENC_GBOX_GAP_CTRL3_REG
*************************************************************/ 
#define XENC_GBOX_GAP_CTRL3_REG_MD_XENC_GBOX_EXTRA_GAP_M1_7_MASK                0x00003f80   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_7 */ 
#define XENC_GBOX_GAP_CTRL3_REG_MD_XENC_GBOX_EXTRA_GAP_M1_7_SHIFT               7  
#define XENC_GBOX_GAP_CTRL3_REG_MD_XENC_GBOX_EXTRA_GAP_M1_6_MASK                0x0000007f   /*!< MD_XENC_GBOX_EXTRA_GAP_M1_6 */ 
#define XENC_GBOX_GAP_CTRL3_REG_MD_XENC_GBOX_EXTRA_GAP_M1_6_SHIFT               0  
/************************************************************* 
* apb2_slv0_amba :: XENC_GBOX_WIDTH_CTRL_REG 
*************************************************************/ 
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_6_MASK                              0x00000008   /*!< MD_160_80B_6 */ 
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_6_SHIFT                             3  
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_4_MASK                              0x00000004   /*!< MD_160_80B_4 */ 
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_4_SHIFT                             2  
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_2_MASK                              0x00000002   /*!< MD_160_80B_2 */ 
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_2_SHIFT                             1  
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_0_MASK                              0x00000001   /*!< MD_160_80B_0 */ 
#define XENC_GBOX_WIDTH_CTRL_REG_MD_160_80B_0_SHIFT                             0  
/************************************************************* 
* apb2_slv0_amba :: XENC_SLICE_CTRL_REG
*************************************************************/ 
#define XENC_SLICE_CTRL_REG_XENC_AM_INS_SLICE_CKEN_MASK                         0x000000ff   /*!< XENC_AM_INS_SLICE_CKEN */ 
#define XENC_SLICE_CTRL_REG_XENC_AM_INS_SLICE_CKEN_SHIFT                        0  

#endif /* PORTOFINO_RTMR_PP_REGS_H */
