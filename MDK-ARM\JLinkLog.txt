T36D8 000:014.147   SEGGER J-Link V7.82 Log File
T36D8 000:014.912   DLL Compiled: Oct 13 2022 13:32:51
T36D8 000:014.935   Logging started @ 2025-08-18 02:18
T36D8 000:015.074 - 15.099ms
T36D8 000:015.565 JLINK_SetWarnOutHandler(...)
T36D8 000:016.063 - 0.523ms
T36D8 000:016.127 JLINK_OpenEx(...)
T36D8 000:022.076   Firmware: J-Link ARM V8 compiled Nov 28 2014 13:44:46
T36D8 000:022.539   Firmware: J-Link ARM V8 compiled Nov 28 2014 13:44:46
T36D8 000:027.066   Hardware: V8.00
T36D8 000:027.096   S/N: 805251123
T36D8 000:027.116   OEM: SEGGER
T36D8 000:027.136   Feature(s): RDI,FlashDL,FlashBP,JFlash,GDB
T36D8 000:028.906   TELNET listener socket opened on port 19021
T36D8 000:029.241   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T36D8 000:029.366   WEBSRV Webserver running on local port 19080
T36D8 000:046.469 - 30.402ms returns "O.K."
T36D8 000:047.031 JLINK_GetEmuCaps()
T36D8 000:047.053 - 0.031ms returns 0xB9FF7BBF
T36D8 000:047.077 JLINK_TIF_GetAvailable(...)
T36D8 000:047.453 - 0.408ms
T36D8 000:047.512 JLINK_SetErrorOutHandler(...)
T36D8 000:047.548 - 0.053ms
T36D8 000:048.025 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\JLinkSettings.ini"", ...). 
T36D8 000:094.678   Device "STM32F205RG" selected.
T36D8 000:095.031 - 47.017ms returns 0x00
T36D8 000:097.806 JLINK_ExecCommand("Device = STM32F205RGTx", ...). 
T36D8 000:101.256   Device "STM32F205RG" selected.
T36D8 000:101.640 - 3.803ms returns 0x00
T36D8 000:101.671 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T36D8 000:101.693 - 0.010ms returns 0x01
T36D8 000:101.712 JLINK_GetHardwareVersion()
T36D8 000:101.732 - 0.044ms returns 80000
T36D8 000:101.776 JLINK_GetDLLVersion()
T36D8 000:101.794 - 0.038ms returns 78200
T36D8 000:101.825 JLINK_GetOEMString(...)
T36D8 000:101.846 JLINK_GetFirmwareString(...)
T36D8 000:101.864 - 0.027ms
T36D8 000:107.487 JLINK_GetDLLVersion()
T36D8 000:107.526 - 0.047ms returns 78200
T36D8 000:107.544 JLINK_GetCompileDateTime()
T36D8 000:107.559 - 0.023ms
T36D8 000:108.934 JLINK_GetFirmwareString(...)
T36D8 000:108.960 - 0.032ms
T36D8 000:110.005 JLINK_GetHardwareVersion()
T36D8 000:110.029 - 0.030ms returns 80000
T36D8 000:111.184 JLINK_GetSN()
T36D8 000:111.206 - 0.029ms returns 805251123
T36D8 000:114.187 JLINK_GetOEMString(...)
T36D8 000:116.346 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T36D8 000:118.080 - 1.751ms returns 0x00
T36D8 000:118.108 JLINK_HasError()
T36D8 000:118.606 JLINK_SetSpeed(5000)
T36D8 000:118.759 - 0.189ms
T36D8 000:118.810 JLINK_GetId()
T36D8 000:122.722   InitTarget() start
T36D8 000:122.771    J-Link Script File: Executing InitTarget()
T36D8 000:145.792   InitTarget() end
T36D8 000:147.470   Found SW-DP with ID 0x2BA01477
T36D8 000:152.404   Old FW that does not support reading DPIDR via DAP jobs
T36D8 000:159.118   DPv0 detected
T36D8 000:160.096   CoreSight SoC-400 or earlier
T36D8 000:161.084   Scanning AP map to find all available APs
T36D8 000:165.048   AP[1]: Stopped AP scan as end of AP map has been reached
T36D8 000:165.966   AP[0]: AHB-AP (IDR: 0x24770011)
T36D8 000:166.975   Iterating through AP map to find AHB-AP to use
T36D8 000:173.828   AP[0]: Core found
T36D8 000:175.492   AP[0]: AHB-AP ROM base: 0xE00FF000
T36D8 000:178.152   CPUID register: 0x412FC230. Implementer code: 0x41 (ARM)
T36D8 000:179.024   Found Cortex-M3 r2p0, Little endian.
T36D8 000:179.348   -- Max. mem block: 0x00002408
T36D8 000:179.468   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T36D8 000:180.039   CPU_ReadMem(4 bytes @ 0x********)
T36D8 000:181.395   FPUnit: 6 code (BP) slots and 2 literal slots
T36D8 000:181.424   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T36D8 000:181.850   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T36D8 000:182.345   CPU_ReadMem(4 bytes @ 0xE0001000)
T36D8 000:182.840   CPU_WriteMem(4 bytes @ 0xE0001000)
T36D8 000:183.313   CPU_ReadMem(4 bytes @ 0xE000ED88)
T36D8 000:183.757   CPU_WriteMem(4 bytes @ 0xE000ED88)
T36D8 000:184.168   CPU_ReadMem(4 bytes @ 0xE000ED88)
T36D8 000:184.647   CPU_WriteMem(4 bytes @ 0xE000ED88)
T36D8 000:188.262   CoreSight components:
T36D8 000:189.605   ROMTbl[0] @ E00FF000
T36D8 000:189.670   CPU_ReadMem(64 bytes @ 0xE00FF000)
T36D8 000:190.629   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T36D8 000:192.630   [0][0]: E000E000 CID B105E00D PID 002BB000 SCS
T36D8 000:192.671   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T36D8 000:195.114   [0][1]: E0001000 CID B105E00D PID 002BB002 DWT
T36D8 000:195.194   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T36D8 000:199.292   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T36D8 000:199.391   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T36D8 000:204.533   [0][3]: ******** CID B105E00D PID 002BB001 ITM
T36D8 000:204.663   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T36D8 000:207.738   [0][4]: ******** CID B105900D PID 002BB923 TPIU-Lite
T36D8 000:207.820   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T36D8 000:210.505   [0][5]: ******** CID B105900D PID 002BB924 ETM-M3
T36D8 000:211.712 - 92.931ms returns 0x2BA01477
T36D8 000:211.767 JLINK_GetDLLVersion()
T36D8 000:211.790 - 0.035ms returns 78200
T36D8 000:211.816 JLINK_CORE_GetFound()
T36D8 000:211.839 - 0.034ms returns 0x30000FF
T36D8 000:211.864 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T36D8 000:211.895   Value=0xE00FF000
T36D8 000:211.929 - 0.076ms returns 0
T36D8 000:213.649 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T36D8 000:213.678   Value=0xE00FF000
T36D8 000:213.705 - 0.066ms returns 0
T36D8 000:213.725 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T36D8 000:213.744   Value=0x********
T36D8 000:213.771 - 0.055ms returns 0
T36D8 000:213.793 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T36D8 000:213.831   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T36D8 000:214.714   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T36D8 000:214.777 - 0.994ms returns 32 (0x20)
T36D8 000:214.803 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T36D8 000:214.823   Value=0x00000000
T36D8 000:214.850 - 0.056ms returns 0
T36D8 000:214.870 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T36D8 000:214.889   Value=0x********
T36D8 000:214.916 - 0.054ms returns 0
T36D8 000:214.935 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T36D8 000:214.953   Value=0x********
T36D8 000:214.980 - 0.054ms returns 0
T36D8 000:214.999 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T36D8 000:215.017   Value=0xE0001000
T36D8 000:215.044 - 0.054ms returns 0
T36D8 000:215.063 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T36D8 000:215.082   Value=0x********
T36D8 000:215.109 - 0.054ms returns 0
T36D8 000:215.128 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T36D8 000:215.146   Value=0xE000E000
T36D8 000:215.173 - 0.054ms returns 0
T36D8 000:215.192 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T36D8 000:215.211   Value=0xE000EDF0
T36D8 000:215.238 - 0.054ms returns 0
T36D8 000:215.257 JLINK_GetDebugInfo(0x01 = Unknown)
T36D8 000:215.281   Value=0x00000000
T36D8 000:215.308 - 0.060ms returns 0
T36D8 000:215.327 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T36D8 000:215.357   CPU_ReadMem(4 bytes @ 0xE000ED00)
T36D8 000:215.935   Data:  30 C2 2F 41
T36D8 000:215.973   Debug reg: CPUID
T36D8 000:216.001 - 0.682ms returns 1 (0x1)
T36D8 000:216.022 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T36D8 000:216.042   Value=0x00000000
T36D8 000:216.069 - 0.055ms returns 0
T36D8 000:216.089 JLINK_HasError()
T36D8 000:216.110 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T36D8 000:216.128 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T36D8 000:216.148 JLINK_Reset()
T36D8 000:216.178   CPU is running
T36D8 000:216.207   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T36D8 000:216.869   CPU is running
T36D8 000:216.929   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T36D8 000:219.151   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T36D8 000:222.784   Reset: Reset device via AIRCR.SYSRESETREQ.
T36D8 000:222.835   CPU is running
T36D8 000:222.860   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T36D8 000:276.147   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T36D8 000:276.853   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T36D8 000:277.450   CPU is running
T36D8 000:277.508   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T36D8 000:278.100   CPU is running
T36D8 000:278.181   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T36D8 000:284.422   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T36D8 000:289.183   CPU_WriteMem(4 bytes @ 0x********)
T36D8 000:289.862   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T36D8 000:290.479   CPU_ReadMem(4 bytes @ 0xE0001000)
T36D8 000:291.093 - 74.981ms
T36D8 000:291.165 JLINK_Halt()
T36D8 000:291.201 - 0.053ms returns 0x00
T36D8 000:291.363 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T36D8 000:291.435   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T36D8 000:292.059   Data:  03 00 03 00
T36D8 000:292.162   Debug reg: DHCSR
T36D8 000:292.214 - 0.869ms returns 1 (0x1)
T36D8 000:292.266 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T36D8 000:292.741   Debug reg: DHCSR
T36D8 000:293.262   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T36D8 000:293.959 - 1.733ms returns 0 (0x00000000)
T36D8 000:294.027 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T36D8 000:294.066   Debug reg: DEMCR
T36D8 000:294.134   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T36D8 000:294.755 - 0.767ms returns 0 (0x00000000)
T36D8 000:310.728 JLINK_GetHWStatus(...)
T36D8 000:311.064 - 0.385ms returns 0
T36D8 000:320.908 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T36D8 000:320.965 - 0.067ms returns 0x06
T36D8 000:320.987 JLINK_GetNumBPUnits(Type = 0xF0)
T36D8 000:321.007 - 0.028ms returns 0x2000
T36D8 000:321.026 JLINK_GetNumWPUnits()
T36D8 000:321.044 - 0.027ms returns 4
T36D8 000:326.930 JLINK_GetSpeed()
T36D8 000:326.965 - 0.042ms returns 4000
T36D8 000:328.782 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T36D8 000:328.824   CPU_ReadMem(4 bytes @ 0xE000E004)
T36D8 000:329.319   Data:  02 00 00 00
T36D8 000:329.342 - 0.568ms returns 1 (0x1)
T36D8 000:329.360 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T36D8 000:329.377   CPU_ReadMem(4 bytes @ 0xE000E004)
T36D8 000:329.939   Data:  02 00 00 00
T36D8 000:329.994 - 0.646ms returns 1 (0x1)
T36D8 000:330.025 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T36D8 000:330.052   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T36D8 000:330.095   CPU_WriteMem(28 bytes @ 0xE0001000)
T36D8 000:330.757 - 0.766ms returns 0x1C
T36D8 000:330.811 JLINK_Halt()
T36D8 000:330.834 - 0.035ms returns 0x00
T36D8 000:330.859 JLINK_IsHalted()
T36D8 000:330.884 - 0.037ms returns TRUE
T36D8 005:836.876 JLINK_Close()
T36D8 005:838.302   OnDisconnectTarget() start
T36D8 005:838.365    J-Link Script File: Executing OnDisconnectTarget()
T36D8 005:838.389   CPU_WriteMem(4 bytes @ 0xE0042004)
T36D8 005:838.893   CPU_WriteMem(4 bytes @ 0xE0042008)
T36D8 005:840.295   OnDisconnectTarget() end
T36D8 005:840.350   CPU_ReadMem(4 bytes @ 0xE0001000)
T36D8 005:840.753   CPU_WriteMem(4 bytes @ 0xE0001004)
T36D8 005:866.007 - 29.212ms
T36D8 005:866.111   
T36D8 005:866.150   Closed
